# Simple CMS Starter Templates

Welcome to the **Simple CMS Starter Templates**! This repository contains front-end templates for building a simple CMS
in different frameworks and libraries. Each subfolder represents a specific framework, offering reusable, scalable, and
easy-to-implement CMS solutions.

## **Templates**

| Framework/Library | Status         | Description                                   |
| ----------------- | -------------- | --------------------------------------------- |
| **Next.js**       | ✅ Released    | A CMS built using Next.js and its App Router. |
| **Nuxt.js**       | ✅ Released    | A CMS template leveraging Nuxt.js features.   |
| **Svelte**        | ✅ Released  	| A CMS template using the Svelte framework.    |
| **Astro**         | ✅ Released    | A CMS optimized for performance with Astro.   |

## **Folder Structure**

Each subfolder contains:

- **Source Code**: Framework-specific implementation of the CMS.
- **Documentation**: Instructions on how to set up, customize, and use the template.
