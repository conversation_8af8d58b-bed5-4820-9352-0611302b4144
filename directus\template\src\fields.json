[{"collection": "ai_prompts", "field": "id", "type": "uuid", "schema": {"name": "id", "table": "ai_prompts", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "ai_prompts", "field": "id", "special": ["uuid"], "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "ai_prompts", "field": "sort", "type": "integer", "schema": {"name": "sort", "table": "ai_prompts", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "ai_prompts", "field": "sort", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 2, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "ai_prompts", "field": "name", "type": "string", "schema": {"name": "name", "table": "ai_prompts", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": true, "is_indexed": true, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "ai_prompts", "field": "name", "special": null, "interface": "input", "options": {"slug": true, "trim": true}, "display": "formatted-value", "display_options": {"font": "monospace"}, "readonly": false, "hidden": false, "sort": 8, "width": "half", "translations": null, "note": "Unique name for the prompt. Use names like \"create-article\" or \"generate-product-description\".", "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "ai_prompts", "field": "status", "type": "string", "schema": {"name": "status", "table": "ai_prompts", "data_type": "character varying", "default_value": "draft", "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "ai_prompts", "field": "status", "special": null, "interface": "select-dropdown", "options": {"choices": [{"text": "$t:draft", "value": "draft", "icon": "draft_orders", "color": "#A2B5CD"}, {"text": "In Review", "value": "in_review", "icon": "rate_review", "color": "#FFA439"}, {"text": "$t:published", "value": "published", "icon": "check", "color": "#2ECDA7"}]}, "display": "labels", "display_options": {"choices": [{"text": "$t:draft", "value": "draft", "icon": "draft_orders", "color": "#A2B5CD"}, {"text": "In Review", "value": "in_review", "icon": "rate_review", "color": "#FFA439"}, {"text": "$t:published", "value": "published", "icon": "check", "color": "#2ECDA7"}]}, "readonly": false, "hidden": false, "sort": 9, "width": "half", "translations": null, "note": "Is this prompt published and available to use?", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "ai_prompts", "field": "description", "type": "text", "schema": {"name": "description", "table": "ai_prompts", "data_type": "text", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "ai_prompts", "field": "description", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 10, "width": "full", "translations": null, "note": "Briefly explain what this prompt does in 1-2 sentences.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "ai_prompts", "field": "messages", "type": "json", "schema": {"name": "messages", "table": "ai_prompts", "data_type": "json", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "ai_prompts", "field": "messages", "special": ["cast-json"], "interface": "inline-repeater-interface", "options": {"fields": [{"field": "role", "name": "role", "type": "string", "meta": {"field": "role", "width": "full", "type": "string", "required": true, "note": "Who is speaking in this message.", "interface": "select-dropdown", "options": {"choices": [{"text": "User", "value": "user", "icon": "person"}, {"text": "Assistant", "value": "assistant", "icon": "robot"}]}, "display": "labels", "display_options": {"choices": [{"text": "User", "value": "user", "icon": "person"}, {"text": "Assistant", "value": "assistant", "icon": "robot"}]}}}, {"field": "text", "name": "text", "type": "text", "meta": {"field": "text", "width": "full", "type": "text", "required": true, "interface": "input-rich-text-md", "display": "formatted-value", "display_options": {"format": true}, "note": "The actual content of the message. You can use {{ curly_braces }} for placeholders that will be replaced with real data."}}], "showConfirmDiscard": true, "template": "{{ role }} • {{ text }}", "addLabel": "New Message"}, "display": "formatted-json-value", "display_options": {"format": "{{ role }} • {{ text }}"}, "readonly": false, "hidden": false, "sort": 12, "width": "full", "translations": null, "note": "Optional: Define the conversation structure between users and AI. Used to add context and improve outputs.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "ai_prompts", "field": "system_prompt", "type": "text", "schema": {"name": "system_prompt", "table": "ai_prompts", "data_type": "text", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "ai_prompts", "field": "system_prompt", "special": null, "interface": "input-multiline", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 11, "width": "full", "translations": null, "note": "Instructions that shape how the AI responds.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "ai_prompts", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "ai_prompts", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "ai_prompts", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "ai_prompts", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "ai_prompts", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "ai_prompts", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "ai_prompts", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "ai_prompts", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "ai_prompts", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "ai_prompts", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "ai_prompts", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "ai_prompts", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 6, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_button", "field": "id", "type": "uuid", "schema": {"name": "id", "table": "block_button", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_button", "field": "id", "special": ["uuid"], "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_button", "field": "sort", "type": "integer", "schema": {"name": "sort", "table": "block_button", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_button", "field": "sort", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 2, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_button", "field": "type", "type": "string", "schema": {"name": "type", "table": "block_button", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_button", "field": "type", "special": null, "interface": "select-dropdown", "options": {"choices": [{"text": "Page", "value": "page", "icon": "web_asset"}, {"text": "Post", "value": "post", "icon": "article"}, {"text": "URL", "value": "url", "icon": "link"}]}, "display": "labels", "display_options": {"format": true, "choices": [{"text": "Page", "value": "page", "icon": "web_asset"}, {"text": "Post", "value": "post", "icon": "article"}, {"text": "URL", "value": "url", "icon": "link"}]}, "readonly": false, "hidden": false, "sort": 8, "width": "half", "translations": null, "note": "What type of link is this? Page and Post allow you to link to internal content. URL is for external content. Group can contain other menu items.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_button", "field": "page", "type": "uuid", "schema": {"name": "page", "table": "block_button", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "pages", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_button", "field": "page", "special": ["m2o"], "interface": "select-dropdown-m2o", "options": {"template": "{{title}} - {{permalink}}"}, "display": "related-values", "display_options": null, "readonly": false, "hidden": true, "sort": 9, "width": "half", "translations": null, "note": "The internal page to link to.", "conditions": [{"name": "IF type = pages", "rule": {"_and": [{"type": {"_eq": "page"}}]}, "hidden": false, "options": {"enableCreate": true, "enableSelect": true}}], "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_button", "field": "post", "type": "uuid", "schema": {"name": "post", "table": "block_button", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "posts", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_button", "field": "post", "special": ["m2o"], "interface": "select-dropdown-m2o", "options": {"template": "{{title}} - {{slug}}"}, "display": "related-values", "display_options": null, "readonly": false, "hidden": true, "sort": 10, "width": "half", "translations": null, "note": "The internal post to link to.", "conditions": [{"name": "IF type = post", "rule": {"_and": [{"type": {"_eq": "post"}}]}, "hidden": false, "options": {"enableCreate": true, "enableSelect": true}}], "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_button", "field": "label", "type": "string", "schema": {"name": "label", "table": "block_button", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_button", "field": "label", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 12, "width": "half", "translations": null, "note": "Text to include on the button.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_button", "field": "variant", "type": "string", "schema": {"name": "variant", "table": "block_button", "data_type": "character varying", "default_value": "solid", "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_button", "field": "variant", "special": null, "interface": "select-dropdown", "options": {"choices": [{"text": "<PERSON><PERSON><PERSON>", "value": "default"}, {"text": "Outline", "value": "outline"}, {"text": "Soft", "value": "soft"}, {"text": "Ghost", "value": "ghost"}, {"text": "Link", "value": "link"}]}, "display": "formatted-value", "display_options": {"format": true}, "readonly": false, "hidden": false, "sort": 13, "width": "half", "translations": null, "note": "What type of button", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_button", "field": "button_group", "type": "uuid", "schema": {"name": "button_group", "table": "block_button", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "block_button_group", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_button", "field": "button_group", "special": null, "interface": "select-dropdown-m2o", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 3, "width": "full", "translations": null, "note": "The id of the Button Group this button belongs to.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_button", "field": "url", "type": "string", "schema": {"name": "url", "table": "block_button", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_button", "field": "url", "special": null, "interface": "input", "options": {"iconLeft": "link", "trim": true}, "display": "formatted-value", "display_options": {"format": true}, "readonly": false, "hidden": true, "sort": 11, "width": "half", "translations": null, "note": "The URL to link to. Could be relative (ie `/my-page`) or a full external URL (ie `https://docs.directus.io`)", "conditions": [{"name": "If type = external", "rule": {"_and": [{"type": {"_eq": "url"}}]}, "hidden": false, "options": {"font": "sans-serif", "trim": false, "masked": false, "clear": false, "slug": false}}], "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_button", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "block_button", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_button", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_button", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "block_button", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_button", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_button", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "block_button", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_button", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 6, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_button", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "block_button", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_button", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 7, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_button_group", "field": "id", "type": "uuid", "schema": {"name": "id", "table": "block_button_group", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_button_group", "field": "id", "special": ["uuid"], "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_button_group", "field": "sort", "type": "integer", "schema": {"name": "sort", "table": "block_button_group", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_button_group", "field": "sort", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 2, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_button_group", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "block_button_group", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_button_group", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_button_group", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "block_button_group", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_button_group", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_button_group", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "block_button_group", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_button_group", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_button_group", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "block_button_group", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_button_group", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 6, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_form", "field": "id", "type": "uuid", "schema": {"name": "id", "table": "block_form", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_form", "field": "id", "special": ["uuid"], "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_form", "field": "form", "type": "uuid", "schema": {"name": "form", "table": "block_form", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "forms", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_form", "field": "form", "special": ["m2o"], "interface": "select-dropdown-m2o", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 9, "width": "full", "translations": null, "note": "Form to show within block", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_form", "field": "headline", "type": "text", "schema": {"name": "headline", "table": "block_form", "data_type": "text", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_form", "field": "headline", "special": null, "interface": "input", "options": {"defaultView": {"center": {"lng": 0, "lat": 0}, "zoom": 0, "bearing": 0, "pitch": 0}, "geometryType": "Point"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "full", "translations": null, "note": "Larger main headline for this page section.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_form", "field": "tagline", "type": "string", "schema": {"name": "tagline", "table": "block_form", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_form", "field": "tagline", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "full", "translations": null, "note": "Smaller copy shown above the headline to label a section or add extra context.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_form", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "block_form", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_form", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 2, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_form", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "block_form", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_form", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_form", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "block_form", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_form", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_form", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "block_form", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_form", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_gallery", "field": "headline", "type": "text", "schema": {"name": "headline", "table": "block_gallery", "data_type": "text", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_gallery", "field": "headline", "special": null, "interface": "input", "options": {"toolbar": ["italic", "removeformat"]}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "full", "translations": null, "note": "Larger main headline for this page section.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_gallery", "field": "id", "type": "uuid", "schema": {"name": "id", "table": "block_gallery", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_gallery", "field": "id", "special": ["uuid"], "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_gallery", "field": "tagline", "type": "string", "schema": {"name": "tagline", "table": "block_gallery", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_gallery", "field": "tagline", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "full", "translations": null, "note": "Smaller copy shown above the headline to label a section or add extra context.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_gallery", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "block_gallery", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_gallery", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 2, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_gallery", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "block_gallery", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_gallery", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_gallery", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "block_gallery", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_gallery", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_gallery", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "block_gallery", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_gallery", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_gallery_items", "field": "id", "type": "uuid", "schema": {"name": "id", "table": "block_gallery_items", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_gallery_items", "field": "id", "special": ["uuid"], "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_gallery_items", "field": "block_gallery", "type": "uuid", "schema": {"name": "block_gallery", "table": "block_gallery_items", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "block_gallery", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_gallery_items", "field": "block_gallery", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 7, "width": "full", "translations": null, "note": "The id of the gallery block this item belongs to.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_gallery_items", "field": "directus_file", "type": "uuid", "schema": {"name": "directus_file", "table": "block_gallery_items", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_files", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_gallery_items", "field": "directus_file", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 8, "width": "full", "translations": null, "note": "The id of the file included in the gallery.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_gallery_items", "field": "sort", "type": "integer", "schema": {"name": "sort", "table": "block_gallery_items", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_gallery_items", "field": "sort", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 2, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_gallery_items", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "block_gallery_items", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_gallery_items", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_gallery_items", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "block_gallery_items", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_gallery_items", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_gallery_items", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "block_gallery_items", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_gallery_items", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_gallery_items", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "block_gallery_items", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_gallery_items", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 6, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_hero", "field": "headline", "type": "text", "schema": {"name": "headline", "table": "block_hero", "data_type": "text", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_hero", "field": "headline", "special": null, "interface": "input", "options": {"defaultView": {"center": {"lng": 0, "lat": 0}, "zoom": 0, "bearing": 0, "pitch": 0}, "geometryType": "Point"}, "display": "raw", "display_options": {"background": "#A2B5CD"}, "readonly": false, "hidden": false, "sort": 8, "width": "full", "translations": null, "note": "Larger main headline for this page section.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_hero", "field": "id", "type": "uuid", "schema": {"name": "id", "table": "block_hero", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_hero", "field": "id", "special": ["uuid"], "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_hero", "field": "image", "type": "uuid", "schema": {"name": "image", "table": "block_hero", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_files", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_hero", "field": "image", "special": ["file"], "interface": "file-image", "options": {"crop": false, "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 12, "width": "full", "translations": null, "note": "Featured image in the hero.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_hero", "field": "button_group", "type": "uuid", "schema": {"name": "button_group", "table": "block_hero", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "block_button_group", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_hero", "field": "button_group", "special": ["m2o"], "interface": "select-dropdown-m2o", "options": {"template": "{{buttons.label}}", "createRelatedItem": "always", "enableLink": true, "enableSelect": false}, "display": "related-values", "display_options": {"template": "{{buttons.label}}"}, "readonly": false, "hidden": false, "sort": 10, "width": "full", "translations": null, "note": "Action buttons that show below headline and description.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_hero", "field": "description", "type": "text", "schema": {"name": "description", "table": "block_hero", "data_type": "text", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_hero", "field": "description", "special": null, "interface": "input-multiline", "options": {"folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 9, "width": "full", "translations": null, "note": "Supporting copy that shows below the headline.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_hero", "field": "tagline", "type": "string", "schema": {"name": "tagline", "table": "block_hero", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_hero", "field": "tagline", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "full", "translations": null, "note": "Smaller copy shown above the headline to label a section or add extra context.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_hero", "field": "layout", "type": "string", "schema": {"name": "layout", "table": "block_hero", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_hero", "field": "layout", "special": null, "interface": "radio-cards-interface", "options": {"choices": [{"text": "Image Left", "value": "image_left", "icon": "format_image_left", "icon_type": "image", "image": "d5a1290f-8819-4e7c-b292-bffe5b1c8274.jpg"}, {"text": "Image Center", "value": "image_center", "icon": "image", "icon_type": "image", "image": "8a652e52-a275-4dde-9fc5-edf2188afe56.jpg"}, {"text": "Image Right", "value": "image_right", "icon": "format_image_right", "icon_type": "image", "image": "fe7c7e04-5aac-4370-8bbd-6fd578d26ea1.jpg"}], "gridSize": 3}, "display": "labels", "display_options": {"format": true, "choices": [{"text": "Image Left", "value": "image_left", "icon": "format_image_left"}, {"text": "Image Center", "value": "image_center", "icon": "image"}, {"text": "Image Right", "value": "image_right", "icon": "format_image_right"}]}, "readonly": false, "hidden": false, "sort": 13, "width": "full", "translations": null, "note": "The layout for the component. You can set the image to display left, right, or in the center of page..", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_hero", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "block_hero", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_hero", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 2, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_hero", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "block_hero", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_hero", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_hero", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "block_hero", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_hero", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_hero", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "block_hero", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_hero", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_posts", "field": "id", "type": "uuid", "schema": {"name": "id", "table": "block_posts", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_posts", "field": "id", "special": ["uuid"], "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_posts", "field": "headline", "type": "text", "schema": {"name": "headline", "table": "block_posts", "data_type": "text", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_posts", "field": "headline", "special": null, "interface": "input", "options": {"defaultView": {"center": {"lng": 0, "lat": 0}, "zoom": 0, "bearing": 0, "pitch": 0}, "geometryType": "Point"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "full", "translations": null, "note": "Larger main headline for this page section.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_posts", "field": "collection", "type": "string", "schema": {"name": "collection", "table": "block_posts", "data_type": "character varying", "default_value": "posts", "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_posts", "field": "collection", "special": null, "interface": "select-radio", "options": {"choices": [{"text": "Posts", "value": "posts"}]}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 9, "width": "half", "translations": null, "note": "The collection of content to fetch and display on the page within this block.", "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_posts", "field": "tagline", "type": "string", "schema": {"name": "tagline", "table": "block_posts", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_posts", "field": "tagline", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "full", "translations": null, "note": "Smaller copy shown above the headline to label a section or add extra context.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_posts", "field": "limit", "type": "integer", "schema": {"name": "limit", "table": "block_posts", "data_type": "integer", "default_value": 6, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_posts", "field": "limit", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 10, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_posts", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "block_posts", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_posts", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 2, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_posts", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "block_posts", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_posts", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_posts", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "block_posts", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_posts", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_posts", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "block_posts", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_posts", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing", "field": "id", "type": "uuid", "schema": {"name": "id", "table": "block_pricing", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_pricing", "field": "id", "special": ["uuid"], "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing", "field": "headline", "type": "text", "schema": {"name": "headline", "table": "block_pricing", "data_type": "text", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_pricing", "field": "headline", "special": null, "interface": "input", "options": {"defaultView": {"center": {"lng": 0, "lat": 0}, "zoom": 0, "bearing": 0, "pitch": 0}, "geometryType": "Point"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "full", "translations": null, "note": "Larger main headline for this page section.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing", "field": "tagline", "type": "string", "schema": {"name": "tagline", "table": "block_pricing", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_pricing", "field": "tagline", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "full", "translations": null, "note": "Smaller copy shown above the headline to label a section or add extra context.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "block_pricing", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_pricing", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 2, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "block_pricing", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_pricing", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "block_pricing", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_pricing", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "block_pricing", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_pricing", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing_cards", "field": "id", "type": "uuid", "schema": {"name": "id", "table": "block_pricing_cards", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_pricing_cards", "field": "id", "special": ["uuid"], "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing_cards", "field": "title", "type": "string", "schema": {"name": "title", "table": "block_pricing_cards", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_pricing_cards", "field": "title", "special": null, "interface": "input", "options": {"placeholder": "Starter Plan"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "half", "translations": null, "note": "Name of the pricing plan. Shown at the top of the card.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing_cards", "field": "description", "type": "text", "schema": {"name": "description", "table": "block_pricing_cards", "data_type": "text", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_pricing_cards", "field": "description", "special": null, "interface": "input-multiline", "options": {"placeholder": "For small businesses and indie hackers"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "half", "translations": null, "note": "Short, one sentence description of the pricing plan and who it is for.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing_cards", "field": "price", "type": "string", "schema": {"name": "price", "table": "block_pricing_cards", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_pricing_cards", "field": "price", "special": null, "interface": "input", "options": {"placeholder": "$199 /month"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 9, "width": "half", "translations": null, "note": "Price and term for the pricing plan. (ie `$199/mo`)", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing_cards", "field": "badge", "type": "string", "schema": {"name": "badge", "table": "block_pricing_cards", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_pricing_cards", "field": "badge", "special": null, "interface": "input", "options": {"placeholder": "Most popular"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 10, "width": "half", "translations": null, "note": "Badge that displays at the top of the pricing plan card to add helpful context.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing_cards", "field": "features", "type": "json", "schema": {"name": "features", "table": "block_pricing_cards", "data_type": "json", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_pricing_cards", "field": "features", "special": ["cast-json"], "interface": "simple-list", "options": {"limit": 10}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 11, "width": "half", "translations": null, "note": "Short list of features included in this plan. Press `Enter` to add another item to the list.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing_cards", "field": "button", "type": "uuid", "schema": {"name": "button", "table": "block_pricing_cards", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "block_button", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_pricing_cards", "field": "button", "special": ["m2o"], "interface": "select-dropdown-m2o", "options": {"createRelatedItem": "always", "template": "{{label}} • {{type}}", "enableSelect": false, "enableLink": true}, "display": "related-values", "display_options": {"template": "{{label}} • {{type}}"}, "readonly": false, "hidden": false, "sort": 12, "width": "full", "translations": null, "note": "The action button / link shown at the bottom of the pricing card.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing_cards", "field": "pricing", "type": "uuid", "schema": {"name": "pricing", "table": "block_pricing_cards", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "block_pricing", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_pricing_cards", "field": "pricing", "special": null, "interface": "select-dropdown-m2o", "options": {"enableSelect": false, "enableLink": true}, "display": "related-values", "display_options": null, "readonly": false, "hidden": true, "sort": 13, "width": "full", "translations": null, "note": "The id of the pricing block this card belongs to.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing_cards", "field": "is_highlighted", "type": "boolean", "schema": {"name": "is_highlighted", "table": "block_pricing_cards", "data_type": "boolean", "default_value": false, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_pricing_cards", "field": "is_highlighted", "special": ["cast-boolean"], "interface": "boolean", "options": {"label": "Highlighted"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 14, "width": "half", "translations": null, "note": "Add highlighted border around the pricing plan to make it stand out.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing_cards", "field": "sort", "type": "integer", "schema": {"name": "sort", "table": "block_pricing_cards", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_pricing_cards", "field": "sort", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 2, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing_cards", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "block_pricing_cards", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_pricing_cards", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing_cards", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "block_pricing_cards", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_pricing_cards", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing_cards", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "block_pricing_cards", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_pricing_cards", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing_cards", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "block_pricing_cards", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_pricing_cards", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 6, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_richtext", "field": "content", "type": "text", "schema": {"name": "content", "table": "block_richtext", "data_type": "text", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_richtext", "field": "content", "special": null, "interface": "input-rich-text-html", "options": {"customFormats": null, "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 9, "width": "full", "translations": null, "note": "Rich text content for this block.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_richtext", "field": "headline", "type": "string", "schema": {"name": "headline", "table": "block_richtext", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_richtext", "field": "headline", "special": null, "interface": "input", "options": null, "display": "raw", "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "full", "translations": null, "note": "Larger main headline for this page section.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_richtext", "field": "id", "type": "uuid", "schema": {"name": "id", "table": "block_richtext", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_richtext", "field": "id", "special": ["uuid"], "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_richtext", "field": "alignment", "type": "string", "schema": {"name": "alignment", "table": "block_richtext", "data_type": "character varying", "default_value": "center", "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_richtext", "field": "alignment", "special": null, "interface": "select-radio", "options": {"choices": [{"text": "Left", "value": "left"}, {"text": "Center", "value": "center"}]}, "display": "formatted-value", "display_options": null, "readonly": false, "hidden": false, "sort": 10, "width": "full", "translations": null, "note": "Controls how the content block is positioned on the page. Choose \"Left\" to align the block against the left margin or \"Center\" to position the block in the middle of the page. This setting affects the entire content block's placement, not the text alignment within it.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_richtext", "field": "tagline", "type": "string", "schema": {"name": "tagline", "table": "block_richtext", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_richtext", "field": "tagline", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "full", "translations": null, "note": "Smaller copy shown above the headline to label a section or add extra context.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_richtext", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "block_richtext", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_richtext", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 2, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_richtext", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "block_richtext", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_richtext", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_richtext", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "block_richtext", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "block_richtext", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_richtext", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "block_richtext", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "block_richtext", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "directus_settings", "field": "command_palette_settings", "type": "json", "schema": {"name": "command_palette_settings", "table": "directus_settings", "data_type": "json", "default_value": {}, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "directus_settings", "field": "command_palette_settings", "special": ["cast-json"], "interface": "input-code", "options": null, "display": "raw", "display_options": null, "readonly": false, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": "Settings for the Command Palette Module.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_fields", "field": "id", "type": "uuid", "schema": {"name": "id", "table": "form_fields", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "form_fields", "field": "id", "special": ["uuid"], "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_fields", "field": "name", "type": "string", "schema": {"name": "name", "table": "form_fields", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "form_fields", "field": "name", "special": null, "interface": "input", "options": {"slug": true, "trim": true, "iconLeft": "key", "placeholder": "full-name"}, "display": "formatted-value", "display_options": {"format": true}, "readonly": false, "hidden": false, "sort": 8, "width": "half", "translations": null, "note": "Unique field identifier, not shown to users (lowercase, hyphenated)", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_fields", "field": "type", "type": "string", "schema": {"name": "type", "table": "form_fields", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "form_fields", "field": "type", "special": null, "interface": "radio-cards-interface", "options": {"choices": [{"text": "Text", "value": "text", "icon": "format_color_text", "icon_type": "icon"}, {"text": "Textarea", "value": "textarea", "icon": "display_external_input", "icon_type": "icon"}, {"text": "Checkbox", "value": "checkbox", "icon": "check_box_outline_blank", "icon_type": "icon"}, {"text": "Checkbox Group", "value": "checkbox_group", "icon": "library_add_check", "icon_type": "icon"}, {"text": "Radio", "value": "radio", "icon": "radio_button_unchecked", "icon_type": "icon"}, {"text": "File", "value": "file", "icon": "attach_file", "icon_type": "icon"}, {"text": "Select", "value": "select", "icon": "text_select_move_down", "icon_type": "icon"}, {"text": "Hidden", "value": "hidden", "icon": "hide_source", "icon_type": "icon"}], "gridSize": 5}, "display": "labels", "display_options": {"choices": [{"text": "Text", "value": "text", "icon": "format_color_text"}, {"text": "Textarea", "value": "textarea", "icon": "display_external_input"}, {"text": "Checkbox", "value": "checkbox", "icon": "check_box_outline_blank"}, {"text": "Checkbox Group", "value": "checkbox_group", "icon": "library_add_check"}, {"text": "Radio", "value": "radio", "icon": "radio_button_unchecked"}, {"text": "File", "value": "file", "icon": "attach_file"}, {"text": "Select", "value": "select", "icon": "text_select_move_down"}, {"text": "Hidden", "value": "hidden", "icon": "hide_source"}]}, "readonly": false, "hidden": false, "sort": 10, "width": "full", "translations": null, "note": "Input type for the field", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_fields", "field": "label", "type": "string", "schema": {"name": "label", "table": "form_fields", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "form_fields", "field": "label", "special": null, "interface": "input", "options": {"placeholder": "Your Full Name"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 9, "width": "half", "translations": null, "note": "Text label shown to form users.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_fields", "field": "placeholder", "type": "string", "schema": {"name": "placeholder", "table": "form_fields", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "form_fields", "field": "placeholder", "special": null, "interface": "input", "options": {"placeholder": "<PERSON>"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 12, "width": "half", "translations": null, "note": "Default text shown in empty input.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_fields", "field": "help", "type": "string", "schema": {"name": "help", "table": "form_fields", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "form_fields", "field": "help", "special": null, "interface": "input", "options": {"placeholder": "Use first and last name"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 13, "width": "half", "translations": null, "note": "Additional instructions shown below the input", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_fields", "field": "validation", "type": "string", "schema": {"name": "validation", "table": "form_fields", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "form_fields", "field": "validation", "special": null, "interface": "input", "options": {"placeholder": "max:255", "trim": true}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 15, "width": "half", "translations": null, "note": "Available rules: `email`, `url`, `min:5`, `max:20`, `length:10`. Combine with pipes example: `email|max:255`", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_fields", "field": "width", "type": "string", "schema": {"name": "width", "table": "form_fields", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "form_fields", "field": "width", "special": null, "interface": "select-radio", "options": {"choices": [{"text": "100%", "value": "100"}, {"text": "67%", "value": "67"}, {"text": "50%", "value": "50"}, {"text": "33%", "value": "33"}]}, "display": "formatted-value", "display_options": {"format": true, "suffix": "%"}, "readonly": false, "hidden": false, "sort": 16, "width": "full", "translations": null, "note": "Field width on the form", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_fields", "field": "choices", "type": "json", "schema": {"name": "choices", "table": "form_fields", "data_type": "json", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "form_fields", "field": "choices", "special": ["cast-json"], "interface": "list", "options": {"fields": [{"field": "text", "name": "text", "type": "string", "meta": {"field": "text", "width": "half", "type": "string", "interface": "input", "note": "Displayed label to user", "required": true, "options": {"placeholder": "Sales Team"}}}, {"field": "value", "name": "value", "type": "string", "meta": {"field": "value", "width": "half", "type": "string", "interface": "input", "note": "Stored value", "required": true, "options": {"placeholder": "sales"}}}], "template": "{{ text }}"}, "display": "formatted-json-value", "display_options": {"format": "{{ label }}"}, "readonly": false, "hidden": true, "sort": 11, "width": "full", "translations": null, "note": "Options for radio or select inputs", "conditions": [{"name": "Show Choices", "rule": {"_and": [{"_or": [{"type": {"_eq": "select"}}, {"type": {"_eq": "checkbox_group"}}, {"type": {"_eq": "radio"}}]}]}, "hidden": false, "options": {}}], "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_fields", "field": "form", "type": "uuid", "schema": {"name": "form", "table": "form_fields", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "forms", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "form_fields", "field": "form", "special": ["m2o"], "interface": "select-dropdown-m2o", "options": {"template": "{{title}}"}, "display": "related-values", "display_options": {"template": "{{title}}"}, "readonly": false, "hidden": true, "sort": 7, "width": "full", "translations": null, "note": "Parent form this field belongs to.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_fields", "field": "sort", "type": "integer", "schema": {"name": "sort", "table": "form_fields", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "form_fields", "field": "sort", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 2, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_fields", "field": "required", "type": "boolean", "schema": {"name": "required", "table": "form_fields", "data_type": "boolean", "default_value": false, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "form_fields", "field": "required", "special": ["cast-boolean"], "interface": "boolean", "options": {"label": "Required"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 14, "width": "half", "translations": null, "note": "Make this field mandatory to complete.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_fields", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "form_fields", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "form_fields", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_fields", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "form_fields", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "form_fields", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_fields", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "form_fields", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "form_fields", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_fields", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "form_fields", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "form_fields", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 6, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_submission_values", "field": "id", "type": "uuid", "schema": {"name": "id", "table": "form_submission_values", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "form_submission_values", "field": "id", "special": ["uuid"], "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_submission_values", "field": "form_submission", "type": "uuid", "schema": {"name": "form_submission", "table": "form_submission_values", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "form_submissions", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "form_submission_values", "field": "form_submission", "special": null, "interface": "select-dropdown-m2o", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 3, "width": "full", "translations": null, "note": "Parent form submission for this value.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_submission_values", "field": "field", "type": "uuid", "schema": {"name": "field", "table": "form_submission_values", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "form_fields", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "form_submission_values", "field": "field", "special": ["m2o"], "interface": "select-dropdown-m2o", "options": {"template": "{{name}} • {{type}}"}, "display": "related-values", "display_options": {"template": "{{name}} • {{type}}"}, "readonly": false, "hidden": false, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_submission_values", "field": "value", "type": "text", "schema": {"name": "value", "table": "form_submission_values", "data_type": "text", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "form_submission_values", "field": "value", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 4, "width": "half", "translations": null, "note": "The data entered by the user for this specific field in the form submission.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_submission_values", "field": "sort", "type": "integer", "schema": {"name": "sort", "table": "form_submission_values", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "form_submission_values", "field": "sort", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 6, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_submission_values", "field": "file", "type": "uuid", "schema": {"name": "file", "table": "form_submission_values", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_files", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "form_submission_values", "field": "file", "special": ["file"], "interface": "file", "options": {"folder": "e6308546-92fb-4b10-b586-eefaf1d97f7f"}, "display": "file", "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_submission_values", "field": "timestamp", "type": "timestamp", "schema": {"name": "timestamp", "table": "form_submission_values", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "form_submission_values", "field": "timestamp", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 2, "width": "half", "translations": null, "note": "Form submission date and time.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_submissions", "field": "id", "type": "uuid", "schema": {"name": "id", "table": "form_submissions", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "form_submissions", "field": "id", "special": ["uuid"], "interface": "input", "options": {"iconLeft": "vpn_key"}, "display": null, "display_options": null, "readonly": true, "hidden": false, "sort": 3, "width": "half", "translations": null, "note": "Unique ID for this specific form submission", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_submissions", "field": "timestamp", "type": "timestamp", "schema": {"name": "timestamp", "table": "form_submissions", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "form_submissions", "field": "timestamp", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": false, "sort": 4, "width": "half", "translations": null, "note": "Form submission date and time.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_submissions", "field": "form", "type": "uuid", "schema": {"name": "form", "table": "form_submissions", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "forms", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "form_submissions", "field": "form", "special": ["m2o"], "interface": "select-dropdown-m2o", "options": {"template": "{{title}}", "enableLink": true}, "display": "related-values", "display_options": {"template": "{{title}}"}, "readonly": true, "hidden": false, "sort": 2, "width": "full", "translations": null, "note": "Associated form for this submission.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "forms", "field": "id", "type": "uuid", "schema": {"name": "id", "table": "forms", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "forms", "field": "id", "special": ["uuid"], "interface": "input", "options": {"iconLeft": "vpn_key"}, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "forms", "field": "on_success", "type": "string", "schema": {"name": "on_success", "table": "forms", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "forms", "field": "on_success", "special": null, "interface": "select-dropdown", "options": {"choices": [{"text": "Redirect to URL", "value": "redirect", "icon": "arrow_outward"}, {"text": "Show Message", "value": "message", "icon": "chat"}]}, "display": "labels", "display_options": {"choices": [{"text": "Redirect to URL", "value": "redirect"}, {"text": "Show Message", "value": "message"}]}, "readonly": false, "hidden": false, "sort": 4, "width": "half", "translations": null, "note": "Action after successful submission.", "conditions": null, "required": false, "group": "meta_fields", "validation": null, "validation_message": null}}, {"collection": "forms", "field": "sort", "type": "integer", "schema": {"name": "sort", "table": "forms", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "forms", "field": "sort", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 2, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "forms", "field": "submit_label", "type": "string", "schema": {"name": "submit_label", "table": "forms", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "forms", "field": "submit_label", "special": null, "interface": "input", "options": {"placeholder": "Sign Up Now", "iconLeft": "smart_button"}, "display": "raw", "display_options": null, "readonly": false, "hidden": false, "sort": 3, "width": "half", "translations": null, "note": "Text shown on submit button.", "conditions": null, "required": false, "group": "meta_fields", "validation": null, "validation_message": null}}, {"collection": "forms", "field": "success_message", "type": "text", "schema": {"name": "success_message", "table": "forms", "data_type": "text", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "forms", "field": "success_message", "special": null, "interface": "input-multiline", "options": {"placeholder": "Thanks for reaching out! We'll be in touch soon."}, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 5, "width": "full", "translations": null, "note": "Message shown after successful submission.", "conditions": [{"name": "If Message", "readonly": false, "hidden": false, "options": {"toolbar": ["bold", "italic", "underline", "h1", "h2", "h3", "numlist", "bullist", "removeformat", "blockquote", "customLink", "customImage", "customMedia", "hr", "code", "fullscreen"], "font": "sans-serif"}, "rule": {"_and": [{"on_success": {"_eq": "message"}}]}}], "required": false, "group": "meta_fields", "validation": null, "validation_message": null}}, {"collection": "forms", "field": "title", "type": "string", "schema": {"name": "title", "table": "forms", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "forms", "field": "title", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "half", "translations": null, "note": "Form name (for internal reference).", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "forms", "field": "success_redirect_url", "type": "string", "schema": {"name": "success_redirect_url", "table": "forms", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "forms", "field": "success_redirect_url", "special": null, "interface": "input", "options": {"iconLeft": "link"}, "display": "raw", "display_options": null, "readonly": false, "hidden": true, "sort": 6, "width": "full", "translations": null, "note": "Destination URL after successful submission.", "conditions": [{"name": "If Redirect", "options": {"font": "sans-serif", "trim": false, "masked": false, "clear": false, "slug": false}, "hidden": false, "rule": {"_and": [{"on_success": {"_eq": "redirect"}}]}}], "required": false, "group": "meta_fields", "validation": null, "validation_message": null}}, {"collection": "forms", "field": "is_active", "type": "boolean", "schema": {"name": "is_active", "table": "forms", "data_type": "boolean", "default_value": true, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "forms", "field": "is_active", "special": ["cast-boolean"], "interface": "boolean", "options": {"label": "Active"}, "display": "boolean", "display_options": {"labelOn": "Active", "labelOff": "Inactive"}, "readonly": false, "hidden": false, "sort": 9, "width": "half", "translations": [{"language": "en-US", "translation": "Active"}], "note": "Show or hide this form from the site.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "forms", "field": "emails", "type": "json", "schema": {"name": "emails", "table": "forms", "data_type": "json", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "forms", "field": "emails", "special": ["cast-json"], "interface": "list", "options": {"fields": [{"field": "to", "name": "to", "type": "json", "meta": {"field": "to", "width": "full", "type": "json", "required": true, "note": "Add an email address and press enter. To use a form field as the email address use merge tags `{# #}` (e.g. `{# email #}.", "interface": "tags", "options": {"choices": null}, "display": "labels", "display_options": {"format": false}}}, {"field": "subject", "name": "subject", "type": "string", "meta": {"field": "subject", "width": "full", "type": "string", "required": true, "note": "Email subject line. You can use merge tags like this `Hi {# first-name #}`.", "interface": "input", "display": "formatted-value", "display_options": {}}}, {"field": "message", "name": "message", "type": "text", "meta": {"field": "message", "width": "full", "type": "text", "required": true, "note": "Include a message for the email body. To merge form responses use merge tags with the field name like `{# first-name #}`.", "interface": "input-rich-text-html", "options": {"folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "toolbar": ["undo", "redo", "bold", "italic", "underline", "h1", "h2", "h3", "numlist", "bullist", "removeformat", "blockquote", "customLink", "customImage", "table", "hr", "code", "fullscreen"]}}}], "addLabel": "Add New Email", "template": "To: {{ to }} • Subject: {{ subject }}"}, "display": "formatted-json-value", "display_options": {"format": "To: {{ to }} • Subject: {{ subject }}"}, "readonly": false, "hidden": false, "sort": 2, "width": "full", "translations": null, "note": "Setup email notifications when forms are submitted.", "conditions": null, "required": false, "group": "meta_emails", "validation": null, "validation_message": null}}, {"collection": "forms", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "forms", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "forms", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "forms", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "forms", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "forms", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "forms", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "forms", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "forms", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "forms", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "forms", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "forms", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 6, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "globals", "field": "description", "type": "text", "schema": {"name": "description", "table": "globals", "data_type": "text", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "globals", "field": "description", "special": null, "interface": "input-multiline", "options": {"softLength": 160}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 16, "width": "full", "translations": null, "note": "Site summary for search results.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "globals", "field": "id", "type": "uuid", "schema": {"name": "id", "table": "globals", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "globals", "field": "id", "special": ["uuid"], "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "globals", "field": "social_links", "type": "json", "schema": {"name": "social_links", "table": "globals", "data_type": "json", "default_value": [], "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "globals", "field": "social_links", "special": ["cast-json"], "interface": "inline-repeater-interface", "options": {"fields": [{"field": "url", "name": "url", "type": "string", "meta": {"field": "url", "width": "half", "type": "string", "interface": "input", "options": {"trim": true, "placeholder": "https://www.linkedin.com/in/fullprofile"}, "note": "Full profile URL (not just username)"}}, {"field": "service", "name": "service", "type": "string", "meta": {"field": "service", "width": "half", "type": "string", "required": null, "note": "Social media platform name", "interface": "select-dropdown", "options": {"choices": [{"text": "Facebook", "value": "facebook", "icon": "facebook"}, {"text": "Instagram", "value": "instagram", "icon": "instagram"}, {"text": "LinkedIn", "value": "linkedin", "icon": "linkedin"}, {"text": "X", "value": "x", "icon": "twitter"}, {"text": "Vimeo", "value": "vimeo", "icon": "vimeo"}, {"text": "YouTube", "value": "youtube", "icon": "youtube"}, {"text": "GitHub", "value": "github", "icon": "github"}, {"text": "Discord", "value": "discord", "icon": "discord"}, {"text": "<PERSON>er", "value": "docker", "icon": "docker"}]}, "display": "labels", "display_options": {"choices": [{"text": "Facebook", "value": "facebook", "icon": "facebook"}, {"text": "Instagram", "value": "instagram", "icon": "instagram"}, {"text": "LinkedIn", "value": "linkedin", "icon": "linkedin"}, {"text": "X", "value": "x", "icon": "twitter"}, {"text": "Vimeo", "value": "vimeo", "icon": "vimeo"}, {"text": "YouTube", "value": "youtube", "icon": "youtube"}, {"text": "GitHub", "value": "github", "icon": "github"}, {"text": "Discord", "value": "discord", "icon": "discord"}, {"text": "<PERSON>er", "value": "docker", "icon": "docker"}]}}}], "template": "{{ service }}", "addLabel": "Add New Link"}, "display": "labels", "display_options": {}, "readonly": false, "hidden": false, "sort": 18, "width": "full", "translations": null, "note": "Social media profile URLs", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "globals", "field": "tagline", "type": "string", "schema": {"name": "tagline", "table": "globals", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "globals", "field": "tagline", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 15, "width": "full", "translations": null, "note": "Short phrase describing the site.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "globals", "field": "title", "type": "string", "schema": {"name": "title", "table": "globals", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "globals", "field": "title", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "half", "translations": null, "note": "Main site title", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "globals", "field": "url", "type": "string", "schema": {"name": "url", "table": "globals", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "globals", "field": "url", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 9, "width": "half", "translations": null, "note": "Public URL for the website", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "globals", "field": "favicon", "type": "uuid", "schema": {"name": "favicon", "table": "globals", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_files", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "globals", "field": "favicon", "special": ["file"], "interface": "file-image", "options": {"crop": false, "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 13, "width": "half", "translations": null, "note": "Small icon for browser tabs. 1:1 ratio. No larger than 512px × 512px.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "globals", "field": "logo", "type": "uuid", "schema": {"name": "logo", "table": "globals", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_files", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "globals", "field": "logo", "special": ["file"], "interface": "file-image", "options": {"crop": false, "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 11, "width": "half", "translations": null, "note": "Main logo shown on the site (for light mode).", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "globals", "field": "openai_api_key", "type": "string", "schema": {"name": "openai_api_key", "table": "globals", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "globals", "field": "openai_api_key", "special": null, "interface": "input", "options": {"trim": true, "iconLeft": "vpn_key_alert", "masked": true}, "display": "formatted-value", "display_options": {"masked": true}, "readonly": false, "hidden": false, "sort": 2, "width": "half", "translations": null, "note": "Secret OpenAI API key. Don't share with anyone outside your team.", "conditions": null, "required": false, "group": "meta_credentials", "validation": null, "validation_message": null}}, {"collection": "globals", "field": "directus_url", "type": "string", "schema": {"name": "directus_url", "table": "globals", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "globals", "field": "directus_url", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 3, "width": "half", "translations": null, "note": "The public URL for this Directus instance. Used in Flows.", "conditions": null, "required": false, "group": "meta_credentials", "validation": null, "validation_message": null}}, {"collection": "globals", "field": "logo_dark_mode", "type": "uuid", "schema": {"name": "logo_dark_mode", "table": "globals", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_files", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "globals", "field": "logo_dark_mode", "special": ["file"], "interface": "file-image", "options": {"crop": false, "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 12, "width": "half", "translations": [{"language": "en-US", "translation": "Dark Mode Logo"}], "note": "Main logo shown on the site (for dark mode).", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "globals", "field": "accent_color", "type": "string", "schema": {"name": "accent_color", "table": "globals", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "globals", "field": "accent_color", "special": null, "interface": "select-color", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 14, "width": "half", "translations": null, "note": "Accent color for the website (used on buttons, links, etc).", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "globals", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "globals", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "globals", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 2, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "globals", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "globals", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "globals", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "globals", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "globals", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "globals", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "globals", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "globals", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "globals", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation", "field": "id", "type": "string", "schema": {"name": "id", "table": "navigation", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "navigation", "field": "id", "special": null, "interface": "input", "options": {"iconLeft": "vpn_key"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "half", "translations": null, "note": "Unique identifier for this menu. Can't be edited after creation.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation", "field": "title", "type": "string", "schema": {"name": "title", "table": "navigation", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "navigation", "field": "title", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 6, "width": "half", "translations": null, "note": "What is the name of this menu? Only used internally.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation", "field": "is_active", "type": "boolean", "schema": {"name": "is_active", "table": "navigation", "data_type": "boolean", "default_value": true, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "navigation", "field": "is_active", "special": ["cast-boolean"], "interface": "boolean", "options": {"label": "Active"}, "display": "boolean", "display_options": {"labelOn": "Active", "labelOff": "Inactive"}, "readonly": false, "hidden": false, "sort": 8, "width": "full", "translations": [{"language": "en-US", "translation": "Active"}], "note": "Show or hide this menu from the site.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "navigation", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "navigation", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 1, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "navigation", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "navigation", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 2, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "navigation", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "navigation", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "navigation", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "navigation", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation_items", "field": "id", "type": "uuid", "schema": {"name": "id", "table": "navigation_items", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "navigation_items", "field": "id", "special": ["uuid"], "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation_items", "field": "navigation", "type": "string", "schema": {"name": "navigation", "table": "navigation_items", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "navigation", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "navigation_items", "field": "navigation", "special": null, "interface": "select-dropdown-m2o", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 7, "width": "full", "translations": null, "note": "Navigation menu that the individual links belong to.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation_items", "field": "page", "type": "uuid", "schema": {"name": "page", "table": "navigation_items", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "pages", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "navigation_items", "field": "page", "special": ["m2o"], "interface": "select-dropdown-m2o", "options": {"template": "{{title}}"}, "display": "related-values", "display_options": {"template": "{{title}}"}, "readonly": false, "hidden": true, "sort": 10, "width": "full", "translations": null, "note": "The internal page to link to.", "conditions": [{"name": "IF page", "rule": {"_and": [{"type": {"_eq": "page"}}]}, "options": {"enableCreate": true, "enableSelect": true}, "hidden": false}, {"name": "Hide If Has Children", "rule": {"_and": [{"has_children": {"_eq": true}}]}, "hidden": true, "options": {"enableCreate": true, "enableSelect": true}}], "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation_items", "field": "parent", "type": "uuid", "schema": {"name": "parent", "table": "navigation_items", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "navigation_items", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "navigation_items", "field": "parent", "special": null, "interface": "select-dropdown-m2o", "options": {"template": "{{title}}"}, "display": "related-values", "display_options": {"template": "{{title}}"}, "readonly": false, "hidden": true, "sort": 14, "width": "full", "translations": null, "note": "The parent navigation item.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation_items", "field": "sort", "type": "integer", "schema": {"name": "sort", "table": "navigation_items", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "navigation_items", "field": "sort", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 2, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation_items", "field": "title", "type": "string", "schema": {"name": "title", "table": "navigation_items", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "navigation_items", "field": "title", "special": null, "interface": "input", "options": null, "display": "raw", "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "half", "translations": null, "note": "Label shown to the user for the menu item.", "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation_items", "field": "type", "type": "string", "schema": {"name": "type", "table": "navigation_items", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "navigation_items", "field": "type", "special": null, "interface": "select-dropdown", "options": {"choices": [{"text": "Page", "value": "page", "icon": "web_asset"}, {"text": "Post", "value": "post", "icon": "article"}, {"text": "URL", "value": "url", "icon": "link"}, {"text": "Group", "value": "group", "icon": "tab_group"}]}, "display": "labels", "display_options": {"choices": [{"text": "Page", "value": "page", "icon": "web_asset"}, {"text": "Post", "value": "post", "icon": "article"}, {"text": "URL", "value": "url", "icon": "link"}, {"text": "Group", "value": "group", "icon": "tab_group"}]}, "readonly": false, "hidden": false, "sort": 9, "width": "half", "translations": null, "note": "What type of link is this? Page and Post allow you to link to internal content. URL is for external content. Group can contain other menu items.", "conditions": [{"name": "Hide if Children", "rule": {"_and": [{"has_children": {"_eq": true}}]}, "hidden": true, "options": {"iconOn": "radio_button_checked", "iconOff": "radio_button_unchecked", "allowOther": false}}], "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation_items", "field": "url", "type": "string", "schema": {"name": "url", "table": "navigation_items", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "navigation_items", "field": "url", "special": null, "interface": "input", "options": null, "display": "raw", "display_options": null, "readonly": false, "hidden": true, "sort": 12, "width": "full", "translations": null, "note": "The URL to link to. Could be relative (ie `/my-page`) or a full external URL (ie `https://docs.directus.io`)", "conditions": [{"name": "IF url", "rule": {"_and": [{"type": {"_eq": "url"}}]}, "options": {"font": "sans-serif", "trim": false, "masked": false, "clear": false, "slug": false}, "hidden": false}, {"name": "Hide If Had Children", "rule": {"_and": [{"has_children": {"_eq": true}}]}, "hidden": true, "options": {"font": "sans-serif", "trim": false, "masked": false, "clear": false, "slug": false}}], "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation_items", "field": "post", "type": "uuid", "schema": {"name": "post", "table": "navigation_items", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "posts", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "navigation_items", "field": "post", "special": ["m2o"], "interface": "select-dropdown-m2o", "options": {"template": "{{title}}"}, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 11, "width": "full", "translations": null, "note": "The internal post to link to.", "conditions": [{"name": "Show If Type = Post", "rule": {"_and": [{"type": {"_eq": "post"}}]}, "hidden": false, "options": {"enableCreate": true, "enableSelect": true}}], "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation_items", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "navigation_items", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "navigation_items", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation_items", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "navigation_items", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "navigation_items", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation_items", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "navigation_items", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "navigation_items", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation_items", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "navigation_items", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "navigation_items", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 6, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "page_blocks", "field": "id", "type": "uuid", "schema": {"name": "id", "table": "page_blocks", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "page_blocks", "field": "id", "special": ["uuid"], "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "page_blocks", "field": "sort", "type": "integer", "schema": {"name": "sort", "table": "page_blocks", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "page_blocks", "field": "sort", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 2, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "page_blocks", "field": "page", "type": "uuid", "schema": {"name": "page", "table": "page_blocks", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "pages", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "page_blocks", "field": "page", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 7, "width": "full", "translations": null, "note": "The id of the page that this block belongs to.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "page_blocks", "field": "item", "type": "string", "schema": {"name": "item", "table": "page_blocks", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "page_blocks", "field": "item", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 8, "width": "full", "translations": null, "note": "The data for the block.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "page_blocks", "field": "collection", "type": "string", "schema": {"name": "collection", "table": "page_blocks", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "page_blocks", "field": "collection", "special": null, "interface": null, "options": null, "display": "labels", "display_options": {"format": true, "color": "#18222F", "border": true}, "readonly": false, "hidden": true, "sort": 9, "width": "full", "translations": null, "note": "The collection (type of block).", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "page_blocks", "field": "hide_block", "type": "boolean", "schema": {"name": "hide_block", "table": "page_blocks", "data_type": "boolean", "default_value": false, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "page_blocks", "field": "hide_block", "special": ["cast-boolean"], "interface": "boolean", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 10, "width": "half", "translations": null, "note": "Temporarily hide this block on the website without having to remove it from your page.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "page_blocks", "field": "background", "type": "string", "schema": {"name": "background", "table": "page_blocks", "data_type": "character varying", "default_value": "light", "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "page_blocks", "field": "background", "special": null, "interface": "select-dropdown", "options": {"choices": [{"text": "<PERSON><PERSON><PERSON>", "value": "light", "icon": "light_mode"}, {"text": "Dark", "value": "dark", "icon": "dark_mode"}]}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 11, "width": "half", "translations": null, "note": "Background color for the block to create contrast. Does not control dark or light mode for the entire site.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "page_blocks", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "page_blocks", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "page_blocks", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "page_blocks", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "page_blocks", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "page_blocks", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "page_blocks", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "page_blocks", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "page_blocks", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "page_blocks", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "page_blocks", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "page_blocks", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 6, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "pages", "field": "id", "type": "uuid", "schema": {"name": "id", "table": "pages", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "pages", "field": "id", "special": ["uuid"], "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "pages", "field": "sort", "type": "integer", "schema": {"name": "sort", "table": "pages", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "pages", "field": "sort", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 2, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "pages", "field": "title", "type": "string", "schema": {"name": "title", "table": "pages", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "pages", "field": "title", "special": null, "interface": "input", "options": {"placeholder": "About Us"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 1, "width": "half", "translations": null, "note": "The title of this page.", "conditions": null, "required": true, "group": "meta_content", "validation": null, "validation_message": null}}, {"collection": "pages", "field": "permalink", "type": "string", "schema": {"name": "permalink", "table": "pages", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": true, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "pages", "field": "permalink", "special": null, "interface": "input", "options": {"font": "monospace", "trim": true, "placeholder": "/about-us"}, "display": "formatted-value", "display_options": {"font": "monospace"}, "readonly": false, "hidden": false, "sort": 2, "width": "half", "translations": null, "note": "Unique URL for this page (start with `/`, can have multiple segments `/about/me`)).", "conditions": null, "required": true, "group": "meta_content", "validation": {"_and": [{"permalink": {"_regex": "^/(?:[a-z0-9]+(?:-[a-z0-9]+)*(?:/[a-z0-9]+(?:-[a-z0-9]+)*)*)?$"}}]}, "validation_message": "Please use lowercase letters, numbers, and hyphens in your permalink, starting with a slash (/) and without a trailing slash"}}, {"collection": "pages", "field": "status", "type": "string", "schema": {"name": "status", "table": "pages", "data_type": "character varying", "default_value": "draft", "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "pages", "field": "status", "special": null, "interface": "select-dropdown", "options": {"choices": [{"text": "$t:draft", "value": "draft", "icon": "draft_orders", "color": "#A2B5CD"}, {"text": "In Review", "value": "in_review", "icon": "rate_review", "color": "#FFA439"}, {"text": "$t:published", "value": "published", "icon": "check", "color": "#2ECDA7"}]}, "display": "labels", "display_options": {"choices": [{"text": "$t:draft", "value": "draft", "icon": "draft_orders", "color": "#A2B5CD"}, {"text": "In Review", "value": "in_review", "icon": "rate_review", "color": "#FFA439"}, {"text": "$t:published", "value": "published", "icon": "check", "color": "#2ECDA7"}]}, "readonly": false, "hidden": false, "sort": 3, "width": "half", "translations": null, "note": "Is this page published?", "conditions": null, "required": false, "group": "meta_content", "validation": null, "validation_message": null}}, {"collection": "pages", "field": "published_at", "type": "timestamp", "schema": {"name": "published_at", "table": "pages", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "pages", "field": "published_at", "special": null, "interface": "datetime", "options": {"use24": false}, "display": "datetime", "display_options": {"format": "short", "relative": true}, "readonly": false, "hidden": false, "sort": 4, "width": "half", "translations": null, "note": "Publish now or schedule for later.", "conditions": [{"name": "Show If Status = Published", "rule": {"_and": [{"status": {"_eq": "published"}}]}, "hidden": false, "options": {"includeSeconds": false, "use24": true}}], "required": false, "group": "meta_content", "validation": null, "validation_message": null}}, {"collection": "pages", "field": "seo", "type": "json", "schema": {"name": "seo", "table": "pages", "data_type": "json", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "pages", "field": "seo", "special": ["cast-json"], "interface": "seo-interface", "options": {"titleTemplate": "{{title}}", "showOgImage": true}, "display": "seo-display", "display_options": {"showSearchPreview": true}, "readonly": false, "hidden": false, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": "meta_seo", "validation": null, "validation_message": null}}, {"collection": "pages", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "pages", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "pages", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "pages", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "pages", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "pages", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "pages", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "pages", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "pages", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "pages", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "pages", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "pages", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 6, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "posts", "field": "content", "type": "text", "schema": {"name": "content", "table": "posts", "data_type": "text", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "posts", "field": "content", "special": null, "interface": "input-rich-text-html", "options": {"toolbar": ["blockquote", "bold", "bullist", "code", "customImage", "customLink", "customMedia", "fullscreen", "h1", "h2", "h3", "hr", "italic", "numlist", "redo", "removeformat", "underline", "undo"], "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9"}, "display": "formatted-value", "display_options": {"format": true}, "readonly": false, "hidden": false, "sort": 11, "width": "full", "translations": null, "note": "Rich text content of your blog post.", "conditions": null, "required": false, "group": "meta_content", "validation": null, "validation_message": null}}, {"collection": "posts", "field": "id", "type": "uuid", "schema": {"name": "id", "table": "posts", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "posts", "field": "id", "special": ["uuid"], "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "posts", "field": "image", "type": "uuid", "schema": {"name": "image", "table": "posts", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_files", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "posts", "field": "image", "special": ["file"], "interface": "file-image", "options": {"crop": false, "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9"}, "display": "image", "display_options": null, "readonly": false, "hidden": false, "sort": 9, "width": "full", "translations": null, "note": "Featured image for this post. Used in cards linking to the post and in the post detail page.", "conditions": null, "required": false, "group": "meta_content", "validation": null, "validation_message": null}}, {"collection": "posts", "field": "slug", "type": "string", "schema": {"name": "slug", "table": "posts", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "posts", "field": "slug", "special": null, "interface": "extension-wpslug", "options": {"font": "monospace", "template": "{{title}}", "placeholder": null}, "display": "formatted-value", "display_options": {"font": "monospace"}, "readonly": false, "hidden": false, "sort": 2, "width": "half", "translations": null, "note": "Unique URL for this post (e.g., `yoursite.com/posts/{{your-slug}}`)", "conditions": null, "required": false, "group": "meta_content", "validation": null, "validation_message": null}}, {"collection": "posts", "field": "sort", "type": "integer", "schema": {"name": "sort", "table": "posts", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "posts", "field": "sort", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 2, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "posts", "field": "status", "type": "string", "schema": {"name": "status", "table": "posts", "data_type": "character varying", "default_value": "draft", "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "posts", "field": "status", "special": null, "interface": "select-dropdown", "options": {"choices": [{"text": "$t:draft", "value": "draft", "icon": "draft_orders", "color": "#A2B5CD"}, {"text": "In Review", "value": "in_review", "icon": "rate_review", "color": "#FFA439"}, {"text": "$t:published", "value": "published", "icon": "check", "color": "#2ECDA7"}]}, "display": "labels", "display_options": {"choices": [{"text": "$t:draft", "value": "draft", "icon": "draft_orders", "color": "#A2B5CD"}, {"text": "In Review", "value": "in_review", "icon": "rate_review", "color": "#FFA439"}, {"text": "$t:published", "value": "published", "icon": "check", "color": "#2ECDA7"}]}, "readonly": false, "hidden": false, "sort": 4, "width": "half", "translations": null, "note": "Is this post published?", "conditions": null, "required": false, "group": "meta_content", "validation": null, "validation_message": null}}, {"collection": "posts", "field": "title", "type": "string", "schema": {"name": "title", "table": "posts", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "posts", "field": "title", "special": null, "interface": "input", "options": {"placeholder": "Essential tips for first-time home buyers"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 1, "width": "half", "translations": null, "note": "Title of the blog post (used in page title and meta tags)", "conditions": null, "required": true, "group": "meta_content", "validation": null, "validation_message": null}}, {"collection": "posts", "field": "description", "type": "text", "schema": {"name": "description", "table": "posts", "data_type": "text", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "posts", "field": "description", "special": null, "interface": "input-multiline", "options": {"placeholder": "Discover key strategies for navigating the home buying process, from budgeting to closing. Learn how to avoid common pitfalls and make informed decisions."}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 6, "width": "half", "translations": null, "note": "Short summary of the blog post to entice readers.", "conditions": null, "required": false, "group": "meta_content", "validation": null, "validation_message": null}}, {"collection": "posts", "field": "author", "type": "uuid", "schema": {"name": "author", "table": "posts", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "posts", "field": "author", "special": ["m2o"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar.$thumbnail}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": {"circle": true}, "readonly": false, "hidden": false, "sort": 7, "width": "half", "translations": null, "note": "Select the team member who wrote this post", "conditions": null, "required": false, "group": "meta_content", "validation": null, "validation_message": null}}, {"collection": "posts", "field": "published_at", "type": "timestamp", "schema": {"name": "published_at", "table": "posts", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "posts", "field": "published_at", "special": null, "interface": "datetime", "options": {"use24": false, "relative": true}, "display": "datetime", "display_options": {"format": "short", "relative": true}, "readonly": false, "hidden": false, "sort": 5, "width": "half", "translations": null, "note": "Publish now or schedule for later.", "conditions": [{"name": "Show If Status = Published", "rule": {"_and": [{"status": {"_eq": "published"}}]}, "hidden": false, "options": {"includeSeconds": false, "use24": true}}], "required": false, "group": "meta_content", "validation": null, "validation_message": null}}, {"collection": "posts", "field": "seo", "type": "json", "schema": {"name": "seo", "table": "posts", "data_type": "json", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "posts", "field": "seo", "special": ["cast-json"], "interface": "seo-interface", "options": {"titleTemplate": "{{title}}", "descriptionTemplate": "{{description}}", "additionalFields": null, "showOgImage": true}, "display": "seo-display", "display_options": {"showSearchPreview": true}, "readonly": false, "hidden": false, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": "meta_seo", "validation": null, "validation_message": null}}, {"collection": "posts", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "posts", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "posts", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "posts", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "posts", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "posts", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "posts", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "posts", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "posts", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "posts", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "posts", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "posts", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 6, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "redirects", "field": "id", "type": "uuid", "schema": {"name": "id", "table": "redirects", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "redirects", "field": "id", "special": ["uuid"], "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "redirects", "field": "response_code", "type": "string", "schema": {"name": "response_code", "table": "redirects", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "redirects", "field": "response_code", "special": null, "interface": "radio-cards-interface", "options": {"choices": [{"text": "Permanent (301)", "value": "301", "icon_type": "icon", "description": "<PERSON> has permanently moved to the new URL.", "icon": "sync_lock"}, {"text": "Temporary (302)", "value": "302", "icon_type": "icon", "description": "Temporarily redirect traffic to the page to the new URL.", "icon": "build_circle"}], "gridSize": 2}, "display": "labels", "display_options": {"conditionalFormatting": null, "choices": [{"text": "Permanent (301)", "value": "301", "icon_type": "icon", "description": "<PERSON> has permanently moved to the new URL.", "icon": "sync_lock"}, {"text": "Temporary (302)", "value": "302", "icon_type": "icon", "description": "Temporarily redirect traffic to the page to the new URL.", "icon": "build_circle"}]}, "readonly": false, "hidden": false, "sort": 9, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "redirects", "field": "url_from", "type": "string", "schema": {"name": "url_from", "table": "redirects", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "redirects", "field": "url_from", "special": null, "interface": "input", "options": {"iconLeft": "link", "font": "monospace"}, "display": "formatted-value", "display_options": {"font": "monospace"}, "readonly": false, "hidden": false, "sort": 7, "width": "full", "translations": [{"language": "en-US", "translation": "Original URL"}], "note": "Old URL has to be relative to the site (ie `/blog` or `/news`). It cannot be a full url like (https://example.com/blog)", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "redirects", "field": "url_to", "type": "string", "schema": {"name": "url_to", "table": "redirects", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "redirects", "field": "url_to", "special": null, "interface": "input", "options": {"iconLeft": "add_link", "font": "monospace"}, "display": "formatted-value", "display_options": {"font": "monospace"}, "readonly": false, "hidden": false, "sort": 8, "width": "full", "translations": [{"language": "en-US", "translation": "Redirect To"}], "note": "The URL you're redirecting to. This can be a relative url (/resources/matt-is-cool) or a full url (https://example.com/blog).", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "redirects", "field": "note", "type": "text", "schema": {"name": "note", "table": "redirects", "data_type": "text", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "redirects", "field": "note", "special": null, "interface": "input-multiline", "options": {"placeholder": null}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 10, "width": "half", "translations": null, "note": "Short explanation of why the redirect was created.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "redirects", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "redirects", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "redirects", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 2, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "redirects", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "redirects", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "redirects", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "redirects", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "redirects", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "redirects", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "redirects", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "redirects", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "redirects", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing", "field": "pricing_cards", "type": "alias", "schema": null, "meta": {"collection": "block_pricing", "field": "pricing_cards", "special": ["o2m"], "interface": "list-o2m", "options": {"template": "{{title}} • {{price}}"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 9, "width": "full", "translations": null, "note": "The individual pricing cards to display.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "pages", "field": "meta_divider_content", "type": "alias", "schema": null, "meta": {"collection": "pages", "field": "meta_divider_content", "special": ["alias", "no-data"], "interface": "presentation-divider", "options": {"icon": "format_paragraph", "color": "#A2B5CD", "inlineTitle": true}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 5, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": "meta_content", "validation": null, "validation_message": null}}, {"collection": "globals", "field": "divider_logo", "type": "alias", "schema": null, "meta": {"collection": "globals", "field": "divider_logo", "special": ["alias", "no-data"], "interface": "presentation-divider", "options": {"color": "#A2B5CD", "icon": "imagesmode", "inlineTitle": true}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 10, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_button_group", "field": "buttons", "type": "alias", "schema": null, "meta": {"collection": "block_button_group", "field": "buttons", "special": ["o2m"], "interface": "list-o2m", "options": {"enableLink": true, "template": "{{label}} - {{type}}"}, "display": "related-values", "display_options": {"template": "{{label}} - {{type}}"}, "readonly": false, "hidden": false, "sort": 7, "width": "full", "translations": null, "note": "Add individual buttons to the button group.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "globals", "field": "meta_credentials", "type": "alias", "schema": null, "meta": {"collection": "globals", "field": "meta_credentials", "special": ["alias", "no-data", "group"], "interface": "group-detail", "options": {"headerIcon": "warning", "start": "closed"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 19, "width": "full", "translations": [{"language": "en-US", "translation": "Credentials"}], "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "forms", "field": "fields", "type": "alias", "schema": null, "meta": {"collection": "forms", "field": "fields", "special": ["o2m"], "interface": "list-o2m", "options": {"template": "{{name}} • {{type}}", "enableSelect": false, "sort": "sort"}, "display": "related-values", "display_options": {"template": "{{name}} • {{type}}"}, "readonly": false, "hidden": false, "sort": 2, "width": "full", "translations": null, "note": "Form structure and input fields", "conditions": null, "required": false, "group": "meta_fields", "validation": null, "validation_message": null}}, {"collection": "globals", "field": "meta_divider_globals", "type": "alias", "schema": null, "meta": {"collection": "globals", "field": "meta_divider_globals", "special": ["alias", "no-data"], "interface": "presentation-divider", "options": {"inlineTitle": true, "icon": "link", "title": null, "color": "#A2B5CD"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 17, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_submissions", "field": "values", "type": "alias", "schema": null, "meta": {"collection": "form_submissions", "field": "values", "special": ["o2m"], "interface": "list-o2m", "options": {"fields": ["field.label", "value", "field.type", "file"], "enableSearchFilter": true, "sort": "sort", "template": "{{value}} • {{field}}", "filter": {"_and": [{"field": {"form": {"_eq": "{{form}}"}}}]}, "layout": "table", "enableSelect": false, "enableCreate": false, "limit": 25}, "display": "related-values", "display_options": {"template": "{{value}} • {{field}}"}, "readonly": true, "hidden": false, "sort": 5, "width": "full", "translations": null, "note": "Submitted field responses", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "posts", "field": "meta_divider_info", "type": "alias", "schema": null, "meta": {"collection": "posts", "field": "meta_divider_info", "special": ["alias", "no-data"], "interface": "presentation-divider", "options": {"icon": "calendar_month", "color": "#A2B5CD", "inlineTitle": true}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 3, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": "meta_content", "validation": null, "validation_message": null}}, {"collection": "pages", "field": "meta_m2a_button", "type": "alias", "schema": null, "meta": {"collection": "pages", "field": "meta_m2a_button", "special": ["alias", "no-data"], "interface": "directus-labs-experimental-m2a-interface", "options": {"target": "above"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": "meta_content", "validation": null, "validation_message": null}}, {"collection": "pages", "field": "blocks", "type": "alias", "schema": null, "meta": {"collection": "pages", "field": "blocks", "special": ["m2a"], "interface": "list-m2a", "options": {"prefix": null, "enableSelect": false}, "display": "related-values", "display_options": {"template": "{{collection}}"}, "readonly": false, "hidden": false, "sort": 7, "width": "full", "translations": null, "note": "Create and arrange different content blocks (like text, images, or videos) to build your page.", "conditions": null, "required": false, "group": "meta_content", "validation": null, "validation_message": null}}, {"collection": "forms", "field": "submissions", "type": "alias", "schema": null, "meta": {"collection": "forms", "field": "submissions", "special": ["o2m"], "interface": "list-o2m", "options": {"layout": "table", "fields": ["timestamp", "values.value", "values.field.name"], "sort": "timestamp", "enableCreate": false, "enableSelect": false, "limit": 25, "enableSearchFilter": true, "enableLink": true}, "display": "related-values", "display_options": {"template": "{{values.value}}"}, "readonly": true, "hidden": false, "sort": 2, "width": "fill", "translations": null, "note": "Received form responses.", "conditions": null, "required": false, "group": "meta_submissions", "validation": null, "validation_message": null}}, {"collection": "forms", "field": "meta_fields", "type": "alias", "schema": null, "meta": {"collection": "forms", "field": "meta_fields", "special": ["alias", "no-data", "group"], "interface": "group-raw", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 1, "width": "full", "translations": [{"language": "en-US", "translation": "Form Fields"}], "note": null, "conditions": null, "required": false, "group": "meta_tabs", "validation": null, "validation_message": null}}, {"collection": "forms", "field": "meta_tabs", "type": "alias", "schema": null, "meta": {"collection": "forms", "field": "meta_tabs", "special": ["alias", "no-data", "group"], "interface": "group-tabs", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 10, "width": "full", "translations": [{"language": "en-US", "translation": "Tabs"}], "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "pages", "field": "meta_notice_pagebuilder", "type": "alias", "schema": null, "meta": {"collection": "pages", "field": "meta_notice_pagebuilder", "special": ["alias", "no-data"], "interface": "presentation-notice", "options": {"text": "Build dynamic pages quickly using ready-made blocks. <a href=\"https://directus.io/docs/tutorials/getting-started/create-reusable-blocks-with-many-to-any-relationships?ref=simple_cms_notices\" target=\"_blank\">See our documentation to learn more about the Page Builder</a>."}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 6, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": "meta_content", "validation": null, "validation_message": null}}, {"collection": "block_hero", "field": "meta_divider_block_image", "type": "alias", "schema": null, "meta": {"collection": "block_hero", "field": "meta_divider_block_image", "special": ["alias", "no-data"], "interface": "presentation-divider", "options": {"inlineTitle": true, "icon": "image", "color": "#A2B5CD"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 11, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "globals", "field": "meta_notice_globals", "type": "alias", "schema": null, "meta": {"collection": "globals", "field": "meta_notice_globals", "special": ["alias", "no-data"], "interface": "presentation-notice", "options": {"text": "Globals are settings that are applied across your entire site. Globals use what we call a `singleton` collection type. <a href=\"https://directus.io/features/global-settings\" target=\"_blank\">Learn more about globals and singletons.</a>"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_gallery", "field": "items", "type": "alias", "schema": null, "meta": {"collection": "block_gallery", "field": "items", "special": ["files"], "interface": "files", "options": {"folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 9, "width": "full", "translations": null, "note": "Images to include in the image gallery.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation_items", "field": "children", "type": "alias", "schema": null, "meta": {"collection": "navigation_items", "field": "children", "special": ["o2m"], "interface": "list-o2m", "options": {"template": "{{title}} • {{type}}", "filter": {"_and": [{"_and": [{"navigation": {"_null": true}}, {"parent": {"navigation": {"_null": true}}}]}]}}, "display": "related-values", "display_options": {"template": "{{title}} • {{type}}"}, "readonly": false, "hidden": true, "sort": 13, "width": "full", "translations": null, "note": "Add child menu items within the group.", "conditions": [{"name": "Show If Group", "rule": {"_and": [{"type": {"_eq": "group"}}]}, "hidden": false, "options": {"layout": "list", "enableCreate": true, "enableSelect": true, "limit": 15, "sortDirection": "+", "enableSearchFilter": false, "enableLink": false}}], "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "forms", "field": "meta_notice_form_fields", "type": "alias", "schema": null, "meta": {"collection": "forms", "field": "meta_notice_form_fields", "special": ["alias", "no-data"], "interface": "presentation-notice", "options": {"text": " Create custom forms by adding and configuring your desired input fields below. No coding required."}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": "meta_fields", "validation": null, "validation_message": null}}, {"collection": "forms", "field": "meta_notice_form_emails", "type": "alias", "schema": null, "meta": {"collection": "forms", "field": "meta_notice_form_emails", "special": ["alias", "no-data"], "interface": "presentation-notice", "options": {"text": "Set up automatic emails to notify your team members when forms are submitted, or send confirmation receipts to people who complete your forms."}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": "meta_emails", "validation": null, "validation_message": null}}, {"collection": "forms", "field": "meta_emails", "type": "alias", "schema": null, "meta": {"collection": "forms", "field": "meta_emails", "special": ["alias", "no-data", "group"], "interface": "group-raw", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 3, "width": "full", "translations": [{"language": "en-US", "translation": "Emails"}], "note": null, "conditions": null, "required": false, "group": "meta_tabs", "validation": null, "validation_message": null}}, {"collection": "forms", "field": "meta_notice_form_responses", "type": "alias", "schema": null, "meta": {"collection": "forms", "field": "meta_notice_form_responses", "special": ["alias", "no-data"], "interface": "presentation-notice", "options": {"text": "This table displays all responses submitted through this form. Each entry includes the submission timestamp and the values for each form field."}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": "meta_submissions", "validation": null, "validation_message": null}}, {"collection": "globals", "field": "meta_notice_security", "type": "alias", "schema": null, "meta": {"collection": "globals", "field": "meta_notice_security", "special": ["alias", "no-data"], "interface": "presentation-notice", "options": {"color": "warning", "icon": "warning", "text": "Be careful about changing the access policies and permissions for the `globals` collection so that you don't expose your private API keys."}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": "meta_credentials", "validation": null, "validation_message": null}}, {"collection": "forms", "field": "meta_submissions", "type": "alias", "schema": null, "meta": {"collection": "forms", "field": "meta_submissions", "special": ["alias", "no-data", "group"], "interface": "group-raw", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 2, "width": "full", "translations": [{"language": "en-US", "translation": "Submissions"}], "note": null, "conditions": null, "required": false, "group": "meta_tabs", "validation": null, "validation_message": null}}, {"collection": "ai_prompts", "field": "meta_header_ai_prompts", "type": "alias", "schema": null, "meta": {"collection": "ai_prompts", "field": "meta_header_ai_prompts", "special": ["alias", "no-data"], "interface": "super-header", "options": {"title": "{{name}}", "subtitle": "{{status}} • Last Updated: {{updated_on}}", "help": "<h2>AI Prompts Collection Help</h2>\n<h3>Name</h3>\n<p><em>Unique identifier for this prompt</em></p>\n<p>Enter a clear, concise name that describes what this prompt does. Use kebab-case (lowercase with hyphens) for consistency, like \"create-article\" or \"generate-product-description\".</p>\n<h3>Description</h3>\n<p><em>Short description for the prompt to display to users</em></p>\n<p>Briefly explain what this prompt does in 1-2 sentences. Focus on the outcome users can expect when using this prompt. Keep it clear and action-oriented.</p>\n<h3>System Prompt</h3>\n<p><em>Instructions that shape how the AI responds</em></p>\n<p>This is the foundation of your AI prompt. Define the AI's role, capabilities, and constraints here. Be specific about tone, style, and approach. This text isn't visible to end users but significantly impacts output quality.</p>\n<h3>Messages</h3>\n<p><em>Define the conversation structure between users and AI</em></p>\n<h4>Role</h4>\n<p><em>Who is speaking in this message</em></p>\n<p>Select \"User\" for instructions from the human, or \"Assistant\" for example responses from the AI. Most prompt templates begin with a user message.</p>\n<h4>Text</h4>\n<p><em>The actual content of the message</em></p>\n<p>For user messages, include placeholders with double curly braces {{like this}} that will be replaced with real data. For assistant messages, provide example responses that model the desired output format.</p>\n<h3>Status</h3>\n<p><em>Is this prompt published and available to use?</em></p>\n<p>Toggle between draft and published states. Only published prompts are available to users in the content creation interface.</p>\n<h3>Creating Effective AI Prompts</h3>\n<p>Good prompts are clear, specific, and structured. Here are quick tips:</p>\n<ol>\n<li><strong>Be specific about deliverables</strong> - Define exactly what output you want (word count, format, sections).</li>\n<li><strong>Establish context</strong> - Provide relevant background information the AI needs to understand the task.</li>\n<li><strong>Define the audience</strong> - Specify who will read the content and their knowledge level.</li>\n<li><strong>Use placeholders consistently</strong> - Format placeholders with double curly braces {{like this}} for variables.</li>\n<li><strong>Include examples</strong> - Show what good outputs look like to guide the AI's response format.</li>\n<li><strong>Balance guidance with flexibility</strong> - Provide enough direction without over-constraining the AI's ability to generate creative content.</li>\n<li><strong>Test and iterate</strong> - Create your prompt, test the output, and refine based on results.</li>\n</ol>\n<p>Remember: The best prompts clearly communicate what you want while giving the AI enough information to generate high-quality, relevant content.</p>"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "directus_users", "field": "posts", "type": "alias", "schema": null, "meta": {"collection": "directus_users", "field": "posts", "special": ["o2m"], "interface": "list-o2m", "options": {"template": "{{title}}"}, "display": "related-values", "display_options": null, "readonly": false, "hidden": false, "sort": 1, "width": "full", "translations": null, "note": "Blog posts this user has authored.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation", "field": "meta_notice_navigation", "type": "alias", "schema": null, "meta": {"collection": "navigation", "field": "meta_notice_navigation", "special": ["alias", "no-data"], "interface": "presentation-notice", "options": {"text": "Create and manage navigation menus for your website. Each menu can contain multiple links organized into a hierarchy."}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 9, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation", "field": "items", "type": "alias", "schema": null, "meta": {"collection": "navigation", "field": "items", "special": ["o2m"], "interface": "list-o2m", "options": {"enableLink": true, "filter": {"_and": [{"_and": [{"navigation": {"_null": true}}, {"parent": {"navigation": {"_null": true}}}]}]}, "template": "{{title}} • {{type}}"}, "display": "related-values", "display_options": {"template": "{{title}} • {{type}}"}, "readonly": false, "hidden": false, "sort": 10, "width": "full", "translations": null, "note": "Links within the menu.", "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_submissions", "field": "meta_notice_submissions", "type": "alias", "schema": null, "meta": {"collection": "form_submissions", "field": "meta_notice_submissions", "special": ["alias", "no-data"], "interface": "presentation-notice", "options": {"text": "Form submissions are configured to be read-only for data integrity reasons. But they are a great candidate for use in [Flows](/admin/settings/flows). Directus Flows is a drag and drop, low-code automation builder to simplify tasks like automatic notifications or sending incoming requests to third party services. <a href=\"https://directus.io/docs/guides/automate/flows?ref=simple_website_cms\" target=\"_blank\">Learn more about Flows.</a>"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 6, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "posts", "field": "meta_header_image", "type": "alias", "schema": null, "meta": {"collection": "posts", "field": "meta_header_image", "special": ["alias", "no-data"], "interface": "super-header", "options": {"title": "Image", "icon": "image", "help": "<p><strong>Need inspiration?</strong> Use Flows like AI Image Generator to generate on-brand images for your post using AI.</p>\n<p><em>Note: This uses OpenAI so you'll need to add your API key in the&nbsp;<a href=\"/admin/content/globals\">Globals</a> collection first.</em></p>", "actions": [{"label": "AI Image Generator", "icon": "image", "type": "normal", "actionType": "flow", "flow": {"key": "d4bbac48-a444-49e0-aedb-9af5273b88df", "collection": "directus_flows"}}]}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": "meta_content", "validation": null, "validation_message": null}}, {"collection": "block_form", "field": "meta_header_block_form", "type": "alias", "schema": null, "meta": {"collection": "block_form", "field": "meta_header_block_form", "special": ["alias", "no-data"], "interface": "super-header", "options": {"title": "Form Block", "help": "<p>Embed interactive forms to collect information from your visitors. Great for:</p>\n<ul>\n<li>Contact forms</li>\n<li>Newsletter signups</li>\n<li>Event registrations</li>\n<li>Feedback collection</li>\n<li>Lead generation</li>\n</ul>\n<p>When adding a form:</p>\n<ul>\n<li>Choose from your pre-configured form templates</li>\n<li>Add a clear headline explaining the form's purpose</li>\n<li>Consider adding a tagline with extra context or instructions</li>\n<li>Ensure all required fields are clearly marked</li>\n</ul>\n<p>You can create or edit forms outside of this block in the <a href=\"/admin/content/forms\">Forms collection.</a></p>", "icon": "format_shapes"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 6, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_gallery", "field": "meta_header_block_gallery", "type": "alias", "schema": null, "meta": {"collection": "block_gallery", "field": "meta_header_block_gallery", "special": ["alias", "no-data"], "interface": "super-header", "options": {"title": "Gallery Block", "icon": "grid_view", "help": "<p>Create visual showcases with multiple images. Ideal for:</p>\n<ul>\n<li>Product portfolios</li>\n<li>Project showcases</li>\n<li>Event photo collections</li>\n<li>Team member galleries</li>\n<li>Before/after demonstrations</li>\n</ul>\n<p>Tips for great galleries:</p>\n<ul>\n<li>Use consistently sized images for the best presentation</li>\n<li>Add descriptive headlines to provide context</li>\n<li>Consider a tagline to explain what visitors are seeing</li>\n<li>Organize images in a logical sequence</li>\n</ul>"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 6, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_hero", "field": "meta_header_block_hero", "type": "alias", "schema": null, "meta": {"collection": "block_hero", "field": "meta_header_block_hero", "special": ["alias", "no-data"], "interface": "super-header", "options": {"title": "Hero Block", "icon": "aspect_ratio", "help": "<p>Creates a prominent section at the top of your page that immediately captures attention. Ideal for:</p>\n<ul>\n<li>Main page headers with impactful headlines</li>\n<li>Showcasing key messages with supporting text</li>\n<li>Featuring high-quality images that support your message</li>\n<li>Adding clear call-to-action buttons</li>\n</ul>\n<p>Tips for effective heroes:</p>\n<ul>\n<li>Keep headlines clear and concise (recommended 2-8 words)</li>\n<li>Use high-resolution images that work well with overlay text</li>\n<li>Consider image placement (left/center/right) based on your headline length</li>\n<li>Add a succinct tagline to provide additional context</li>\n</ul>"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 6, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_posts", "field": "meta_header_block_posts", "type": "alias", "schema": null, "meta": {"collection": "block_posts", "field": "meta_header_block_posts", "special": ["alias", "no-data"], "interface": "super-header", "options": {"title": "Posts Block", "help": "<p>Automatically displays your most recent blog posts in chronological order. Perfect for:</p>\n<ul>\n<li>Showing latest articles on your homepage</li>\n<li>Keeping your content fresh and up-to-date</li>\n<li>Creating dynamic content sections</li>\n<li>Showcasing your recent writing</li>\n</ul>\n<p>You can:</p>\n<ul>\n<li>Set the number of posts to display</li>\n<li>Add a custom headline and tagline to label the section</li>\n<li>Trust the system to automatically keep your content current</li>\n</ul>\n<p>The posts will automatically update as you publish new content, ensuring your pages always show your latest articles.</p>", "icon": "signpost"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 6, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "form_submissions", "field": "meta_header_form_submissions", "type": "alias", "schema": null, "meta": {"collection": "form_submissions", "field": "meta_header_form_submissions", "special": ["alias", "no-data"], "interface": "super-header", "options": {"title": "Form Submission", "subtitle": "Submitted at: {{timestamp}}", "help": "<h2>Form Submissions</h2>\n<p>Form submissions are read-only records of responses received through your site's forms. This ensures data integrity and provides a reliable audit trail of all submitted information.</p>\n<h3>Understanding Submissions</h3>\n<p>Each submission contains:</p>\n<ul>\n<li>Unique submission ID</li>\n<li>Timestamp of submission</li>\n<li>Reference to the parent form</li>\n<li>All submitted field values</li>\n</ul>\n<h3>Automating with Flows</h3>\n<p>While submissions are read-only, you can use Flows to automate actions when new submissions arrive:</p>\n<ul>\n<li>Send custom email notifications</li>\n<li>Push data to external services</li>\n<li>Create records in other collections</li>\n<li>Trigger webhooks</li>\n<li>Format and transform submission data</li>\n</ul>\n<p>Think of Flows as your automation toolkit - they let you act on submission data without modifying the original records.</p>\n<h3>Data Management</h3>\n<h4>Exporting Data</h4>\n<p>Export submissions to work with them outside the CMS:</p>\n<ul>\n<li>Download as CSV for spreadsheet analysis</li>\n<li>Include date ranges for periodic reports</li>\n<li>Select specific fields to include</li>\n<li>Export file attachments in bulk</li>\n</ul>\n<h4>Data Cleanup</h4>\n<p>Keep your submission data organized:</p>\n<ul>\n<li>Archive old submissions</li>\n<li>Monitor storage usage from file uploads</li>\n<li>Watch for spam patterns</li>\n<li>Maintain audit logs</li>\n</ul>\n<h3>Privacy &amp; Security</h3>\n<p>Since form submissions often contain personal data:</p>\n<ul>\n<li>Follow your data retention policy</li>\n<li>Handle personal information according to privacy laws</li>\n<li>Regularly review old submissions for deletion</li>\n<li>Consider automation for data cleanup</li>\n<li>Monitor who has access to submission data</li>\n</ul>"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "redirects", "field": "meta_header_redirects", "type": "alias", "schema": null, "meta": {"collection": "redirects", "field": "meta_header_redirects", "special": ["alias", "no-data"], "interface": "super-header", "options": {"title": "{{url_from}} • {{response_code}}", "subtitle": "Redirecting to: {{url_to}}", "help": "<h2>What Are Redirects?</h2>\n<p>Redirects automatically send visitors from old URLs to new ones when content moves or changes.</p>\n<h2>Setting Up Redirects</h2>\n<ul>\n<li><strong>URL From</strong>: The old path (must be relative, like <code>/old-about-us</code>)</li>\n<li><strong>URL To</strong>: Where visitors should go (can be relative or full URL)</li>\n<li><strong>Response Code</strong>: Choose 301 (permanent) or 302 (temporary)</li>\n</ul>\n<h2>Automatic Redirects</h2>\n<p>When you change a published page's URL, there's a Flow that automatically creates redirects to prevent broken links. You can disable this in the Settings -&gt; Flows.</p>\n<h2>Best Practices</h2>\n<ul>\n<li>Keep URLs descriptive but concise</li>\n<li>Regularly review and clean up old redirects</li>\n<li>Avoid redirect chains</li>\n<li>Test redirects before publishing</li>\n</ul>"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 6, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "posts", "field": "meta_header_posts", "type": "alias", "schema": null, "meta": {"collection": "posts", "field": "meta_header_posts", "special": ["alias", "no-data"], "interface": "super-header", "options": {"title": "{{title}}", "subtitle": "{{status}} • Published At: {{published_at}}", "help": "<h2>Managing Blog Posts</h2>\n<p>Blog posts are a powerful way to share your content, news, and updates with your audience. Each post can include rich text content, images, and metadata to help with organization and discovery.</p>\n<h3>Post Settings</h3>\n<h4>Title</h4>\n<p>The main headline of your blog post. This appears at the top of the post and in preview cards across your site. Make it:</p>\n<ul>\n<li>Engaging and descriptive</li>\n<li>Clear about the post's content</li>\n<li>Optimized for search engines (aim for 50-60 characters)</li>\n</ul>\n<h4>Description</h4>\n<p>A brief preview of your post that appears in blog listings and search results. Write 1-2 compelling sentences that:</p>\n<ul>\n<li>Summarize the main point of your article</li>\n<li>Entice readers to click through</li>\n<li>Include relevant keywords naturally</li>\n</ul>\n<h4>Featured Image</h4>\n<p>The main visual for your post that appears:</p>\n<ul>\n<li>At the top of the post</li>\n<li>In preview cards across your site</li>\n<li>When sharing on social media Use high-quality, relevant images that capture attention and support your content.</li>\n</ul>\n<h4>URL Slug</h4>\n<p>The unique portion of the URL for this post. For example, in \"yoursite.com/posts/my-first-post\", \"my-first-post\" is the slug.</p>\n<ul>\n<li>Use lowercase letters and hyphens</li>\n<li>Keep it short but descriptive</li>\n<li>Include relevant keywords when possible</li>\n<li>Avoid special characters</li>\n</ul>\n<h4>Content</h4>\n<p>The main body of your post, supporting rich text formatting including:</p>\n<ul>\n<li>Headings and subheadings</li>\n<li>Paragraphs with formatting (bold, italic, etc.)</li>\n<li>Links to other content</li>\n<li>Bulleted and numbered lists</li>\n<li>Images and media</li>\n</ul>\n<h4>Author</h4>\n<p>Select the team member who wrote the post. This helps:</p>\n<ul>\n<li>Give credit to your writers</li>\n<li>Build authority for your content</li>\n<li>Allow readers to find more posts by the same author</li>\n</ul>\n<h4>Publishing Options</h4>\n<ul>\n<li><strong>Draft</strong>: Work on the post privately</li>\n<li><strong>In Review</strong>: Submit for team review</li>\n<li><strong>Published</strong>: Make the post public</li>\n<li><strong>Schedule</strong>: Set a future publication date and time</li>\n</ul>\n<h3>Best Practices for Blog Posts</h3>\n<h4>Writing Tips</h4>\n<ul>\n<li>Start with a strong opening paragraph</li>\n<li>Use subheadings to break up long content</li>\n<li>Keep paragraphs short (3-4 sentences)</li>\n<li>Include relevant internal and external links</li>\n<li>End with a clear conclusion or call to action</li>\n</ul>\n<h4>SEO Considerations</h4>\n<ul>\n<li>Include your main keyword in the title</li>\n<li>Use descriptive alt text for images</li>\n<li>Write naturally for your audience</li>\n<li>Link to related content on your site</li>\n<li>Use appropriate heading hierarchy</li>\n</ul>\n<h4>Image Guidelines</h4>\n<ul>\n<li>Choose relevant, high-quality featured images</li>\n<li>Optimize images for web performance</li>\n<li>Use consistent image sizes</li>\n<li>Include descriptive alt text</li>\n<li>Consider image placement within content</li>\n</ul>", "icon": null, "actions": [{"label": "View on Website", "icon": "arrow_outward", "type": "normal", "actionType": "link", "url": "http://localhost:3000/blog/{{slug}}"}, {"label": "Open in Visual Editor", "icon": "edit", "type": "normal", "actionType": "link", "url": "/admin/visual/http://localhost:3000/blog/{{slug}}?visual-editing=true"}, {"label": "Request Review", "icon": "approval_delegation", "type": "normal", "actionType": "flow", "flow": {"key": "3172d021-0b0f-4d4d-bcca-c5c46eb8fadf", "collection": "directus_flows"}}]}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "fill", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "forms", "field": "meta_header_forms", "type": "alias", "schema": null, "meta": {"collection": "forms", "field": "meta_header_forms", "special": ["alias", "no-data"], "interface": "super-header", "options": {"title": "{{title}}", "help": "<h2>Understanding Forms</h2>\n<p>Forms are reusable components that you create once and can place anywhere on your site using Form blocks. Think of the Forms collection as your form template library - you build the forms here, then use Form blocks to display them on your pages.</p>\n<h3>The Forms &amp; Form Blocks Relationship</h3>\n<ol>\n<li>First, create and configure your form in the Forms collection</li>\n<li>Then, add a Form block to any page where you want the form to appear</li>\n<li>Select your form from the dropdown in the Form block settings</li>\n<li>Customize the block's headline and tagline to match your page context</li>\n</ol>\n<p>This approach lets you:</p>\n<ul>\n<li>Reuse the same form across multiple pages</li>\n<li>Update the form once to change it everywhere</li>\n<li>Maintain consistent data collection</li>\n<li>Track submissions from all instances of the form</li>\n</ul>\n<h2>Creating Forms</h2>\n<h3>Basic Settings</h3>\n<h4>Form Title</h4>\n<p>Internal name to identify your form in the admin panel. Choose something descriptive like:</p>\n<ul>\n<li>\"Newsletter Signup 2024\"</li>\n<li>\"Contact Form - Main\"</li>\n<li>\"Event Registration Form\"</li>\n</ul>\n<h4>Submit Button</h4>\n<p>Customize the text on your submit button. Use action-oriented phrases like:</p>\n<ul>\n<li>\"Send Message\"</li>\n<li>\"Sign Up Now\"</li>\n<li>\"Register Today\"</li>\n</ul>\n<h4>Success Handling</h4>\n<p>Choose what happens after submission:</p>\n<p><strong>Show Message</strong></p>\n<ul>\n<li>Display a custom thank you message</li>\n<li>Confirm the submission was received</li>\n<li>Set expectations for next steps</li>\n</ul>\n<p><strong>Redirect</strong></p>\n<ul>\n<li>Send users to a specific thank you page</li>\n<li>Guide them to relevant content</li>\n<li>Start the next step in their journey</li>\n</ul>\n<h4>Email Notifications</h4>\n<p>Configure automated emails for form submissions:</p>\n<ul>\n<li>Add multiple recipient email addresses</li>\n<li>Customize subject lines and messages</li>\n<li>Include form submission details in the email</li>\n</ul>\n<h4>Active Status</h4>\n<p>Control form availability:</p>\n<ul>\n<li>Enable/disable without deleting</li>\n<li>Temporarily pause data collection</li>\n<li>Test before making public</li>\n</ul>\n<h3>Building Your Form</h3>\n<h4>Available Field Types</h4>\n<ul>\n<li><strong>Text</strong>: Single line text input</li>\n<li><strong>Textarea</strong>: Multi-line text input</li>\n<li><strong>Checkbox</strong>: Single yes/no option</li>\n<li><strong>Checkbox Group</strong>: Multiple selectable options</li>\n<li><strong>Radio</strong>: Single choice from options</li>\n<li><strong>Select</strong>: Dropdown menu</li>\n<li><strong>File</strong>: File upload capability</li>\n<li><strong>Hidden</strong>: Invisible fields for tracking</li>\n</ul>\n<h4>Field Configuration</h4>\n<p><strong>Label</strong> The visible field name shown to users</p>\n<p><strong>Placeholder</strong> Optional hint text inside empty fields</p>\n<p><strong>Help Text</strong> Additional instructions below the field</p>\n<p><strong>Required Fields</strong> Mark essential fields that must be filled out</p>\n<p><strong>Width Options</strong> Control field layout:</p>\n<ul>\n<li>Full width (100%)</li>\n<li>Two-thirds (67%)</li>\n<li>Half (50%)</li>\n<li>One-third (33%)</li>\n</ul>\n<p><strong>Validation:</strong> Here's the available rules</p>\n<ul>\n<li>Email format: <code>email</code></li>\n<li>URL format: <code>url</code></li>\n<li>Minimum length: <code>min:5</code> (replace 5 with desired length)</li>\n<li>Maximum length: <code>max:20</code> (replace 20 with desired length)</li>\n<li>Exact length: <code>length:10</code> (replace 10 with desired length)</li>\n<li>Required fields: Set \"Required\" toggle to true</li>\n</ul>\n<p>You can combine rules with pipes: <code>email|max:255</code></p>\n<h2>Using Forms with Form Blocks</h2>\n<h3>Adding Forms to Pages</h3>\n<ol>\n<li>Add a Form block to your page</li>\n<li>Select your form from the dropdown</li>\n<li>Add an optional headline and tagline</li>\n<li>Preview to check the layout</li>\n</ol>\n<h3>Form Block Customization</h3>\n<ul>\n<li>Add context with a headline</li>\n<li>Provide instructions in the tagline</li>\n<li>Match the form to your page content</li>\n<li>Ensure the form fits your layout</li>\n</ul>\n<h2>Best Practices</h2>\n<h3>Form Design &amp; Experience</h3>\n<ul>\n<li>Keep forms as short as possible - only include essential fields</li>\n<li>Group related fields together logically</li>\n<li>Use clear labels and mark required fields</li>\n<li>Ensure error messages are helpful and specific</li>\n<li>Test on both desktop and mobile devices</li>\n</ul>\n<h3>Data Collection</h3>\n<ul>\n<li>Only collect information you'll actually use</li>\n<li>Follow data protection guidelines</li>\n<li>Review submissions regularly</li>\n</ul>\n<h2>Managing Form Submissions</h2>\n<p>All submissions are stored in the <a href=\"/admin/content/form_submissions\">Form Submissions collection</a>.</p>\n<ul>\n<li>Export data as needed</li>\n<li>Configure email notifications</li>\n<li>Track submission sources</li>\n<li>Monitor submission trends</li>\n</ul>", "actions": []}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "globals", "field": "meta_header_globals", "type": "alias", "schema": null, "meta": {"collection": "globals", "field": "meta_header_globals", "special": ["alias", "no-data"], "interface": "super-header", "options": {"title": "{{title}} • Site Settings", "subtitle": "{{url}}", "help": "<h2>Global Settings</h2>\n<p>These settings control site-wide elements that affect your entire website. There's only one global settings record.</p>\n<h3>Site Info</h3>\n<ul>\n<li><strong>Title</strong>: Your website's name, appears in browser tabs and search results</li>\n<li><strong>Tagline</strong>: Short description of your site used in search results</li>\n<li><strong>Description</strong>: Longer site summary for SEO and social sharing</li>\n<li><strong>Website URL</strong>: Your site's public address</li>\n<li><strong>CMS URL</strong>: Address of your admin system</li>\n</ul>\n<h3>Brand Assets</h3>\n<ul>\n<li><strong>Logo</strong>: Main site logo for light backgrounds</li>\n<li><strong>Dark Mode Logo</strong>: Alternate logo for dark themes</li>\n<li><strong>Favicon</strong>: Small icon for browser tabs (max 512&times;512px)</li>\n<li><strong>Accent Color</strong>: Your brand's primary color for buttons and highlights</li>\n</ul>\n<h3>Social Media Links</h3>\n<p>Add URLs for your social profiles:</p>\n<ul>\n<li>Facebook, Instagram, LinkedIn</li>\n<li>Twitter, YouTube, Vimeo</li>\n<li>GitHub, Discord, Docker</li>\n</ul>\n<h3>API Settings</h3>\n<ul>\n<li><strong>OpenAI API Key</strong>: Required for AI-powered features</li>\n</ul>"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 6, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "navigation", "field": "meta_header_navigation", "type": "alias", "schema": null, "meta": {"collection": "navigation", "field": "meta_header_navigation", "special": ["alias", "no-data"], "interface": "super-header", "options": {"title": "{{title}}", "help": "<h2>Understanding Navigation</h2>\n<p>Navigation menus help visitors find their way around your site. You can create multiple menus (like main navigation, footer links, or utility menus) and manage them all from this collection.</p>\n<h3>Navigation Structure</h3>\n<p>Each navigation menu consists of:</p>\n<ol>\n<li>A menu container (Navigation)</li>\n<li>Menu items within it (Navigation Items)</li>\n</ol>\n<h2>Creating Navigation Menus</h2>\n<h3>Menu Settings</h3>\n<h4>Title</h4>\n<p>Internal name for your menu (only visible in admin). Examples:</p>\n<ul>\n<li>\"Main Navigation\"</li>\n<li>\"Footer Links\"</li>\n<li>\"Header Utility Menu\"</li>\n</ul>\n<h4>Active Status</h4>\n<p>Toggle menus on/off without deleting them. Useful for:</p>\n<ul>\n<li>Testing new menu structures</li>\n<li>Seasonal navigation changes</li>\n<li>Temporary menu updates</li>\n</ul>\n<h2>Adding Menu Items</h2>\n<h3>Item Types</h3>\n<h4>Page</h4>\n<ul>\n<li>Link to any page in your site</li>\n<li>Automatically updates if page URL changes</li>\n<li>Maintains internal link structure</li>\n</ul>\n<h4>Post</h4>\n<ul>\n<li>Link to a specific blog post</li>\n<li>Useful for featuring important articles</li>\n<li>Updates automatically if post URL changes</li>\n</ul>\n<h4>URL</h4>\n<ul>\n<li>Link to any external website (e.g., \"<a href=\"https://example.com/\">https://example.com</a>\")</li>\n<li>Create social media links</li>\n<li>Link to specific file downloads</li>\n<li>Add internal anchor links (e.g., \"#contact-section\")</li>\n<li>Point to email addresses (e.g., \"mailto:<a href=\"mailto:<EMAIL>\"><EMAIL></a>\")</li>\n<li>Link to phone numbers (e.g., \"tel:+1234567890\")</li>\n</ul>\n<h4>Group</h4>\n<ul>\n<li>Creates a container for other menu items</li>\n<li>Appears as a dropdown menu when hovered</li>\n<li>Can contain any mix of Pages, Posts, URLs, or other Groups</li>\n<li>Perfect for organizing related content (e.g., \"Services\", \"Products\")</li>\n<li>Can be nested to create multi-level navigation</li>\n<li>Doesn't require a URL - acts as a category header</li>\n</ul>\n<p>Examples of Group Usage:</p>\n<div>\n<pre>About (Group) \n└─ Our Story (Page)\n└─ Team (Page)\n└─ Careers (URL - External job board)<br>\nServices (Group) \n└─ Consulting (Page) \n└─ Training (Group) \n└─ Online Courses (URL) \n└─ Workshops (Page) \n└─ Support (Page)\n</pre>\n<h3>Item Settings</h3>\n<h4>Title</h4>\n<p>The text shown to visitors in the menu</p>\n<h4>Parent Item</h4>\n<p>For creating nested navigation:</p>\n<ul>\n<li>Select another item as parent</li>\n<li>Creates dropdown menus</li>\n<li>Builds hierarchical structure</li>\n</ul>\n<h4>Sort Order</h4>\n<p>Control the sequence of menu items:</p>\n<ul>\n<li>Drag and drop to reorder</li>\n<li>Items display in ascending order</li>\n<li>Groups show as dropdowns</li>\n</ul>\n<h2>Best Practices</h2>\n<h3>Structure</h3>\n<ul>\n<li>Keep main navigation simple</li>\n<li>Limit dropdown levels to 2-3</li>\n<li>Group related items logically</li>\n<li>Use clear, concise labels</li>\n</ul>\n<h3>Usability</h3>\n<ul>\n<li>Ensure all items are reachable</li>\n<li>Test on mobile devices</li>\n<li>Review links regularly</li>\n<li>Keep labels brief but descriptive</li>\n</ul>\n</div>"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 5, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "pages", "field": "meta_tabs", "type": "alias", "schema": null, "meta": {"collection": "pages", "field": "meta_tabs", "special": ["alias", "no-data", "group"], "interface": "group-tabs", "options": {"fillWidth": true}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "full", "translations": [{"language": "en-US", "translation": "Tabs"}], "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "block_pricing", "field": "meta_header_block_pricing", "type": "alias", "schema": null, "meta": {"collection": "block_pricing", "field": "meta_header_block_pricing", "special": ["alias", "no-data"], "interface": "super-header", "options": {"help": "<p>Display your product or service pricing options in a clear, comparative format. Use this when:</p>\n<ul>\n<li>Showcasing different service tiers</li>\n<li>Highlighting feature differences between plans</li>\n<li>Promoting special offers or featured plans</li>\n<li>Presenting subscription options</li>\n</ul>\n<p>Best practices:</p>\n<ul>\n<li>Use clear, benefit-focused plan names</li>\n<li>Highlight your recommended or most popular option</li>\n<li>Keep feature lists scannable and easy to compare</li>\n<li>Include clear call-to-action buttons for each plan</li>\n</ul>", "title": "Pricing Block", "icon": "attach_money"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 6, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "posts", "field": "meta_content", "type": "alias", "schema": null, "meta": {"collection": "posts", "field": "meta_content", "special": ["alias", "no-data", "group"], "interface": "group-raw", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 1, "width": "full", "translations": [{"language": "en-US", "translation": "Content"}], "note": null, "conditions": null, "required": false, "group": "meta_tabs", "validation": null, "validation_message": null}}, {"collection": "posts", "field": "meta_seo", "type": "alias", "schema": null, "meta": {"collection": "posts", "field": "meta_seo", "special": ["alias", "no-data", "group"], "interface": "group-raw", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 2, "width": "full", "translations": [{"language": "en-US", "translation": "SEO"}], "note": null, "conditions": null, "required": false, "group": "meta_tabs", "validation": null, "validation_message": null}}, {"collection": "posts", "field": "meta_header_content", "type": "alias", "schema": null, "meta": {"collection": "posts", "field": "meta_header_content", "special": ["alias", "no-data"], "interface": "super-header", "options": {"title": "Content", "icon": "text_snippet", "help": "<p><strong>Need inspiration?</strong> Use Flows like AI Ghostwriter to help draft your content.</p>\n<p><em>Note: This uses OpenAI so you'll need to add your API key in the&nbsp;<a href=\"/admin/content/globals\">Globals</a> collection first.</em></p>", "actions": [{"label": "AI Ghostwriter", "icon": "text_increase", "type": "normal", "actionType": "flow", "flow": {"key": "5915dd55-fff8-4d47-b48c-a0e42e5033c1", "collection": "directus_flows"}}]}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 10, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": "meta_content", "validation": null, "validation_message": null}}, {"collection": "posts", "field": "meta_tabs", "type": "alias", "schema": null, "meta": {"collection": "posts", "field": "meta_tabs", "special": ["alias", "no-data", "group"], "interface": "group-tabs", "options": {"fillWidth": true}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "full", "translations": [{"language": "en-US", "translation": "Tabs"}], "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "pages", "field": "meta_content", "type": "alias", "schema": null, "meta": {"collection": "pages", "field": "meta_content", "special": ["alias", "no-data", "group"], "interface": "group-raw", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 1, "width": "full", "translations": [{"language": "en-US", "translation": "Content"}], "note": null, "conditions": null, "required": false, "group": "meta_tabs", "validation": null, "validation_message": null}}, {"collection": "pages", "field": "meta_seo", "type": "alias", "schema": null, "meta": {"collection": "pages", "field": "meta_seo", "special": ["alias", "no-data", "group"], "interface": "group-raw", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 2, "width": "full", "translations": [{"language": "en-US", "translation": "SEO"}], "note": null, "conditions": null, "required": false, "group": "meta_tabs", "validation": null, "validation_message": null}}, {"collection": "block_richtext", "field": "meta_header_block_richtext", "type": "alias", "schema": null, "meta": {"collection": "block_richtext", "field": "meta_header_block_richtext", "special": ["alias", "no-data"], "interface": "super-header", "options": {"title": "Rich Text Block", "help": "<p>Perfect for creating formatted text content with headings, paragraphs, and lists. Use this block when you need to:</p>\n<ul>\n<li>Write longer form content with multiple paragraphs</li>\n<li>Create structured content with headings and subheadings</li>\n<li>Include inline formatting like bold, italic, or links</li>\n<li>Add bulleted or numbered lists</li>\n</ul>", "icon": "format_color_text"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 6, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "pages", "field": "meta_header_pages", "type": "alias", "schema": null, "meta": {"collection": "pages", "field": "meta_header_pages", "special": ["alias", "no-data"], "interface": "super-header", "options": {"title": "{{title}}", "subtitle": "{{status}} • Published At: {{published_at}}", "help": "<h2>Creating and Managing Pages</h2>\n<p>Pages are the foundation of your website, where you'll combine different content blocks to create engaging layouts. Each page can be built using a mix of blocks like Hero sections, Rich Text, Forms, and more.</p>\n<h3>Page Settings</h3>\n<h4>Title</h4>\n<p>The page title appears in browser tabs and search results. Make it clear and descriptive of the page's content. This is what visitors will see when they bookmark your page or share it on social media.</p>\n<h4>URL (Permalink)</h4>\n<ul>\n<li>Always starts with a forward slash (/)</li>\n<li>Use simple, lowercase words separated by hyphens</li>\n<li>Keep it short but descriptive</li>\n<li>Examples:\n<ul>\n<li>/about</li>\n<li>/contact-us</li>\n<li>/services/web-design</li>\n</ul>\n</li>\n</ul>\n<h4>Description</h4>\n<p>A brief summary of the page that appears in search results. Write 1-2 sentences that clearly explain what visitors will find on this page. Good descriptions help with SEO and encourage people to click through to your site.</p>\n<h4>Publishing Options</h4>\n<ul>\n<li><strong>Draft</strong>: Work on the page without making it public</li>\n<li><strong>In Review</strong>: Submit the page for review by other team members</li>\n<li><strong>Published</strong>: Make the page visible to the public</li>\n<li><strong>Schedule</strong>: Set a future publication date</li>\n</ul>\n<p><img src=\"/assets/5e93050a-6f17-4314-a7e5-f78bda425fea.png?width=2076&amp;height=1551\" alt=\"Help Blocks Content\" loading=\"lazy\"></p>\n<h2>Working with Blocks</h2>\n<p>Your page content is built by adding and arranging blocks. Think of blocks as building pieces that you can stack and rearrange to create your perfect page layout.</p>\n<h4>Tips for Building with Blocks</h4>\n<ul>\n<li>Start with a Hero block for main landing pages</li>\n<li>Use consistent block types for similar content across pages</li>\n<li>Consider the natural flow of information when ordering blocks</li>\n<li>Preview your page on both desktop and mobile before publishing</li>\n<li>You can temporarily hide blocks without deleting them using the \"Hide block\" toggle</li>\n</ul>\n<h4>Block Background Options</h4>\n<p>Each block can have either a light or dark background:</p>\n<ul>\n<li>Default (light): Default option, best for regular content sections</li>\n<li>Dark: Creates visual contrast, good for highlighting important sections</li>\n</ul>\n<h4>General Tips for All Blocks</h4>\n<ul>\n<li>Preview your changes before publishing</li>\n<li>Consider mobile viewing experience</li>\n<li>Keep related content in the same block type for consistency</li>\n<li>Use clear headlines and taglines to guide your visitors</li>\n<li>Make sure images are optimized for web use</li>\n<li>Test all interactive elements like forms and buttons</li>\n</ul>", "actions": [{"label": "View on Website", "icon": "arrow_outward", "type": "normal", "actionType": "link", "url": "http://localhost:3000{{permalink}}"}, {"label": "Open in Visual Editor", "icon": "edit", "type": "normal", "actionType": "link", "url": "/admin/visual/http://localhost:3000{{permalink}}?visual-editing=true"}, {"label": "Duplicate Page", "icon": "copy_all", "type": "normal", "actionType": "flow", "flow": {"key": "a23110e1-700b-41b8-9f9e-ca0998b84a76", "collection": "directus_flows"}}]}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "fill", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}]