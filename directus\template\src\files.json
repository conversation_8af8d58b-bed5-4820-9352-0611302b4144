[{"id": "03a7d1c7-81e2-432f-9561-9df2691189c8", "storage": "cloud", "filename_disk": "03a7d1c7-81e2-432f-9561-9df2691189c8.png", "filename_download": "directus-tshirt-product-interface.png", "title": "Directus E-Commerce T-Shirt Product Interface", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2024-12-18T13:23:30.706Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:40:23.748Z", "charset": null, "filesize": "659420", "width": 2560, "height": 1440, "duration": null, "embed": null, "description": "Screenshot of the Directus admin interface showing a product detail page for a 'Directus Super Soft T-Shirt'. The interface displays product variants with color and size options, and a thumbnail image of a person wearing the gray t-shirt with Directus logo.", "location": null, "tags": ["e-commerce", "product", "interface", "t-shirt", "directus", "admin", "screenshot"], "metadata": {}, "focal_point_x": 471, "focal_point_y": 181, "tus_id": null, "tus_data": null, "uploaded_on": "2024-12-18T13:23:30.749Z"}, {"id": "12e02b82-b4a4-4aaf-8ca4-e73c20a41c26", "storage": "cloud", "filename_disk": "12e02b82-b4a4-4aaf-8ca4-e73c20a41c26.jpeg", "filename_download": "cottontail-rabbit-portrait.jpeg", "title": "Cottontail Rabbit in Grass", "type": "image/jpeg", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2024-02-26T13:02:44.071Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:40:36.335Z", "charset": null, "filesize": "214070", "width": 1920, "height": 1280, "duration": null, "embed": null, "description": "Close-up portrait of a wild cottontail rabbit sitting alert in green grass with a soft-focus golden yellow background. The rabbit is looking directly at the camera with its distinctive large ears upright.", "location": null, "tags": ["rabbit", "wildlife", "nature", "animal", "cottontail", "outdoor", "photography"], "metadata": {}, "focal_point_x": 960, "focal_point_y": 345, "tus_id": null, "tus_data": null, "uploaded_on": "2024-02-26T13:02:44.071Z"}, {"id": "14594872-a805-4251-8dfd-b93bb2effbc0", "storage": "cloud", "filename_disk": "14594872-a805-4251-8dfd-b93bb2effbc0.png", "filename_download": "frontend-developer-avatar.png", "title": "Frontend Developer Avatar", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2025-04-07T21:57:36.731Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:40:52.008Z", "charset": null, "filesize": "1406914", "width": 1024, "height": 1536, "duration": null, "embed": null, "description": "Digital avatar representing a frontend developer character in portrait orientation. The image features a stylized character design suitable for profile or user representation in a development or tech context.", "location": null, "tags": ["avatar", "frontend", "developer", "digital", "character", "profile", "illustration"], "metadata": {}, "focal_point_x": 512, "focal_point_y": 768, "tus_id": null, "tus_data": null, "uploaded_on": "2025-04-07T21:57:36.757Z"}, {"id": "1d3d2bd3-ff59-4626-bef5-9d5eef6510b3", "storage": "cloud", "filename_disk": "1d3d2bd3-ff59-4626-bef5-9d5eef6510b3.png", "filename_download": "visual-studio-code-logo.png", "title": "Visual Studio Code Logo", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2024-12-18T15:57:57.074Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:41:04.741Z", "charset": null, "filesize": "57617", "width": 1024, "height": 1024, "duration": null, "embed": null, "description": "The official blue Visual Studio Code (VS Code) logo, featuring a stylized folded ribbon design forming a visual representation of the '<>' characters in blue gradient on a transparent background.", "location": null, "tags": ["vscode", "logo", "microsoft", "IDE", "development", "software", "code editor"], "metadata": {}, "focal_point_x": 512, "focal_point_y": 512, "tus_id": null, "tus_data": null, "uploaded_on": "2024-12-18T15:57:57.111Z"}, {"id": "2b4a0ba0-52c7-4e10-b191-c803d8da6a36", "storage": "cloud", "filename_disk": "2b4a0ba0-52c7-4e10-b191-c803d8da6a36.png", "filename_download": "directus-logo-purple.png", "title": "<PERSON><PERSON> - Purple Background", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2024-02-26T13:02:44.656Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:41:16.804Z", "charset": null, "filesize": "43613", "width": 512, "height": 512, "duration": null, "embed": null, "description": "The Directus logo displaying a white silhouette of a running gazelle or antelope on a vibrant purple square background with rounded corners. The logo represents speed and agility, core values of the Directus platform.", "location": null, "tags": ["directus", "logo", "brand", "purple", "gazelle", "CMS", "headless"], "metadata": {}, "focal_point_x": 256, "focal_point_y": 256, "tus_id": null, "tus_data": null, "uploaded_on": "2024-02-26T13:02:44.656Z"}, {"id": "35a67f1b-d401-4300-a503-b8e583186f2a", "storage": "cloud", "filename_disk": "35a67f1b-d401-4300-a503-b8e583186f2a.svg", "filename_download": "directus-logo.svg", "title": "Directus Logo SVG", "type": "image/svg+xml", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2024-02-26T13:02:44.751Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:41:25.160Z", "charset": null, "filesize": "11267", "width": null, "height": null, "duration": null, "embed": null, "description": "The scalable vector graphic (SVG) version of the Directus logo featuring a stylized gazelle silhouette. This vector format allows the logo to be scaled to any size without losing quality.", "location": null, "tags": ["directus", "logo", "svg", "vector", "brand", "gazelle", "scalable"], "metadata": null, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2024-02-26T13:02:44.751Z"}, {"id": "3eff7dc2-445a-47c5-9503-3f600ecdb5c6", "storage": "cloud", "filename_disk": "3eff7dc2-445a-47c5-9503-3f600ecdb5c6.jpeg", "filename_download": "steampunk-rabbit-portrait.jpeg", "title": "Steampunk Rabbit Portrait", "type": "image/jpeg", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2024-02-26T13:02:44.859Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:41:38.383Z", "charset": null, "filesize": "51166", "width": 512, "height": 512, "duration": null, "embed": null, "description": "Digital art portrait of an anthropomorphized rabbit wearing Victorian or steampunk-style clothing with mechanical details. The rabbit has a realistic head with dark fur and alert ears, while wearing a formal brown jacket with gear elements against a vintage paper background.", "location": null, "tags": ["rabbit", "steampunk", "anthropomorphic", "digital art", "portrait", "fantasy", "Victorian"], "metadata": {}, "focal_point_x": 256, "focal_point_y": 256, "tus_id": null, "tus_data": null, "uploaded_on": "2024-02-26T13:02:44.859Z"}, {"id": "43ddd7b8-9b2f-4aa1-b63c-933b4ae81ca2", "storage": "cloud", "filename_disk": "43ddd7b8-9b2f-4aa1-b63c-933b4ae81ca2.svg", "filename_download": "directus-logo-dark.svg", "title": "<PERSON><PERSON>", "type": "image/svg+xml", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2024-09-13T00:30:00.408Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:41:48.788Z", "charset": null, "filesize": "11267", "width": null, "height": null, "duration": null, "embed": null, "description": "The SVG version of the Directus logo designed for use on light backgrounds, featuring the iconic gazelle silhouette in a dark color scheme. This scalable vector format ensures the logo maintains crisp quality at any size.", "location": null, "tags": ["directus", "logo", "svg", "dark", "brand", "vector", "gazelle"], "metadata": null, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2024-09-13T00:30:00.438Z"}, {"id": "44a4e780-d59b-4fa5-9b26-1c4b447474d2", "storage": "cloud", "filename_disk": "44a4e780-d59b-4fa5-9b26-1c4b447474d2.jpg", "filename_download": "multicolored-can-wall-art-dreams.jpg", "title": "Multicolored Can Wall Art - Dreams Come True", "type": "image/jpeg", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2024-02-26T13:02:44.950Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:42:03.964Z", "charset": null, "filesize": "1221311", "width": 1917, "height": 1918, "duration": null, "embed": null, "description": "Creative wall art made from painted beverage cans arranged to form the inspirational phrase \"I FEEL LIKE MAKIN' DREAMS COME TRUE\". The cans are painted in white, yellow, teal, and black with letters stenciled on them, mounted against a textured concrete background.", "location": null, "tags": ["wall art", "inspirational", "typography", "upcycled", "cans", "motivational", "urban"], "metadata": {}, "focal_point_x": 959, "focal_point_y": 959, "tus_id": null, "tus_data": null, "uploaded_on": "2024-02-26T13:02:44.950Z"}, {"id": "50570a31-a990-453c-bdfc-0ad7175dd8bf", "storage": "cloud", "filename_disk": "50570a31-a990-453c-bdfc-0ad7175dd8bf.png", "filename_download": "directus-crm-pipeline-view.png", "title": "Directus CRM Pipeline Interface", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2024-12-18T13:22:47.817Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:42:16.341Z", "charset": null, "filesize": "409799", "width": 2560, "height": 1440, "duration": null, "embed": null, "description": "Screenshot of a Directus CRM interface showing a sales pipeline view with three columns: New, Qualification, and Proposal. The pipeline displays ongoing deals with client names and dates, including HealthPlus Digital Transformation, Tezla Deal, SolarTech Solutions, and TechNova AI Integration.", "location": null, "tags": ["CRM", "directus", "interface", "sales", "pipeline", "screenshot", "dashboard"], "metadata": {}, "focal_point_x": 621, "focal_point_y": 220, "tus_id": null, "tus_data": null, "uploaded_on": "2024-12-18T13:22:47.846Z"}, {"id": "5e93050a-6f17-4314-a7e5-f78bda425fea", "storage": "cloud", "filename_disk": "5e93050a-6f17-4314-a7e5-f78bda425fea.png", "filename_download": "content-blocks-interface.png", "title": "Content Blocks Interface", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2025-01-15T15:15:24.119Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:42:27.598Z", "charset": null, "filesize": "828178", "width": 2076, "height": 1551, "duration": null, "embed": null, "description": "Screenshot of a website builder interface showing content blocks functionality against a purple gradient background. The interface displays existing blocks (Hero, Rich Text, Gallery, Pricing, Form) with their content previews and a section below to create new content block types with associated icons.", "location": null, "tags": ["interface", "website builder", "content blocks", "UI", "CMS", "web design", "purple"], "metadata": {}, "focal_point_x": 595, "focal_point_y": 450, "tus_id": null, "tus_data": null, "uploaded_on": "2025-01-15T15:15:24.151Z"}, {"id": "5f35b7e3-0357-47c3-807f-f132cca95e3f", "storage": "cloud", "filename_disk": "5f35b7e3-0357-47c3-807f-f132cca95e3f.png", "filename_download": "content-writer-avatar.png", "title": "Content Writer <PERSON><PERSON>", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2025-04-07T21:37:15.525Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:42:39.157Z", "charset": null, "filesize": "1194631", "width": 1024, "height": 1536, "duration": null, "embed": null, "description": "Digital illustration of a content writer avatar in portrait orientation. The image features a stylized character designed to represent a professional writer or content creator, suitable for profile pictures or user representations in writing or publishing platforms.", "location": null, "tags": ["avatar", "writer", "content creator", "illustration", "digital", "character", "profile"], "metadata": {}, "focal_point_x": 512, "focal_point_y": 768, "tus_id": null, "tus_data": null, "uploaded_on": "2025-04-07T21:37:15.556Z"}, {"id": "6464e61f-455a-4b47-b623-bb12e5251dfe", "storage": "cloud", "filename_disk": "6464e61f-455a-4b47-b623-bb12e5251dfe.jpeg", "filename_download": "armored-knight-rabbit.jpeg", "title": "Knight Rabbit in Armor", "type": "image/jpeg", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2024-02-26T13:02:45.463Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:42:52.927Z", "charset": null, "filesize": "537459", "width": 2048, "height": 3072, "duration": null, "embed": null, "description": "Digital fantasy art of an anthropomorphic rabbit in detailed plate armor with a purple cape and red accent fabric. The rabbit has distinctive long ears and is portrayed in a noble, knight-like pose with intricate metallic armor against a soft-focus sunset background.", "location": null, "tags": ["fantasy", "rabbit", "knight", "armor", "digital art", "anthropomorphic", "medieval"], "metadata": {}, "focal_point_x": 1024, "focal_point_y": 1024, "tus_id": null, "tus_data": null, "uploaded_on": "2024-02-26T13:02:45.463Z"}, {"id": "68103296-6634-4d66-918a-04b09afe6621", "storage": "cloud", "filename_disk": "68103296-6634-4d66-918a-04b09afe6621.jpeg", "filename_download": "black-rabbit-figurine-night.jpeg", "title": "Black Rabbit Figurine", "type": "image/jpeg", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2024-02-26T13:02:45.654Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:43:05.630Z", "charset": null, "filesize": "421019", "width": 3072, "height": 2048, "duration": null, "embed": null, "description": "3D render of a stylized black rabbit figurine with red inner ears, golden eyes, and a leather collar with a medallion. The figurine is illuminated against a blurred nighttime urban background with warm lighting, creating dramatic highlights on its glossy surface.", "location": null, "tags": ["rabbit", "figurine", "3D render", "black", "stylized", "digital art", "urban"], "metadata": {}, "focal_point_x": 768, "focal_point_y": 320, "tus_id": null, "tus_data": null, "uploaded_on": "2024-02-26T13:02:45.654Z"}, {"id": "6964d750-1c00-4b9c-81e4-0c81cfa82bbb", "storage": "cloud", "filename_disk": "6964d750-1c00-4b9c-81e4-0c81cfa82bbb.png", "filename_download": "ecommerce-login-interface.png", "title": "E-Commerce Login Page", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2024-12-18T13:23:30.688Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:43:17.489Z", "charset": null, "filesize": "665029", "width": 2560, "height": 1440, "duration": null, "embed": null, "description": "Screenshot of a Simple eCommerce application login page with a clean, modern interface. The left side features a white panel with email and password input fields, sign in button, and forgot password link. The right side has a dark blue-green gradient background. The Directus logo appears in the top-left corner.", "location": null, "tags": ["login", "e-commerce", "UI", "interface", "directus", "web design", "authentication"], "metadata": {}, "focal_point_x": 320, "focal_point_y": 359, "tus_id": null, "tus_data": null, "uploaded_on": "2024-12-18T13:23:30.724Z"}, {"id": "7775c53a-6c2c-453d-8c22-8b5445121d10", "storage": "cloud", "filename_disk": "7775c53a-6c2c-453d-8c22-8b5445121d10.jpeg", "filename_download": "business-rabbit-office.jpeg", "title": "Business Rabbit with Laptop", "type": "image/jpeg", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2024-02-26T13:02:46.268Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:43:29.836Z", "charset": null, "filesize": "358326", "width": 2048, "height": 3072, "duration": null, "embed": null, "description": "Digital art portrait of an anthropomorphic rabbit wearing a business suit with a navy jacket and bright pink tie, standing at a desk with a laptop. The image blends photorealistic rabbit features with human clothing and posture in an office setting.", "location": null, "tags": ["rabbit", "business", "anthropomorphic", "digital art", "office", "laptop", "suit"], "metadata": {}, "focal_point_x": 1024, "focal_point_y": 768, "tus_id": null, "tus_data": null, "uploaded_on": "2024-02-26T13:02:46.268Z"}, {"id": "8a652e52-a275-4dde-9fc5-edf2188afe56", "storage": "cloud", "filename_disk": "8a652e52-a275-4dde-9fc5-edf2188afe56.jpg", "filename_download": "directus-headless-cms-hero.jpg", "title": "Hero Center Image", "type": "image/jpeg", "folder": "7304d56d-8c53-49cd-9815-d8188cec22db", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2025-01-14T17:09:54.433Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T04:14:41.394Z", "charset": null, "filesize": "80086", "width": 906, "height": 800, "duration": null, "embed": null, "description": "Marketing hero section image for Directus CMS with the tagline 'Headless but not brainless'. The image includes a brief description about Directus providing a backend for headless CMS use cases with additional features like authentication and user permissions. Below is a simplified UI mockup with purple accent colors.", "location": null, "tags": ["directus", "headless CMS", "hero section", "marketing", "UI", "web design", "purple"], "metadata": {}, "focal_point_x": 453, "focal_point_y": 400, "tus_id": null, "tus_data": null, "uploaded_on": "2025-01-14T17:09:54.468Z"}, {"id": "8f748634-d77b-4985-b27e-7e1f3559881a", "storage": "cloud", "filename_disk": "8f748634-d77b-4985-b27e-7e1f3559881a.jpeg", "filename_download": "scholarly-rabbit-portrait.jpeg", "title": "Scholarly Rabbit Portrait", "type": "image/jpeg", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2024-02-26T13:02:46.650Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:43:55.934Z", "charset": null, "filesize": "98432", "width": 512, "height": 512, "duration": null, "embed": null, "description": "Black and white illustration of an anthropomorphic white rabbit dressed in Victorian or 19th century scholar attire with a coat and cravat. The rabbit is depicted writing with a quill pen in an open book or ledger, presented in a vintage engraving style.", "location": null, "tags": ["rabbit", "vintage", "illustration", "anthropomorphic", "scholar", "Victorian", "black and white"], "metadata": {}, "focal_point_x": 256, "focal_point_y": 256, "tus_id": null, "tus_data": null, "uploaded_on": "2024-02-26T13:02:46.650Z"}, {"id": "9a52e835-e131-4290-81bb-5a512599f93e", "storage": "cloud", "filename_disk": "9a52e835-e131-4290-81bb-5a512599f93e.png", "filename_download": "technical-illustration-wide.png", "title": "Generated Technical Illustration", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2024-09-13T04:23:32.265Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T03:03:16.520Z", "charset": null, "filesize": "1834271", "width": 1200, "height": 686, "duration": null, "embed": null, "description": "A wide-format technical or conceptual illustration, likely AI-generated based on the filename pattern. The image has landscape orientation with dimensions suitable for presentations, documentation, or web design mockups.", "location": null, "tags": ["illustration", "technical", "generated", "wide format", "digital art", "concept", "design"], "metadata": {}, "focal_point_x": 896, "focal_point_y": 512, "tus_id": null, "tus_data": null, "uploaded_on": "2025-05-07T03:03:16.516Z"}, {"id": "a051ea01-07a5-45cb-bcc6-411af9560be5", "storage": "cloud", "filename_disk": "a051ea01-07a5-45cb-bcc6-411af9560be5.png", "filename_download": "directus-crm-tags-interface.png", "title": "Directus CRM Tags Interface", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2024-12-18T13:22:15.631Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:44:25.529Z", "charset": null, "filesize": "297608", "width": 2560, "height": 1440, "duration": null, "embed": null, "description": "Screenshot of the Directus CRM tags management interface. The screen shows a list of tags including 'VIP' (blue tag) and 'Key Account' (red tag) with their associated collections (Contacts and Organizations). The left sidebar displays the CRM navigation menu and the purple Directus logo.", "location": null, "tags": ["CRM", "directus", "interface", "tags", "management", "screenshot", "UI"], "metadata": {}, "focal_point_x": 550, "focal_point_y": 220, "tus_id": null, "tus_data": null, "uploaded_on": "2024-12-18T13:22:15.675Z"}, {"id": "ac905071-0643-4337-8f53-48ed45b1ccf2", "storage": "cloud", "filename_disk": "ac905071-0643-4337-8f53-48ed45b1ccf2.jpg", "filename_download": "tree-silhouette-starry-sunset.jpg", "title": "Tree Silhouette Against Starry Sunset", "type": "image/jpeg", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2024-02-26T13:02:46.862Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:44:39.100Z", "charset": null, "filesize": "300062", "width": 1920, "height": 1280, "duration": null, "embed": null, "description": "Breathtaking landscape photograph of a solitary tree silhouetted against a vibrant orange and gold sunset sky filled with stars. The dark silhouette of the tree and distant hills creates a dramatic contrast against the warm sunset glow and the deep blue-black of the night sky above.", "location": null, "tags": ["sunset", "silhouette", "tree", "landscape", "stars", "night sky", "nature"], "metadata": {}, "focal_point_x": 960, "focal_point_y": 640, "tus_id": null, "tus_data": null, "uploaded_on": "2024-02-26T13:02:46.862Z"}, {"id": "ae390ba1-fcff-4b99-a445-5f19257095d1", "storage": "cloud", "filename_disk": "ae390ba1-fcff-4b99-a445-5f19257095d1.svg", "filename_download": "directus-logo-white.svg", "title": "<PERSON><PERSON>", "type": "image/svg+xml", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2025-01-09T17:49:25.084Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:44:50.427Z", "charset": null, "filesize": "11427", "width": null, "height": null, "duration": null, "embed": null, "description": "White SVG version of the Directus logo featuring the iconic gazelle silhouette. This version is optimized for use on dark backgrounds. As a vector graphic, it maintains perfect quality at any size and can be used for websites, applications, and marketing materials.", "location": null, "tags": ["directus", "logo", "svg", "white", "vector", "brand", "gazelle"], "metadata": null, "focal_point_x": null, "focal_point_y": null, "tus_id": null, "tus_data": null, "uploaded_on": "2025-01-09T17:49:25.109Z"}, {"id": "b9db00d9-535f-4e24-8a46-5f7e5fc65bf2", "storage": "cloud", "filename_disk": "b9db00d9-535f-4e24-8a46-5f7e5fc65bf2.jpg", "filename_download": "ui-wireframe-sketching.jpg", "title": "UI/UX Wireframe Sketching", "type": "image/jpeg", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2024-02-26T13:02:47.145Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:45:04.425Z", "charset": null, "filesize": "129160", "width": 1920, "height": 1280, "duration": null, "embed": null, "description": "Close-up photograph of a hand holding a yellow pen drawing UI wireframes on paper. The image shows three linked mobile app screen mockups with navigation arrows, interface elements, and highlighted areas in orange. The person has light-colored nail polish and appears to be sketching preliminary design concepts.", "location": null, "tags": ["wireframe", "UX design", "sketching", "UI", "design process", "mobile app", "prototype"], "metadata": {}, "focal_point_x": 960, "focal_point_y": 425, "tus_id": null, "tus_data": null, "uploaded_on": "2024-02-26T13:02:47.145Z"}, {"id": "c2a301bd-74ed-4a50-9b85-3cb1f40f8dee", "storage": "cloud", "filename_disk": "c2a301bd-74ed-4a50-9b85-3cb1f40f8dee.png", "filename_download": "directus-product-editor-interface.png", "title": "Directus E-Commerce Product Editor", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2024-12-18T13:23:30.689Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:45:18.901Z", "charset": null, "filesize": "630837", "width": 2560, "height": 1440, "duration": null, "embed": null, "description": "Screenshot of the Directus e-commerce product editing interface for a 'Directus Super Soft T-Shirt'. The screen shows various product fields including title, status (Active), slug, category (Shirts), and a product description editor with humorous content about a developer t-shirt with the Directus rabbit logo.", "location": null, "tags": ["directus", "e-commerce", "product editor", "interface", "CMS", "t-shirt", "screenshot"], "metadata": {}, "focal_point_x": 650, "focal_point_y": 420, "tus_id": null, "tus_data": null, "uploaded_on": "2024-12-18T13:23:30.725Z"}, {"id": "d4fd6edc-4cc5-48c1-8bc7-e646924bbdca", "storage": "cloud", "filename_disk": "d4fd6edc-4cc5-48c1-8bc7-e646924bbdca.jpeg", "filename_download": "rabbit-in-blue-hoodie.jpeg", "title": "Rabbit in Blue Hoodie", "type": "image/jpeg", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2024-02-26T13:02:47.562Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:46:30.770Z", "charset": null, "filesize": "395249", "width": 2048, "height": 3072, "duration": null, "embed": null, "description": "Digital artwork of an anthropomorphic rabbit wearing a navy blue hoodie or jacket, standing upright with hands in pockets. The image features a realistic rabbit head with large ears and expressive eyes, combined with human posture and clothing. The background is a blurred path or tunnel creating depth and focus on the character.", "location": null, "tags": ["rabbit", "anthropomorphic", "digital art", "hoodie", "urban", "cute", "3D render"], "metadata": {}, "focal_point_x": 1024, "focal_point_y": 1536, "tus_id": null, "tus_data": null, "uploaded_on": "2024-02-26T13:02:47.562Z"}, {"id": "d5a1290f-8819-4e7c-b292-bffe5b1c8274", "storage": "cloud", "filename_disk": "d5a1290f-8819-4e7c-b292-bffe5b1c8274.jpg", "filename_download": "directus-hero-left-section.jpg", "title": "Hero Left Image", "type": "image/jpeg", "folder": "7304d56d-8c53-49cd-9815-d8188cec22db", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2025-01-14T17:09:48.650Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T04:14:58.244Z", "charset": null, "filesize": "80496", "width": 1188, "height": 663, "duration": null, "embed": null, "description": "Left-aligned website hero section for Directus CMS featuring the tagline 'Headless but not brainless' and a brief description of <PERSON>us as a backend for headless CMS use cases with authentication and user permissions. The left side shows a simplified UI mockup with purple accent colors against a light pattern background.", "location": null, "tags": ["directus", "headless CMS", "hero section", "UI mockup", "website", "marketing", "purple"], "metadata": {}, "focal_point_x": 594, "focal_point_y": 331, "tus_id": null, "tus_data": null, "uploaded_on": "2025-01-14T17:09:48.691Z"}, {"id": "d627d585-2c14-4bbf-89ca-34581083cc1d", "storage": "cloud", "filename_disk": "d627d585-2c14-4bbf-89ca-34581083cc1d.png", "filename_download": "webmaster-developer-avatar.png", "title": "Webmaster Developer Avatar", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2025-04-07T21:53:25.874Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:46:58.021Z", "charset": null, "filesize": "1347150", "width": 1024, "height": 1536, "duration": null, "embed": null, "description": "Digital avatar representing a webmaster or web developer character in portrait orientation. The image features a stylized character design suitable for profile pictures or user representations in web development, IT, or technical contexts.", "location": null, "tags": ["avatar", "webmaster", "developer", "digital", "character", "profile", "illustration"], "metadata": {}, "focal_point_x": 512, "focal_point_y": 768, "tus_id": null, "tus_data": null, "uploaded_on": "2025-04-07T21:53:25.911Z"}, {"id": "dc258f02-d1a3-47f4-9f3e-2a71a0010c56", "storage": "cloud", "filename_disk": "dc258f02-d1a3-47f4-9f3e-2a71a0010c56.png", "filename_download": "directus-ai-image-generator.png", "title": "Directus AI Image Generator Interface", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2024-12-18T13:25:18.656Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:47:12.309Z", "charset": null, "filesize": "559420", "width": 2560, "height": 1440, "duration": null, "embed": null, "description": "Screenshot of the Directus CMS AI Image Generator modal dialog. The interface shows a text prompt input area with formatting tools, where users can describe the image they want to generate. The prompt describes creating a hand-drawn marker style illustration for developer blog posts. The modal also includes color selection options and a 'Create New' button.", "location": null, "tags": ["directus", "AI", "image generator", "interface", "CMS", "content creation", "modal"], "metadata": {}, "focal_point_x": 599, "focal_point_y": 330, "tus_id": null, "tus_data": null, "uploaded_on": "2024-12-18T13:25:18.670Z"}, {"id": "df0745c2-b6e3-4b37-b64d-55a4eb0033ab", "storage": "cloud", "filename_disk": "df0745c2-b6e3-4b37-b64d-55a4eb0033ab.avif", "filename_download": "modern-web-banner.avif", "title": "Modern Web Banner - AVIF Format", "type": "image/avif", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2024-02-26T13:02:47.856Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:47:30.279Z", "charset": null, "filesize": "17700", "width": 1184, "height": 666, "duration": null, "embed": null, "description": "A widescreen banner image in the modern AVIF file format, which offers better compression and quality than traditional image formats. This image demonstrates the use of next-generation image formats for web optimization. AVIF provides excellent image quality at smaller file sizes, improving website performance.", "location": null, "tags": ["AVIF", "widescreen", "banner", "digital", "modern", "web format", "next-gen"], "metadata": {}, "focal_point_x": 592, "focal_point_y": 333, "tus_id": null, "tus_data": null, "uploaded_on": "2024-02-26T13:02:47.856Z"}, {"id": "ea743e20-e6e9-4be8-a949-3771cd182810", "storage": "cloud", "filename_disk": "ea743e20-e6e9-4be8-a949-3771cd182810.png", "filename_download": "directus-interface-mockup.png", "title": "Directus Interface <PERSON>", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2025-01-09T17:57:02.562Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T02:47:43.041Z", "charset": null, "filesize": "35267", "width": 616, "height": 424, "duration": null, "embed": null, "description": "A simplified mockup of the Directus interface showing the dashboard with a clean, minimal design. The interface features the Directus logo in the top left, navigation elements, and a content area with placeholder elements highlighted with purple accent colors. The background has a subtle grid pattern.", "location": null, "tags": ["directus", "UI", "interface", "mockup", "dashboard", "minimal", "purple"], "metadata": {}, "focal_point_x": 308, "focal_point_y": 212, "tus_id": null, "tus_data": null, "uploaded_on": "2025-01-09T17:57:02.603Z"}, {"id": "fd6440c2-dd48-4792-9d08-3124cd99b40f", "storage": "cloud", "filename_disk": "fd6440c2-dd48-4792-9d08-3124cd99b40f.png", "filename_download": "technical-landscape-illustration.png", "title": "Digital Concept Art - Widescreen Format", "type": "image/png", "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "uploaded_by": null, "created_on": "2024-09-13T04:43:17.484Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T03:03:34.811Z", "charset": null, "filesize": "1600543", "width": 1200, "height": 686, "duration": null, "embed": null, "description": "High-resolution widescreen digital artwork in landscape orientation (1792×1024 pixels). This versatile image is optimized for website hero sections, presentation backgrounds, or digital marketing materials. The wide aspect ratio provides ample space for visual storytelling and graphic elements while maintaining excellent quality for various display sizes.", "location": null, "tags": ["illustration", "digital art", "concept", "design", "widescreen", "web graphics", "creative"], "metadata": {}, "focal_point_x": 896, "focal_point_y": 512, "tus_id": null, "tus_data": null, "uploaded_on": "2025-05-07T03:03:34.802Z"}, {"id": "fe7c7e04-5aac-4370-8bbd-6fd578d26ea1", "storage": "cloud", "filename_disk": "fe7c7e04-5aac-4370-8bbd-6fd578d26ea1.jpg", "filename_download": "directus-hero-right-section.jpg", "title": "Hero Right Image", "type": "image/jpeg", "folder": "7304d56d-8c53-49cd-9815-d8188cec22db", "uploaded_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "created_on": "2025-01-14T17:09:58.733Z", "modified_by": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "modified_on": "2025-05-07T04:14:48.251Z", "charset": null, "filesize": "78328", "width": 1229, "height": 663, "duration": null, "embed": null, "description": "Right-aligned website hero section for Directus CMS featuring the tagline 'Headless but not brainless' and a brief description about Directus providing backend solutions for headless CMS use cases. The right side displays a simplified UI mockup with purple accent colors, while the left contains marketing text against a clean background.", "location": null, "tags": ["directus", "headless CMS", "hero section", "UI mockup", "website", "marketing", "purple"], "metadata": {}, "focal_point_x": 614, "focal_point_y": 332, "tus_id": null, "tus_data": null, "uploaded_on": "2025-01-14T17:09:58.743Z"}]