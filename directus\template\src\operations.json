[{"id": "0beeeb1a-2338-4ab4-95f5-0757bf3e43be", "name": "Log <PERSON>r", "key": "log_error", "type": "log", "position_x": 37, "position_y": 17, "options": {"message": "<PERSON><PERSON> did NOT include a `to` address. "}, "resolve": null, "reject": null, "flow": "7c8732cd-9d9e-44be-a3f6-89c0d42c7687", "date_created": "2024-12-19T15:26:30.553Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "0cf30253-f9ed-413f-ac27-7a0ecffa9ee4", "name": "Globals", "key": "globals", "type": "trigger", "position_x": 19, "position_y": 1, "options": {"flow": "69e87d0b-df14-4779-bdc8-abc05f2f1e97"}, "resolve": "5df25cf8-b407-4fef-9d3b-c328f89f1561", "reject": null, "flow": "d4bbac48-a444-49e0-aedb-9af5273b88df", "date_created": "2024-09-13T04:12:38.641Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "1bb8a2e2-c98a-4a52-9224-78b4100ec087", "name": "Duplication Fields", "key": "duplication_fields", "type": "item-read", "position_x": 19, "position_y": 1, "options": {"permissions": "$trigger", "emitEvents": false, "query": {"fields": ["item_duplication_fields"]}, "collection": "directus_collections", "key": ["{{$trigger.body.collection}}"]}, "resolve": "c02bfa07-a395-48df-a0c9-e5acace6da40", "reject": null, "flow": "a23110e1-700b-41b8-9f9e-ca0998b84a76", "date_created": "2025-05-04T14:20:32.332Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "282f9a77-5519-460e-bb54-4dc6e30b059a", "name": "Did Slug Change", "key": "slug_changed", "type": "condition", "position_x": 19, "position_y": 1, "options": {"filter": {"_or": [{"$trigger": {"payload": {"slug": {"_nnull": true}}}}, {"$trigger": {"payload": {"permalink": {"_nnull": true}}}}]}}, "resolve": "f491233d-f942-4c8a-bc9d-9074fce45844", "reject": "78f34568-e64e-457b-b680-59d9a40a1a22", "flow": "c62ceaa5-bd09-4158-8329-98792debc5d5", "date_created": "2025-05-04T17:21:59.268Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "2adf33da-7fd7-42f7-a6fe-48409463d51b", "name": "Update", "key": "update", "type": "item-update", "position_x": 92, "position_y": 1, "options": {"collection": "posts", "payload": {"image": "{{import.data.data.id}}"}, "key": "{{$trigger.body.keys}}", "permissions": "$full"}, "resolve": null, "reject": null, "flow": "d4bbac48-a444-49e0-aedb-9af5273b88df", "date_created": "2024-09-13T04:12:38.606Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "2d441500-54d0-4519-a813-b16e0bbf1c08", "name": "Is Published", "key": "is_published", "type": "condition", "position_x": 55, "position_y": 1, "options": {"filter": {"_and": [{"item": {"status": {"_eq": "published"}}}]}}, "resolve": "d7f64e04-ab43-4d77-b8e8-379b41af2d3a", "reject": "9039e7fa-8dcb-4a2f-935c-b0d8dec7bd61", "flow": "c62ceaa5-bd09-4158-8329-98792debc5d5", "date_created": "2025-05-02T20:49:48.108Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "396d13ef-af98-49c1-8237-2070549a8cd0", "name": "Create Page", "key": "create_page", "type": "item-create", "position_x": 73, "position_y": 1, "options": {"permissions": "$trigger", "emitEvents": false, "collection": "{{$trigger.body.collection}}", "payload": "{{payload}}"}, "resolve": null, "reject": null, "flow": "a23110e1-700b-41b8-9f9e-ca0998b84a76", "date_created": "2025-05-04T14:20:32.287Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "3b7d004a-1dba-43c4-b902-a44fd71602ae", "name": "Format", "key": "format", "type": "exec", "position_x": 55, "position_y": 1, "options": {"code": "module.exports = function(data) {\n    const aiResponse = JSON.parse(data.write)\n\tconst payload = {}\n    payload.title = aiResponse.title\n    payload.description = aiResponse.description\n    payload.content = aiResponse.content\n    payload.slug = aiResponse.slug\n    return payload\n}"}, "resolve": "e6c50f84-e229-4f15-8119-c7708256e825", "reject": null, "flow": "5915dd55-fff8-4d47-b48c-a0e42e5033c1", "date_created": "2024-09-13T03:52:01.503Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "4b46b1e9-1ca0-47c3-86a8-68826136664e", "name": "Get Post", "key": "get_post", "type": "item-read", "position_x": 19, "position_y": 19, "options": {"permissions": "$full", "collection": "{{$trigger.body.collection}}", "key": "{{$trigger.body.keys}}"}, "resolve": "f6ae03f7-014d-43d1-8d69-cf122da302c0", "reject": null, "flow": "3172d021-0b0f-4d4d-bcca-c5c46eb8fadf", "date_created": "2025-04-02T19:01:05.023Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "599b80e4-7c74-4496-b243-da198c8613d9", "name": "Form", "key": "form", "type": "item-read", "position_x": 19, "position_y": 1, "options": {"collection": "forms", "key": ["{{$trigger.payload.form}}"], "query": {"fields": ["*", "fields.*"]}, "permissions": "$full"}, "resolve": "dff1a702-bcc0-4528-905e-309feb880111", "reject": null, "flow": "61757ab0-b139-4079-b5eb-4e05bb8142ac", "date_created": "2024-09-13T03:30:58.055Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "5d667ac5-2594-4f16-a863-3847d8917caa", "name": "Globals", "key": "globals", "type": "trigger", "position_x": 19, "position_y": 1, "options": {"flow": "69e87d0b-df14-4779-bdc8-abc05f2f1e97"}, "resolve": "89125be2-8205-4ad0-a77f-bd3e984202b3", "reject": null, "flow": "5915dd55-fff8-4d47-b48c-a0e42e5033c1", "date_created": "2024-09-13T02:39:32.169Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "5df25cf8-b407-4fef-9d3b-c328f89f1561", "name": "Prompt", "key": "prompt", "type": "directus-labs-ai-writer-operation", "position_x": 37, "position_y": 1, "options": {"apiKey": "{{globals.openai_api_key}}", "promptKey": "custom", "system": "You are an expert in writing prompts for generating images through Dall•E 3. \n\n## Rules\nReturn only the prompt text", "text": "Use the following information and context to write a prompt to generate an image for a blog post.\n\nImage Style:\n{{ $trigger.body.prompt }}\n\nAspect Ratio: \n{{ $trigger.body.aspect_ratio }}\n\nColor Palette:\n{{ $trigger.body.colors }}", "model": "gpt-4o-mini", "aiProvider": "openai", "apiKeyOpenAi": "{{globals.openai_api_key}}"}, "resolve": "d6f73e98-19ef-47d6-8d1f-99d77137d36b", "reject": null, "flow": "d4bbac48-a444-49e0-aedb-9af5273b88df", "date_created": "2024-09-13T04:12:38.633Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "6efe1572-6cdd-4c80-a063-eb71b7f1e519", "name": "Verify To", "key": "verify_to", "type": "condition", "position_x": 19, "position_y": 1, "options": {"filter": {"$trigger": {"to": {"_nnull": true}}}}, "resolve": "8673740a-f7f8-44dd-9abd-5a714e0d6c74", "reject": "0beeeb1a-2338-4ab4-95f5-0757bf3e43be", "flow": "7c8732cd-9d9e-44be-a3f6-89c0d42c7687", "date_created": "2024-12-19T15:26:30.564Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "78f34568-e64e-457b-b680-59d9a40a1a22", "name": "No Change", "key": "no_change", "type": "log", "position_x": 37, "position_y": 18, "options": {"message": "No redirect was created. The item `slug` or `permalink` did not change."}, "resolve": null, "reject": null, "flow": "c62ceaa5-bd09-4158-8329-98792debc5d5", "date_created": "2025-05-02T20:50:32.806Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "823ee957-6d5a-412c-886d-74d18de73864", "name": "Get Editor", "key": "get_editor", "type": "item-read", "position_x": 38, "position_y": 1, "options": {"permissions": "$full", "collection": "directus_users", "key": ["{{$trigger.body.editor.key}}"], "query": {"fields": ["id", "first_name", "last_name", "email"]}}, "resolve": "dc181cb9-3853-41b9-98bf-6901472bc1af", "reject": null, "flow": "3172d021-0b0f-4d4d-bcca-c5c46eb8fadf", "date_created": "2025-04-02T19:01:05.036Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "8673740a-f7f8-44dd-9abd-5a714e0d6c74", "name": "Send Email", "key": "send_email", "type": "mail", "position_x": 37, "position_y": 1, "options": {"to": "{{$trigger.to}}", "subject": "{{$trigger.subject}}", "type": "wysiwyg", "body": "<p>{{$trigger.body}}</p>"}, "resolve": null, "reject": null, "flow": "7c8732cd-9d9e-44be-a3f6-89c0d42c7687", "date_created": "2024-09-25T13:08:40.529Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "89125be2-8205-4ad0-a77f-bd3e984202b3", "name": "Write", "key": "write", "type": "directus-labs-ai-writer-operation", "position_x": 37, "position_y": 1, "options": {"apiKey": "{{globals.openai_api_key}}", "model": "gpt-4o-mini", "promptKey": "custom", "system": "You are an expert in writing blog posts that are valuable for readers.\n\n## Rules\n- You are a human content writer. Avoid AI words like \"empower\". Avoid using prolix. Sound human.\n- ALWAYS output a JSON object that matches the sample below.\n{\n\"title\": \"Article Title\",\n\"slug\": \"formatted-slug\",\n\"description\": \"Short summary of the article to entice readers\",\n\"content\": \"Article content goes here. This needs to be properly encoded HTML string with proper line breaks, etc.\"\n}", "json_mode": true, "text": "Write an article based on this prompt.\n{{$trigger.body.prompt}}\n\n\n## Voice\nMatch this style and tone of voice when writing.\n{{ $trigger.body.voice }}", "aiProvider": "openai", "apiKeyOpenAi": "{{globals.openai_api_key}}", "maxToken": 8192}, "resolve": "3b7d004a-1dba-43c4-b902-a44fd71602ae", "reject": null, "flow": "5915dd55-fff8-4d47-b48c-a0e42e5033c1", "date_created": "2024-09-13T03:52:01.514Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "9039e7fa-8dcb-4a2f-935c-b0d8dec7bd61", "name": "Not Published", "key": "not_published", "type": "log", "position_x": 73, "position_y": 18, "options": {"message": "No redirect was created. The item `status` was not `published`."}, "resolve": null, "reject": null, "flow": "c62ceaa5-bd09-4158-8329-98792debc5d5", "date_created": "2025-05-04T17:21:59.280Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "a16e81d1-4177-443e-a292-786a66faee8a", "name": "Globals", "key": "globals", "type": "trigger", "position_x": 19, "position_y": 1, "options": {"flow": "69e87d0b-df14-4779-bdc8-abc05f2f1e97"}, "resolve": "823ee957-6d5a-412c-886d-74d18de73864", "reject": null, "flow": "3172d021-0b0f-4d4d-bcca-c5c46eb8fadf", "date_created": "2025-04-02T19:28:58.499Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "aecbd95c-e882-4ceb-acef-b806eaa25770", "name": "Format Payload", "key": "payload", "type": "exec", "position_x": 55, "position_y": 1, "options": {"code": "/**\n* Page Duplication Flow Function\n* \n* This function handles the duplication of a Directus page, including all its content blocks.\n* It allows specifying a new title and permalink, or automatically creates them with \"-Copy\" suffix.\n* The duplicated page is always set to draft status for review before publishing.\n* \n* @param {Object} data - Data object from Directus Flow\n* @param {Object} data.$trigger.body - Optional data passed to the flow (new title/permalink)\n* @param {Object} data.item - The original page being duplicated with all its properties\n* @returns {Object} Modified page data for creating the duplicate\n*/\nmodule.exports = function(data) {\n   // Get the data submitted with the flow trigger (if any)\n   const triggerData = data.$trigger.body;\n   \n   // Get the full data of the existing page to duplicate\n   const existingData = data.item;\n   \n   // Set the title for the duplicate:\n   // - Use the provided title if it exists in the trigger data\n   // - Otherwise, append \"- Copy\" to the original page title\n   existingData.title = triggerData.title ?? existingData.title + ' - Copy';\n   \n   // Set the permalink for the duplicate:\n   // - Use the provided permalink if it exists in the trigger data\n   // - Otherwise, append \"-copy\" to the original permalink\n   existingData.permalink = triggerData.permalink ?? existingData.permalink + '-copy';\n   \n   // Always set the duplicate page to draft status\n   // This prevents accidental publishing of duplicate content\n   existingData.status = 'draft';\n   \n   // Return the modified data to create the duplicate page\n   // Note: Directus will handle copying all related blocks/components\n   return existingData;\n}"}, "resolve": "396d13ef-af98-49c1-8237-2070549a8cd0", "reject": null, "flow": "a23110e1-700b-41b8-9f9e-ca0998b84a76", "date_created": "2025-05-04T14:20:32.310Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "bb1b1e3f-032e-48b7-b260-1cf3af4a116c", "name": "Read Globals", "key": "read_globals", "type": "item-read", "position_x": 19, "position_y": 1, "options": {"permissions": "$full", "collection": "globals"}, "resolve": "fd271542-fac2-42d8-aa10-a02520c3753f", "reject": null, "flow": "69e87d0b-df14-4779-bdc8-abc05f2f1e97", "date_created": "2024-02-26T13:03:38.721Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "bc71ca4e-d979-4ef9-a449-af100a7e1b3b", "name": "Import", "key": "import", "type": "request", "position_x": 74, "position_y": 1, "options": {"method": "POST", "url": "{{globals.directus_url}}/files/import", "body": "{\n  \"url\": \"{{image}}\"\n}", "headers": [{"header": "Authorization", "value": "Bearer fT6_R5in_QUpUSXlLqGIhYtbRlBnopFe"}]}, "resolve": "2adf33da-7fd7-42f7-a6fe-48409463d51b", "reject": null, "flow": "d4bbac48-a444-49e0-aedb-9af5273b88df", "date_created": "2024-09-13T04:12:38.612Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "c02bfa07-a395-48df-a0c9-e5acace6da40", "name": "<PERSON><PERSON>", "key": "item", "type": "item-read", "position_x": 37, "position_y": 1, "options": {"permissions": "$trigger", "emitEvents": false, "query": {"fields": "{{duplication_fields.item_duplication_fields}}"}, "key": "{{$trigger.body.keys}}", "collection": "{{$trigger.body.collection}}"}, "resolve": "aecbd95c-e882-4ceb-acef-b806eaa25770", "reject": null, "flow": "a23110e1-700b-41b8-9f9e-ca0998b84a76", "date_created": "2025-05-04T14:20:32.324Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "c846f644-e7c1-43c9-8bb7-81181de0cd60", "name": "Render Template", "key": "render", "type": "liquidjs-operation", "position_x": 19, "position_y": 1, "options": {"publicUrl": "http://localhost:8001/", "mode": "custom", "operationMode": "single", "template": "{{ $trigger.template }}", "data": "{{ $trigger.data }}"}, "resolve": "edc5e1ae-a3ed-45af-ae77-10409a66cd03", "reject": null, "flow": "5429ccb0-7e97-4ef5-9d65-2bbcf964f9a6", "date_created": "2024-09-25T13:15:05.717Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "d6f73e98-19ef-47d6-8d1f-99d77137d36b", "name": "Image", "key": "image", "type": "directus-labs-ai-image-generation", "position_x": 56, "position_y": 1, "options": {"apiKey": "{{globals.openai_api_key}}", "quality": "hd", "size": "landscape", "prompt": "{{prompt}}"}, "resolve": "bc71ca4e-d979-4ef9-a449-af100a7e1b3b", "reject": null, "flow": "d4bbac48-a444-49e0-aedb-9af5273b88df", "date_created": "2024-09-13T04:12:38.623Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "d7f64e04-ab43-4d77-b8e8-379b41af2d3a", "name": "Format Payload", "key": "format_payload", "type": "exec", "position_x": 73, "position_y": 1, "options": {"code": "/**\n * Automatic Redirect Generator for Content URL Changes\n * \n * This function generates redirect objects when published content URLs change.\n * It works with both posts and pages collections to maintain SEO value\n * and prevent broken links when content is moved or renamed.\n */\nconst map = {\n  // Configuration for blog posts\n  posts: {\n    slugField: \"slug\",        // Field that contains the URL identifier for posts\n    routePrefix: \"/blog/\",    // Prefix added to all post URLs (e.g., /blog/my-post)\n  },\n  // Configuration for pages\n  pages: {\n    slugField: \"permalink\",   // Field that contains the URL identifier for pages\n    routePrefix: \"\",          // No prefix for pages (e.g., /about-us)\n  },\n};\n\n/**\n * Flow function executed when content URL changes\n * @param {Object} data - Data object from Directus Flow\n * @param {Object} data.$trigger - Information about what triggered the flow\n * @param {string} data.$trigger.collection - Collection name (posts/pages)\n * @param {Object} data.$trigger.payload - New data being saved\n * @param {Object} data.item - Previous version of the item before changes\n * @returns {Object} Redirect configuration object\n */\nmodule.exports = function (data) {\n  // Get the collection configuration based on whether this is a page or post\n  const collection = map[data.$trigger.collection];\n\n  const oldSlug = data.item[collection.slugField];\n  const newSlug = data.$trigger.payload[collection.slugField];\n  \n  // Build the old URL by combining the route prefix with the previous slug/permalink\n  const oldUrl = `${collection.routePrefix}${oldSlug}`;\n  \n  // Build the new URL by combining the route prefix with the new slug/permalink\n  const newUrl = `${collection.routePrefix}${newSlug}`;\n    \n  // Add a note\n  const note = `Created by Redirect automation on ${new Date().toLocaleDateString(\"en-US\")} at ${new Date().toLocaleTimeString(\"en-US\")} when a ${data.$trigger.collection} ${collection.slugField} was updated from \"${oldSlug}\" to \"${newSlug}\"`\n  \n  // Return a redirect object that will be saved to the redirects collection\n  return {\n    response_code: 301,       // Permanent redirect (best for SEO)\n    url_from: oldUrl,         // Where users were going before\n    url_to: newUrl,           // Where they should be sent now\n    note,\n  };\n};"}, "resolve": "f6b8abc3-20bd-4d05-9bec-b7c759559e13", "reject": null, "flow": "c62ceaa5-bd09-4158-8329-98792debc5d5", "date_created": "2025-05-02T20:47:12.265Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "dc181cb9-3853-41b9-98bf-6901472bc1af", "name": "Get User", "key": "get_user", "type": "item-read", "position_x": 57, "position_y": 1, "options": {"permissions": "$full", "collection": "directus_users", "key": ["{{$accountability.user}}"], "query": {"fields": ["id", "first_name", "last_name", "email"]}}, "resolve": "4b46b1e9-1ca0-47c3-86a8-68826136664e", "reject": null, "flow": "3172d021-0b0f-4d4d-bcca-c5c46eb8fadf", "date_created": "2025-04-02T19:12:25.011Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "dff1a702-bcc0-4528-905e-309feb880111", "name": "Format", "key": "format", "type": "exec", "position_x": 19, "position_y": 18, "options": {"code": "/**\n * Prepares email template data by matching trigger values with form fields\n * @param {Object} trigger - The trigger object containing payload values\n * @param {Object} form - The form object containing fields and email templates\n * @returns {Array} Array of prepared email template objects\n * @throws {Error} If required data is missing or invalid\n */\nfunction prepareEmailTemplateData(trigger, form) {\n  // Validate input parameters\n  if (!trigger?.payload?.values || !Array.isArray(trigger.payload.values)) {\n    throw new Error('Invalid trigger payload values');\n  }\n  \n  if (!form?.fields || !Array.isArray(form.fields)) {\n    throw new Error('Invalid form fields');\n  }\n  \n  if (!form?.emails || !Array.isArray(form.emails)) {\n    throw new Error('Invalid form emails');\n  }\n\n  // Create an object to store the field name-value pairs\n  const data = {};\n  \n  // Iterate through the trigger values and match them with form fields\n  trigger.payload.values.forEach(item => {\n    if (!item || typeof item !== 'object') {\n      throw new Error('Invalid trigger value item');\n    }\n\n    // Ensure required properties exist\n    if (!item.field && !item.field_name) {\n      throw new Error('Missing field identifier in trigger value');\n    }\n    \n    if (item.value === undefined) {\n      throw new Error('Missing value in trigger value');\n    }\n\n    // Find the corresponding field in the form\n    const formField = form.fields.find(field => field.id === item.field);\n    \n    // If a matching field is found, use its name as the key\n    if (formField && formField.name) {\n      data[formField.name] = item.value;\n    } else {\n      // Fallback to using the field_name from the trigger if no match is found\n      data[item.field_name || item.field] = item.value;\n    }\n  });\n\n  // Process and validate all email templates\n  const emailTemplates = form.emails.map(email => {\n    // Validate required email template properties\n    if (!email.subject) {\n      throw new Error('Missing email subject');\n    }\n    \n    if (!email.message) {\n      throw new Error('Missing email message template');\n    }\n    \n    if (!email.to) {\n      throw new Error('Missing email recipient');\n    }\n\n    // Normalize 'to' field to always be an array\n    const toField = Array.isArray(email.to) ? email.to : [email.to];\n    \n    // Validate each email address\n    toField.forEach(recipient => {\n      if (typeof recipient !== 'string' || !recipient.trim()) {\n        throw new Error('Invalid email recipient');\n      }\n    });\n\n    return {\n      to: toField,\n      subject: email.subject,\n      template: email.message,\n      data: data\n    };\n  });\n\n  return emailTemplates;\n}\n\nmodule.exports = function(data) {\n  if (!data?.$trigger || !data?.form) {\n    throw new Error('Missing required data');\n  }\n  \n  return prepareEmailTemplateData(data.$trigger, data.form);\n};"}, "resolve": "fbf696ea-c6af-4230-8f62-c663beebd6d9", "reject": null, "flow": "61757ab0-b139-4079-b5eb-4e05bb8142ac", "date_created": "2024-09-13T03:41:36.144Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "e12b95db-a408-4e7e-b30b-264f7fa8baa3", "name": "Send", "key": "send", "type": "trigger", "position_x": 59, "position_y": 1, "options": {"flow": "7c8732cd-9d9e-44be-a3f6-89c0d42c7687", "payload": "{{ render }}"}, "resolve": null, "reject": null, "flow": "61757ab0-b139-4079-b5eb-4e05bb8142ac", "date_created": "2024-09-25T13:33:39.709Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "e6c50f84-e229-4f15-8119-c7708256e825", "name": "Update", "key": "update", "type": "item-update", "position_x": 73, "position_y": 1, "options": {"collection": "posts", "permissions": "$full", "key": ["{{$trigger.body.keys}}"], "payload": "{{format}}"}, "resolve": null, "reject": null, "flow": "5915dd55-fff8-4d47-b48c-a0e42e5033c1", "date_created": "2024-09-13T03:52:01.490Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "edc5e1ae-a3ed-45af-ae77-10409a66cd03", "name": "Format", "key": "format", "type": "exec", "position_x": 37, "position_y": 1, "options": {"code": "module.exports = function(data) {\n\treturn {\n        ...data.$trigger,\n        body: data.render.template\n    }\n}"}, "resolve": null, "reject": null, "flow": "5429ccb0-7e97-4ef5-9d65-2bbcf964f9a6", "date_created": "2024-09-25T13:31:35.265Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "f491233d-f942-4c8a-bc9d-9074fce45844", "name": "Read Item", "key": "item", "type": "item-read", "position_x": 37, "position_y": 1, "options": {"permissions": "$trigger", "emitEvents": false, "collection": "{{$trigger.collection}}", "key": "{{$trigger.keys}}"}, "resolve": "2d441500-54d0-4519-a813-b16e0bbf1c08", "reject": null, "flow": "c62ceaa5-bd09-4158-8329-98792debc5d5", "date_created": "2025-05-02T20:33:51.968Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "f6ae03f7-014d-43d1-8d69-cf122da302c0", "name": "Send Email", "key": "send_email", "type": "mail", "position_x": 37, "position_y": 19, "options": {"to": ["{{get_editor.email}}"], "subject": "🧐 Review Request: {{get_post.title}} from {{get_user.first_name}} {{ get_user.last_name}}", "body": "Hi {{ get_editor.first_name }},\n\n{{ get_user.first_name }} {{ get_user.last_name}} has requested your review on the following content:\n\n[{{ $trigger.body.collection }} – **{{ get_post.title }}**]({{globals.directus_url}}/admin/content/{{$trigger.body.collection}}/{{$trigger.body.keys[0]}})\n\n**Comments:**\n{{ $trigger.body.comments }}\n\n---\n\nPlease review to schedule for publishing or request changes.", "replyTo": ["{{get_user.email}}"]}, "resolve": null, "reject": null, "flow": "3172d021-0b0f-4d4d-bcca-c5c46eb8fadf", "date_created": "2025-04-02T19:04:55.879Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "f6b8abc3-20bd-4d05-9bec-b7c759559e13", "name": "Create Redirect", "key": "create_redirect", "type": "item-create", "position_x": 91, "position_y": 1, "options": {"permissions": "$trigger", "emitEvents": false, "collection": "redirects", "payload": "{{format_payload}}"}, "resolve": null, "reject": null, "flow": "c62ceaa5-bd09-4158-8329-98792debc5d5", "date_created": "2025-05-02T20:37:57.954Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "fbf696ea-c6af-4230-8f62-c663beebd6d9", "name": "Render", "key": "render", "type": "trigger", "position_x": 41, "position_y": 1, "options": {"flow": "5429ccb0-7e97-4ef5-9d65-2bbcf964f9a6", "payload": "{{ format }}"}, "resolve": "e12b95db-a408-4e7e-b30b-264f7fa8baa3", "reject": null, "flow": "61757ab0-b139-4079-b5eb-4e05bb8142ac", "date_created": "2024-09-25T13:29:26.743Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}, {"id": "fd271542-fac2-42d8-aa10-a02520c3753f", "name": "Format", "key": "format", "type": "exec", "position_x": 37, "position_y": 1, "options": {"code": "module.exports = async function(data) {\n    return data.read_globals[0]\n}"}, "resolve": null, "reject": null, "flow": "69e87d0b-df14-4779-bdc8-abc05f2f1e97", "date_created": "2024-02-26T13:03:38.753Z", "user_created": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0"}]