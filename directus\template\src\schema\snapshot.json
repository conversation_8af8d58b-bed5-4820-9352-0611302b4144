{"version": 1, "directus": "11.7.2", "vendor": "postgres", "collections": [{"collection": "ai_prompts", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": "archived", "collapse": "open", "collection": "ai_prompts", "color": null, "display_template": "{{name}}", "group": "website", "hidden": false, "icon": "magic_button", "item_duplication_fields": null, "note": "Store and manage all your prompts for LLMs", "preview_url": null, "singleton": false, "sort": 8, "sort_field": "sort", "translations": null, "unarchive_value": "draft", "versioning": false}, "schema": {"name": "ai_prompts"}}, {"collection": "block_button", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": "archived", "collapse": "open", "collection": "block_button", "color": null, "display_template": "{{label}} - {{type}}", "group": "block_button_group", "hidden": true, "icon": "smart_button", "item_duplication_fields": ["sort", "type", "page", "post", "url", "label", "variant"], "note": null, "preview_url": null, "singleton": false, "sort": 1, "sort_field": "sort", "translations": [{"language": "en-US", "plural": "Buttons", "singular": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>"}], "unarchive_value": "draft", "versioning": false}, "schema": {"name": "block_button"}}, {"collection": "block_button_group", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": "archived", "collapse": "open", "collection": "block_button_group", "color": null, "display_template": "{{buttons.label}}", "group": "blocks", "hidden": true, "icon": "smart_button", "item_duplication_fields": ["sort", "buttons.sort", "buttons.type", "buttons.page", "buttons.post", "buttons.url", "buttons.label", "buttons.variant"], "note": null, "preview_url": null, "singleton": false, "sort": 8, "sort_field": null, "translations": [{"language": "en-US", "plural": "Button Groups", "singular": "Button Group", "translation": "Button Group"}], "unarchive_value": "draft", "versioning": false}, "schema": {"name": "block_button_group"}}, {"collection": "block_form", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "block_form", "color": null, "display_template": "{{form.title}}", "group": "blocks", "hidden": true, "icon": "format_shapes", "item_duplication_fields": ["tagline", "headline", "form"], "note": null, "preview_url": null, "singleton": false, "sort": 3, "sort_field": null, "translations": [{"language": "en-US", "translation": "Form"}], "unarchive_value": null, "versioning": false}, "schema": {"name": "block_form"}}, {"collection": "block_gallery", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "block_gallery", "color": null, "display_template": "{{headline}}", "group": "blocks", "hidden": true, "icon": "grid_view", "item_duplication_fields": ["tagline", "headline", "items.block_gallery", "items.directus_file", "items.sort"], "note": null, "preview_url": null, "singleton": false, "sort": 5, "sort_field": null, "translations": [{"language": "en-US", "plural": "Gallery", "singular": "Gallery", "translation": "Gallery"}], "unarchive_value": null, "versioning": false}, "schema": {"name": "block_gallery"}}, {"collection": "block_gallery_items", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "block_gallery_items", "color": null, "display_template": null, "group": "block_gallery", "hidden": true, "icon": "gallery_thumbnail", "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": false, "sort": 1, "sort_field": null, "translations": [{"language": "en-US", "translation": "Gallery Items"}], "unarchive_value": null, "versioning": false}, "schema": {"name": "block_gallery_items"}}, {"collection": "block_hero", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "block_hero", "color": null, "display_template": "{{headline}}", "group": "blocks", "hidden": true, "icon": "aspect_ratio", "item_duplication_fields": ["tagline", "description", "headline", "image", "layout", "button_group.sort", "button_group.buttons.sort", "button_group.buttons.type", "button_group.buttons.page", "button_group.buttons.post", "button_group.buttons.url", "button_group.buttons.label", "button_group.buttons.variant"], "note": null, "preview_url": null, "singleton": false, "sort": 1, "sort_field": null, "translations": [{"language": "en-US", "plural": "Hero", "singular": "Hero", "translation": "Hero"}], "unarchive_value": null, "versioning": false}, "schema": {"name": "block_hero"}}, {"collection": "block_posts", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "block_posts", "color": null, "display_template": "{{headline}}", "group": "blocks", "hidden": true, "icon": "signpost", "item_duplication_fields": ["tagline", "headline", "collection", "limit"], "note": null, "preview_url": null, "singleton": false, "sort": 4, "sort_field": null, "translations": [{"language": "en-US", "translation": "Posts"}], "unarchive_value": null, "versioning": false}, "schema": {"name": "block_posts"}}, {"collection": "block_pricing", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "block_pricing", "color": null, "display_template": "{{headline}}", "group": "blocks", "hidden": true, "icon": "attach_money", "item_duplication_fields": ["tagline", "headline", "pricing_cards.title", "pricing_cards.badge", "pricing_cards.price", "pricing_cards.is_highlighted", "pricing_cards.features", "pricing_cards.description", "pricing_cards.sort", "pricing_cards.button.type", "pricing_cards.button.sort", "pricing_cards.button.page", "pricing_cards.button.post", "pricing_cards.button.url", "pricing_cards.button.label", "pricing_cards.button.variant"], "note": null, "preview_url": null, "singleton": false, "sort": 7, "sort_field": null, "translations": [{"language": "en-US", "translation": "Pricing"}], "unarchive_value": null, "versioning": false}, "schema": {"name": "block_pricing"}}, {"collection": "block_pricing_cards", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "block_pricing_cards", "color": null, "display_template": null, "group": "block_pricing", "hidden": true, "icon": "price_change", "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": false, "sort": 1, "sort_field": null, "translations": [{"language": "en-US", "translation": "Pricing Cards"}], "unarchive_value": null, "versioning": false}, "schema": {"name": "block_pricing_cards"}}, {"collection": "block_richtext", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "block_richtext", "color": null, "display_template": "{{headline}}", "group": "blocks", "hidden": true, "icon": "format_color_text", "item_duplication_fields": ["tagline", "headline", "content", "alignment"], "note": null, "preview_url": null, "singleton": false, "sort": 2, "sort_field": null, "translations": [{"language": "en-US", "plural": "Rich Text", "singular": "Rich Text", "translation": "Rich Text"}], "unarchive_value": null, "versioning": false}, "schema": {"name": "block_richtext"}}, {"collection": "blocks", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "closed", "collection": "blocks", "color": null, "display_template": null, "group": null, "hidden": true, "icon": "content_copy", "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": false, "sort": 2, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}}, {"collection": "form_fields", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "form_fields", "color": null, "display_template": "{{name}} • {{type}}", "group": "forms", "hidden": true, "icon": "input", "item_duplication_fields": null, "note": "Individual fields for each form", "preview_url": null, "singleton": false, "sort": 1, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}, "schema": {"name": "form_fields"}}, {"collection": "form_submission_values", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "form_submission_values", "color": null, "display_template": null, "group": "form_submissions", "hidden": true, "icon": "list", "item_duplication_fields": null, "note": "Individual responses to each form field within a form submission", "preview_url": null, "singleton": false, "sort": 1, "sort_field": null, "translations": [{"language": "en-US", "translation": "Submission Values"}], "unarchive_value": null, "versioning": false}, "schema": {"name": "form_submission_values"}}, {"collection": "form_submissions", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "form_submissions", "color": null, "display_template": "{{timestamp}} • {{form}}", "group": "website", "hidden": false, "icon": "inbox", "item_duplication_fields": null, "note": "Data for all form responses", "preview_url": null, "singleton": false, "sort": 4, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}, "schema": {"name": "form_submissions"}}, {"collection": "forms", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": "is_active", "archive_value": "false", "collapse": "open", "collection": "forms", "color": null, "display_template": "{{title}}", "group": "website", "hidden": false, "icon": "text_format", "item_duplication_fields": ["title", "submit_label", "on_success", "success_message", "success_redirect_url", "status", "fields", "emails"], "note": "Dynamic forms", "preview_url": null, "singleton": false, "sort": 3, "sort_field": "sort", "translations": null, "unarchive_value": "true", "versioning": false}, "schema": {"name": "forms"}}, {"collection": "globals", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "globals", "color": null, "display_template": null, "group": "website", "hidden": false, "icon": "south_america", "item_duplication_fields": null, "note": "Site-wide settings", "preview_url": null, "singleton": true, "sort": 6, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}, "schema": {"name": "globals"}}, {"collection": "navigation", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": "is_active", "archive_value": "false", "collapse": "open", "collection": "navigation", "color": null, "display_template": null, "group": "website", "hidden": false, "icon": "menu_open", "item_duplication_fields": ["title", "status", "items.title", "items.type", "items.url", "items.children", "items.parent", "items.page", "items.post"], "note": "Dynamic menus for your site", "preview_url": null, "singleton": false, "sort": 5, "sort_field": null, "translations": null, "unarchive_value": "true", "versioning": false}, "schema": {"name": "navigation"}}, {"collection": "navigation_items", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "navigation_items", "color": null, "display_template": "{{title}}", "group": "navigation", "hidden": true, "icon": "navigate_next", "item_duplication_fields": ["children", "has_children", "image", "label", "navigation", "open_in_new_tab", "title", "type", "url", "post", "page", "sort"], "note": "Individual menu items", "preview_url": null, "singleton": false, "sort": 1, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}, "schema": {"name": "navigation_items"}}, {"collection": "page_blocks", "meta": {"accountability": "all", "archive_app_filter": false, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "page_blocks", "color": null, "display_template": null, "group": "pages", "hidden": true, "icon": "code_blocks", "item_duplication_fields": ["page", "sort", "item:block_hero.tagline", "item:block_hero.headline", "item:block_hero.description", "item:block_hero.button_group.sort", "item:block_hero.button_group.buttons.sort", "item:block_hero.button_group.buttons.type", "item:block_hero.button_group.buttons.page", "item:block_hero.button_group.buttons.post", "item:block_hero.button_group.buttons.url", "item:block_hero.button_group.buttons.label", "item:block_hero.button_group.buttons.variant", "item:block_hero.image", "item:block_richtext.tagline", "item:block_richtext.headline", "item:block_richtext.content", "item:block_richtext.alignment", "item:block_form.tagline", "item:block_form.headline", "item:block_form.form", "item:block_posts.tagline", "item:block_posts.headline", "item:block_posts.collection", "item:block_gallery.tagline", "item:block_gallery.headline", "item:block_gallery.items", "item:block_pricing.tagline", "item:block_pricing.headline", "item:block_pricing.pricing_cards.title", "item:block_pricing.pricing_cards.badge", "item:block_pricing.pricing_cards.price", "item:block_pricing.pricing_cards.is_highlighted", "item:block_pricing.pricing_cards.description", "item:block_pricing.pricing_cards.features", "item:block_pricing.pricing_cards.button.sort", "item:block_pricing.pricing_cards.button.type", "item:block_pricing.pricing_cards.button.page", "item:block_pricing.pricing_cards.button.post", "item:block_pricing.pricing_cards.button.url", "item:block_pricing.pricing_cards.button.variant", "item:block_pricing.pricing_cards.button.label", "item:block_pricing.pricing_cards.sort", "collection", "hide_block", "background"], "note": null, "preview_url": null, "singleton": false, "sort": 3, "sort_field": "sort", "translations": null, "unarchive_value": null, "versioning": false}, "schema": {"name": "page_blocks"}}, {"collection": "pages", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": "status", "archive_value": "archived", "collapse": "open", "collection": "pages", "color": null, "display_template": "{{title}}", "group": "website", "hidden": false, "icon": "web_asset", "item_duplication_fields": ["sort", "title", "permalink", "status", "published_at", "seo", "blocks.sort", "blocks.page", "blocks.item:block_hero.tagline", "blocks.item:block_hero.headline", "blocks.item:block_hero.description", "blocks.item:block_hero.button_group.sort", "blocks.item:block_hero.button_group.buttons.sort", "blocks.item:block_hero.button_group.buttons.type", "blocks.item:block_hero.button_group.buttons.page", "blocks.item:block_hero.button_group.buttons.post", "blocks.item:block_hero.button_group.buttons.url", "blocks.item:block_hero.button_group.buttons.label", "blocks.item:block_hero.button_group.buttons.variant", "blocks.item:block_hero.image", "blocks.item:block_hero.layout", "blocks.item:block_richtext.tagline", "blocks.item:block_richtext.headline", "blocks.item:block_richtext.content", "blocks.item:block_richtext.alignment", "blocks.item:block_form.tagline", "blocks.item:block_form.headline", "blocks.item:block_form.form", "blocks.item:block_posts.tagline", "blocks.item:block_posts.headline", "blocks.item:block_posts.collection", "blocks.item:block_posts.limit", "blocks.item:block_gallery.tagline", "blocks.item:block_gallery.headline", "blocks.item:block_gallery.items.block_gallery", "blocks.item:block_gallery.items.directus_file", "blocks.item:block_gallery.items.sort", "blocks.item:block_pricing.tagline", "blocks.item:block_pricing.headline", "blocks.item:block_pricing.pricing_cards.title", "blocks.item:block_pricing.pricing_cards.badge", "blocks.item:block_pricing.pricing_cards.price", "blocks.item:block_pricing.pricing_cards.is_highlighted", "blocks.item:block_pricing.pricing_cards.description", "blocks.item:block_pricing.pricing_cards.features", "blocks.item:block_pricing.pricing_cards.button.sort", "blocks.item:block_pricing.pricing_cards.button.type", "blocks.item:block_pricing.pricing_cards.button.page", "blocks.item:block_pricing.pricing_cards.button.post", "blocks.item:block_pricing.pricing_cards.button.url", "blocks.item:block_pricing.pricing_cards.button.label", "blocks.item:block_pricing.pricing_cards.button.variant", "blocks.item:block_pricing.pricing_cards.sort", "blocks.collection", "blocks.hide_block", "blocks.background"], "note": "Dynamic page builder", "preview_url": "http://localhost:3000{{permalink}}?preview=true&version={{$version}}", "singleton": false, "sort": 1, "sort_field": "sort", "translations": null, "unarchive_value": "draft", "versioning": true}, "schema": {"name": "pages"}}, {"collection": "posts", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": "status", "archive_value": "archived", "collapse": "open", "collection": "posts", "color": null, "display_template": "{{title}}", "group": "website", "hidden": false, "icon": "article", "item_duplication_fields": ["title", "slug", "author", "status", "image", "description", "content", "seo"], "note": "Individual blog posts", "preview_url": "http://localhost:3000/blog/{{slug}}?preview=true&version={{$version}}", "singleton": false, "sort": 2, "sort_field": "sort", "translations": null, "unarchive_value": "draft", "versioning": true}, "schema": {"name": "posts"}}, {"collection": "redirects", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "redirects", "color": null, "display_template": "{{url_from}} • {{response_code}}", "group": "website", "hidden": false, "icon": "turn_sharp_right", "item_duplication_fields": null, "note": "Manage URL redirects for your website", "preview_url": null, "singleton": false, "sort": 7, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}, "schema": {"name": "redirects"}}, {"collection": "website", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "website", "color": null, "display_template": null, "group": null, "hidden": false, "icon": "folder_special", "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": false, "sort": 1, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}}], "fields": [{"collection": "ai_prompts", "field": "id", "type": "uuid", "meta": {"collection": "ai_prompts", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": ["uuid"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "id", "table": "ai_prompts", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "ai_prompts", "field": "sort", "type": "integer", "meta": {"collection": "ai_prompts", "conditions": null, "display": null, "display_options": null, "field": "sort", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "sort", "table": "ai_prompts", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "ai_prompts", "field": "name", "type": "string", "meta": {"collection": "ai_prompts", "conditions": null, "display": "formatted-value", "display_options": {"font": "monospace"}, "field": "name", "group": null, "hidden": false, "interface": "input", "note": "Unique name for the prompt. Use names like \"create-article\" or \"generate-product-description\".", "options": {"slug": true, "trim": true}, "readonly": false, "required": true, "sort": 8, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "name", "table": "ai_prompts", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": true, "is_indexed": true, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "ai_prompts", "field": "status", "type": "string", "meta": {"collection": "ai_prompts", "conditions": null, "display": "labels", "display_options": {"choices": [{"color": "#A2B5CD", "icon": "draft_orders", "text": "$t:draft", "value": "draft"}, {"color": "#FFA439", "icon": "rate_review", "text": "In Review", "value": "in_review"}, {"color": "#2ECDA7", "icon": "check", "text": "$t:published", "value": "published"}]}, "field": "status", "group": null, "hidden": false, "interface": "select-dropdown", "note": "Is this prompt published and available to use?", "options": {"choices": [{"color": "#A2B5CD", "icon": "draft_orders", "text": "$t:draft", "value": "draft"}, {"color": "#FFA439", "icon": "rate_review", "text": "In Review", "value": "in_review"}, {"color": "#2ECDA7", "icon": "check", "text": "$t:published", "value": "published"}]}, "readonly": false, "required": false, "sort": 9, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "status", "table": "ai_prompts", "data_type": "character varying", "default_value": "draft", "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "ai_prompts", "field": "description", "type": "text", "meta": {"collection": "ai_prompts", "conditions": null, "display": null, "display_options": null, "field": "description", "group": null, "hidden": false, "interface": "input", "note": "Briefly explain what this prompt does in 1-2 sentences.", "options": null, "readonly": false, "required": false, "sort": 10, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "description", "table": "ai_prompts", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "ai_prompts", "field": "messages", "type": "json", "meta": {"collection": "ai_prompts", "conditions": null, "display": "formatted-json-value", "display_options": {"format": "{{ role }} • {{ text }}"}, "field": "messages", "group": null, "hidden": false, "interface": "inline-repeater-interface", "note": "Optional: Define the conversation structure between users and AI. Used to add context and improve outputs.", "options": {"addLabel": "New Message", "fields": [{"field": "role", "meta": {"display": "labels", "display_options": {"choices": [{"icon": "person", "text": "User", "value": "user"}, {"icon": "robot", "text": "Assistant", "value": "assistant"}]}, "field": "role", "interface": "select-dropdown", "note": "Who is speaking in this message.", "options": {"choices": [{"icon": "person", "text": "User", "value": "user"}, {"icon": "robot", "text": "Assistant", "value": "assistant"}]}, "required": true, "type": "string", "width": "full"}, "name": "role", "type": "string"}, {"field": "text", "meta": {"display": "formatted-value", "display_options": {"format": true}, "field": "text", "interface": "input-rich-text-md", "note": "The actual content of the message. You can use {{ curly_braces }} for placeholders that will be replaced with real data.", "required": true, "type": "text", "width": "full"}, "name": "text", "type": "text"}], "showConfirmDiscard": true, "template": "{{ role }} • {{ text }}"}, "readonly": false, "required": false, "sort": 12, "special": ["cast-json"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "messages", "table": "ai_prompts", "data_type": "json", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "ai_prompts", "field": "meta_header_ai_prompts", "type": "alias", "meta": {"collection": "ai_prompts", "conditions": null, "display": null, "display_options": null, "field": "meta_header_ai_prompts", "group": null, "hidden": false, "interface": "super-header", "note": null, "options": {"help": "<h2>AI Prompts Collection Help</h2>\n<h3>Name</h3>\n<p><em>Unique identifier for this prompt</em></p>\n<p>Enter a clear, concise name that describes what this prompt does. Use kebab-case (lowercase with hyphens) for consistency, like \"create-article\" or \"generate-product-description\".</p>\n<h3>Description</h3>\n<p><em>Short description for the prompt to display to users</em></p>\n<p>Briefly explain what this prompt does in 1-2 sentences. Focus on the outcome users can expect when using this prompt. Keep it clear and action-oriented.</p>\n<h3>System Prompt</h3>\n<p><em>Instructions that shape how the AI responds</em></p>\n<p>This is the foundation of your AI prompt. Define the AI's role, capabilities, and constraints here. Be specific about tone, style, and approach. This text isn't visible to end users but significantly impacts output quality.</p>\n<h3>Messages</h3>\n<p><em>Define the conversation structure between users and AI</em></p>\n<h4>Role</h4>\n<p><em>Who is speaking in this message</em></p>\n<p>Select \"User\" for instructions from the human, or \"Assistant\" for example responses from the AI. Most prompt templates begin with a user message.</p>\n<h4>Text</h4>\n<p><em>The actual content of the message</em></p>\n<p>For user messages, include placeholders with double curly braces {{like this}} that will be replaced with real data. For assistant messages, provide example responses that model the desired output format.</p>\n<h3>Status</h3>\n<p><em>Is this prompt published and available to use?</em></p>\n<p>Toggle between draft and published states. Only published prompts are available to users in the content creation interface.</p>\n<h3>Creating Effective AI Prompts</h3>\n<p>Good prompts are clear, specific, and structured. Here are quick tips:</p>\n<ol>\n<li><strong>Be specific about deliverables</strong> - Define exactly what output you want (word count, format, sections).</li>\n<li><strong>Establish context</strong> - Provide relevant background information the AI needs to understand the task.</li>\n<li><strong>Define the audience</strong> - Specify who will read the content and their knowledge level.</li>\n<li><strong>Use placeholders consistently</strong> - Format placeholders with double curly braces {{like this}} for variables.</li>\n<li><strong>Include examples</strong> - Show what good outputs look like to guide the AI's response format.</li>\n<li><strong>Balance guidance with flexibility</strong> - Provide enough direction without over-constraining the AI's ability to generate creative content.</li>\n<li><strong>Test and iterate</strong> - Create your prompt, test the output, and refine based on results.</li>\n</ol>\n<p>Remember: The best prompts clearly communicate what you want while giving the AI enough information to generate high-quality, relevant content.</p>", "subtitle": "{{status}} • Last Updated: {{updated_on}}", "title": "{{name}}"}, "readonly": false, "required": false, "sort": 7, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "ai_prompts", "field": "system_prompt", "type": "text", "meta": {"collection": "ai_prompts", "conditions": null, "display": null, "display_options": null, "field": "system_prompt", "group": null, "hidden": false, "interface": "input-multiline", "note": "Instructions that shape how the AI responds.", "options": null, "readonly": false, "required": false, "sort": 11, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "system_prompt", "table": "ai_prompts", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "ai_prompts", "field": "date_created", "type": "timestamp", "meta": {"collection": "ai_prompts", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 3, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "ai_prompts", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "ai_prompts", "field": "user_created", "type": "uuid", "meta": {"collection": "ai_prompts", "conditions": null, "display": "user", "display_options": null, "field": "user_created", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 4, "special": ["user-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_created", "table": "ai_prompts", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "ai_prompts", "field": "date_updated", "type": "timestamp", "meta": {"collection": "ai_prompts", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 5, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "ai_prompts", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "ai_prompts", "field": "user_updated", "type": "uuid", "meta": {"collection": "ai_prompts", "conditions": null, "display": "user", "display_options": null, "field": "user_updated", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 6, "special": ["user-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_updated", "table": "ai_prompts", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "block_button", "field": "id", "type": "uuid", "meta": {"collection": "block_button", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": ["uuid"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "id", "table": "block_button", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_button", "field": "sort", "type": "integer", "meta": {"collection": "block_button", "conditions": null, "display": null, "display_options": null, "field": "sort", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "sort", "table": "block_button", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_button", "field": "type", "type": "string", "meta": {"collection": "block_button", "conditions": null, "display": "labels", "display_options": {"choices": [{"icon": "web_asset", "text": "Page", "value": "page"}, {"icon": "article", "text": "Post", "value": "post"}, {"icon": "link", "text": "URL", "value": "url"}], "format": true}, "field": "type", "group": null, "hidden": false, "interface": "select-dropdown", "note": "What type of link is this? Page and Post allow you to link to internal content. URL is for external content. Group can contain other menu items.", "options": {"choices": [{"icon": "web_asset", "text": "Page", "value": "page"}, {"icon": "article", "text": "Post", "value": "post"}, {"icon": "link", "text": "URL", "value": "url"}]}, "readonly": false, "required": false, "sort": 8, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "type", "table": "block_button", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_button", "field": "page", "type": "uuid", "meta": {"collection": "block_button", "conditions": [{"hidden": false, "name": "IF type = pages", "options": {"enableCreate": true, "enableSelect": true}, "rule": {"_and": [{"type": {"_eq": "page"}}]}}], "display": "related-values", "display_options": null, "field": "page", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": "The internal page to link to.", "options": {"template": "{{title}} - {{permalink}}"}, "readonly": false, "required": false, "sort": 9, "special": ["m2o"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "page", "table": "block_button", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "pages", "foreign_key_column": "id"}}, {"collection": "block_button", "field": "post", "type": "uuid", "meta": {"collection": "block_button", "conditions": [{"hidden": false, "name": "IF type = post", "options": {"enableCreate": true, "enableSelect": true}, "rule": {"_and": [{"type": {"_eq": "post"}}]}}], "display": "related-values", "display_options": null, "field": "post", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": "The internal post to link to.", "options": {"template": "{{title}} - {{slug}}"}, "readonly": false, "required": false, "sort": 10, "special": ["m2o"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "post", "table": "block_button", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "posts", "foreign_key_column": "id"}}, {"collection": "block_button", "field": "label", "type": "string", "meta": {"collection": "block_button", "conditions": null, "display": null, "display_options": null, "field": "label", "group": null, "hidden": false, "interface": "input", "note": "Text to include on the button.", "options": null, "readonly": false, "required": false, "sort": 12, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "label", "table": "block_button", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_button", "field": "variant", "type": "string", "meta": {"collection": "block_button", "conditions": null, "display": "formatted-value", "display_options": {"format": true}, "field": "variant", "group": null, "hidden": false, "interface": "select-dropdown", "note": "What type of button", "options": {"choices": [{"text": "<PERSON><PERSON><PERSON>", "value": "default"}, {"text": "Outline", "value": "outline"}, {"text": "Soft", "value": "soft"}, {"text": "Ghost", "value": "ghost"}, {"text": "Link", "value": "link"}]}, "readonly": false, "required": false, "sort": 13, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "variant", "table": "block_button", "data_type": "character varying", "default_value": "solid", "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_button", "field": "button_group", "type": "uuid", "meta": {"collection": "block_button", "conditions": null, "display": null, "display_options": null, "field": "button_group", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": "The id of the Button Group this button belongs to.", "options": null, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "button_group", "table": "block_button", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "block_button_group", "foreign_key_column": "id"}}, {"collection": "block_button", "field": "url", "type": "string", "meta": {"collection": "block_button", "conditions": [{"hidden": false, "name": "If type = external", "options": {"clear": false, "font": "sans-serif", "masked": false, "slug": false, "trim": false}, "rule": {"_and": [{"type": {"_eq": "url"}}]}}], "display": "formatted-value", "display_options": {"format": true}, "field": "url", "group": null, "hidden": true, "interface": "input", "note": "The URL to link to. Could be relative (ie `/my-page`) or a full external URL (ie `https://docs.directus.io`)", "options": {"iconLeft": "link", "trim": true}, "readonly": false, "required": false, "sort": 11, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "url", "table": "block_button", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_button", "field": "date_created", "type": "timestamp", "meta": {"collection": "block_button", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 4, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "block_button", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_button", "field": "user_created", "type": "uuid", "meta": {"collection": "block_button", "conditions": null, "display": "user", "display_options": null, "field": "user_created", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 5, "special": ["user-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_created", "table": "block_button", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "block_button", "field": "date_updated", "type": "timestamp", "meta": {"collection": "block_button", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 6, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "block_button", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_button", "field": "user_updated", "type": "uuid", "meta": {"collection": "block_button", "conditions": null, "display": "user", "display_options": null, "field": "user_updated", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 7, "special": ["user-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_updated", "table": "block_button", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "block_button_group", "field": "id", "type": "uuid", "meta": {"collection": "block_button_group", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": ["uuid"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "id", "table": "block_button_group", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_button_group", "field": "sort", "type": "integer", "meta": {"collection": "block_button_group", "conditions": null, "display": null, "display_options": null, "field": "sort", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "sort", "table": "block_button_group", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_button_group", "field": "buttons", "type": "alias", "meta": {"collection": "block_button_group", "conditions": null, "display": "related-values", "display_options": {"template": "{{label}} - {{type}}"}, "field": "buttons", "group": null, "hidden": false, "interface": "list-o2m", "note": "Add individual buttons to the button group.", "options": {"enableLink": true, "template": "{{label}} - {{type}}"}, "readonly": false, "required": false, "sort": 7, "special": ["o2m"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "block_button_group", "field": "date_created", "type": "timestamp", "meta": {"collection": "block_button_group", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 3, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "block_button_group", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_button_group", "field": "user_created", "type": "uuid", "meta": {"collection": "block_button_group", "conditions": null, "display": "user", "display_options": null, "field": "user_created", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 4, "special": ["user-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_created", "table": "block_button_group", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "block_button_group", "field": "date_updated", "type": "timestamp", "meta": {"collection": "block_button_group", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 5, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "block_button_group", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_button_group", "field": "user_updated", "type": "uuid", "meta": {"collection": "block_button_group", "conditions": null, "display": "user", "display_options": null, "field": "user_updated", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 6, "special": ["user-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_updated", "table": "block_button_group", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "block_form", "field": "id", "type": "uuid", "meta": {"collection": "block_form", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": ["uuid"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "block_form", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_form", "field": "form", "type": "uuid", "meta": {"collection": "block_form", "conditions": null, "display": null, "display_options": null, "field": "form", "group": null, "hidden": false, "interface": "select-dropdown-m2o", "note": "Form to show within block", "options": null, "readonly": false, "required": false, "sort": 9, "special": ["m2o"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "form", "table": "block_form", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "forms", "foreign_key_column": "id"}}, {"collection": "block_form", "field": "headline", "type": "text", "meta": {"collection": "block_form", "conditions": null, "display": null, "display_options": null, "field": "headline", "group": null, "hidden": false, "interface": "input", "note": "Larger main headline for this page section.", "options": {"defaultView": {"bearing": 0, "center": {"lat": 0, "lng": 0}, "pitch": 0, "zoom": 0}, "geometryType": "Point"}, "readonly": false, "required": false, "sort": 8, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "headline", "table": "block_form", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_form", "field": "tagline", "type": "string", "meta": {"collection": "block_form", "conditions": null, "display": null, "display_options": null, "field": "tagline", "group": null, "hidden": false, "interface": "input", "note": "Smaller copy shown above the headline to label a section or add extra context.", "options": null, "readonly": false, "required": false, "sort": 7, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "tagline", "table": "block_form", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_form", "field": "meta_header_block_form", "type": "alias", "meta": {"collection": "block_form", "conditions": null, "display": null, "display_options": null, "field": "meta_header_block_form", "group": null, "hidden": false, "interface": "super-header", "note": null, "options": {"help": "<p>Embed interactive forms to collect information from your visitors. Great for:</p>\n<ul>\n<li>Contact forms</li>\n<li>Newsletter signups</li>\n<li>Event registrations</li>\n<li>Feedback collection</li>\n<li>Lead generation</li>\n</ul>\n<p>When adding a form:</p>\n<ul>\n<li>Choose from your pre-configured form templates</li>\n<li>Add a clear headline explaining the form's purpose</li>\n<li>Consider adding a tagline with extra context or instructions</li>\n<li>Ensure all required fields are clearly marked</li>\n</ul>\n<p>You can create or edit forms outside of this block in the <a href=\"/admin/content/forms\">Forms collection.</a></p>", "icon": "format_shapes", "title": "Form Block"}, "readonly": false, "required": false, "sort": 6, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "block_form", "field": "date_created", "type": "timestamp", "meta": {"collection": "block_form", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 2, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "block_form", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_form", "field": "user_created", "type": "uuid", "meta": {"collection": "block_form", "conditions": null, "display": "user", "display_options": null, "field": "user_created", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 3, "special": ["user-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_created", "table": "block_form", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "block_form", "field": "date_updated", "type": "timestamp", "meta": {"collection": "block_form", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 4, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "block_form", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_form", "field": "user_updated", "type": "uuid", "meta": {"collection": "block_form", "conditions": null, "display": "user", "display_options": null, "field": "user_updated", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 5, "special": ["user-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_updated", "table": "block_form", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "block_gallery", "field": "headline", "type": "text", "meta": {"collection": "block_gallery", "conditions": null, "display": null, "display_options": null, "field": "headline", "group": null, "hidden": false, "interface": "input", "note": "Larger main headline for this page section.", "options": {"toolbar": ["italic", "removeformat"]}, "readonly": false, "required": false, "sort": 8, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "headline", "table": "block_gallery", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_gallery", "field": "id", "type": "uuid", "meta": {"collection": "block_gallery", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": ["uuid"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "block_gallery", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_gallery", "field": "items", "type": "alias", "meta": {"collection": "block_gallery", "conditions": null, "display": null, "display_options": null, "field": "items", "group": null, "hidden": false, "interface": "files", "note": "Images to include in the image gallery.", "options": {"folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9"}, "readonly": false, "required": false, "sort": 9, "special": ["files"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "block_gallery", "field": "tagline", "type": "string", "meta": {"collection": "block_gallery", "conditions": null, "display": null, "display_options": null, "field": "tagline", "group": null, "hidden": false, "interface": "input", "note": "Smaller copy shown above the headline to label a section or add extra context.", "options": null, "readonly": false, "required": false, "sort": 7, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "tagline", "table": "block_gallery", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_gallery", "field": "meta_header_block_gallery", "type": "alias", "meta": {"collection": "block_gallery", "conditions": null, "display": null, "display_options": null, "field": "meta_header_block_gallery", "group": null, "hidden": false, "interface": "super-header", "note": null, "options": {"help": "<p>Create visual showcases with multiple images. Ideal for:</p>\n<ul>\n<li>Product portfolios</li>\n<li>Project showcases</li>\n<li>Event photo collections</li>\n<li>Team member galleries</li>\n<li>Before/after demonstrations</li>\n</ul>\n<p>Tips for great galleries:</p>\n<ul>\n<li>Use consistently sized images for the best presentation</li>\n<li>Add descriptive headlines to provide context</li>\n<li>Consider a tagline to explain what visitors are seeing</li>\n<li>Organize images in a logical sequence</li>\n</ul>", "icon": "grid_view", "title": "Gallery Block"}, "readonly": false, "required": false, "sort": 6, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "block_gallery", "field": "date_created", "type": "timestamp", "meta": {"collection": "block_gallery", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 2, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "block_gallery", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_gallery", "field": "user_created", "type": "uuid", "meta": {"collection": "block_gallery", "conditions": null, "display": "user", "display_options": null, "field": "user_created", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 3, "special": ["user-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_created", "table": "block_gallery", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "block_gallery", "field": "date_updated", "type": "timestamp", "meta": {"collection": "block_gallery", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 4, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "block_gallery", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_gallery", "field": "user_updated", "type": "uuid", "meta": {"collection": "block_gallery", "conditions": null, "display": "user", "display_options": null, "field": "user_updated", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 5, "special": ["user-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_updated", "table": "block_gallery", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "block_gallery_items", "field": "id", "type": "uuid", "meta": {"collection": "block_gallery_items", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": ["uuid"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "id", "table": "block_gallery_items", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_gallery_items", "field": "block_gallery", "type": "uuid", "meta": {"collection": "block_gallery_items", "conditions": null, "display": null, "display_options": null, "field": "block_gallery", "group": null, "hidden": true, "interface": null, "note": "The id of the gallery block this item belongs to.", "options": null, "readonly": false, "required": false, "sort": 7, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "block_gallery", "table": "block_gallery_items", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "block_gallery", "foreign_key_column": "id"}}, {"collection": "block_gallery_items", "field": "directus_file", "type": "uuid", "meta": {"collection": "block_gallery_items", "conditions": null, "display": null, "display_options": null, "field": "directus_file", "group": null, "hidden": true, "interface": null, "note": "The id of the file included in the gallery.", "options": null, "readonly": false, "required": false, "sort": 8, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "directus_file", "table": "block_gallery_items", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_files", "foreign_key_column": "id"}}, {"collection": "block_gallery_items", "field": "sort", "type": "integer", "meta": {"collection": "block_gallery_items", "conditions": null, "display": null, "display_options": null, "field": "sort", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "sort", "table": "block_gallery_items", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_gallery_items", "field": "date_created", "type": "timestamp", "meta": {"collection": "block_gallery_items", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 3, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "block_gallery_items", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_gallery_items", "field": "user_created", "type": "uuid", "meta": {"collection": "block_gallery_items", "conditions": null, "display": "user", "display_options": null, "field": "user_created", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 4, "special": ["user-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_created", "table": "block_gallery_items", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "block_gallery_items", "field": "date_updated", "type": "timestamp", "meta": {"collection": "block_gallery_items", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 5, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "block_gallery_items", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_gallery_items", "field": "user_updated", "type": "uuid", "meta": {"collection": "block_gallery_items", "conditions": null, "display": "user", "display_options": null, "field": "user_updated", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 6, "special": ["user-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_updated", "table": "block_gallery_items", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "block_hero", "field": "headline", "type": "text", "meta": {"collection": "block_hero", "conditions": null, "display": "raw", "display_options": {"background": "#A2B5CD"}, "field": "headline", "group": null, "hidden": false, "interface": "input", "note": "Larger main headline for this page section.", "options": {"defaultView": {"bearing": 0, "center": {"lat": 0, "lng": 0}, "pitch": 0, "zoom": 0}, "geometryType": "Point"}, "readonly": false, "required": false, "sort": 8, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "headline", "table": "block_hero", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_hero", "field": "id", "type": "uuid", "meta": {"collection": "block_hero", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": ["uuid"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "block_hero", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_hero", "field": "image", "type": "uuid", "meta": {"collection": "block_hero", "conditions": null, "display": null, "display_options": null, "field": "image", "group": null, "hidden": false, "interface": "file-image", "note": "Featured image in the hero.", "options": {"crop": false, "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9"}, "readonly": false, "required": false, "sort": 12, "special": ["file"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "image", "table": "block_hero", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_files", "foreign_key_column": "id"}}, {"collection": "block_hero", "field": "button_group", "type": "uuid", "meta": {"collection": "block_hero", "conditions": null, "display": "related-values", "display_options": {"template": "{{buttons.label}}"}, "field": "button_group", "group": null, "hidden": false, "interface": "select-dropdown-m2o", "note": "Action buttons that show below headline and description.", "options": {"createRelatedItem": "always", "enableLink": true, "enableSelect": false, "template": "{{buttons.label}}"}, "readonly": false, "required": false, "sort": 10, "special": ["m2o"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "button_group", "table": "block_hero", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "block_button_group", "foreign_key_column": "id"}}, {"collection": "block_hero", "field": "description", "type": "text", "meta": {"collection": "block_hero", "conditions": null, "display": null, "display_options": null, "field": "description", "group": null, "hidden": false, "interface": "input-multiline", "note": "Supporting copy that shows below the headline.", "options": {"folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9"}, "readonly": false, "required": false, "sort": 9, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "description", "table": "block_hero", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_hero", "field": "tagline", "type": "string", "meta": {"collection": "block_hero", "conditions": null, "display": null, "display_options": null, "field": "tagline", "group": null, "hidden": false, "interface": "input", "note": "Smaller copy shown above the headline to label a section or add extra context.", "options": null, "readonly": false, "required": false, "sort": 7, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "tagline", "table": "block_hero", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_hero", "field": "meta_divider_block_image", "type": "alias", "meta": {"collection": "block_hero", "conditions": null, "display": null, "display_options": null, "field": "meta_divider_block_image", "group": null, "hidden": false, "interface": "presentation-divider", "note": null, "options": {"color": "#A2B5CD", "icon": "image", "inlineTitle": true}, "readonly": false, "required": false, "sort": 11, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "block_hero", "field": "layout", "type": "string", "meta": {"collection": "block_hero", "conditions": null, "display": "labels", "display_options": {"choices": [{"icon": "format_image_left", "text": "Image Left", "value": "image_left"}, {"icon": "image", "text": "Image Center", "value": "image_center"}, {"icon": "format_image_right", "text": "Image Right", "value": "image_right"}], "format": true}, "field": "layout", "group": null, "hidden": false, "interface": "radio-cards-interface", "note": "The layout for the component. You can set the image to display left, right, or in the center of page..", "options": {"choices": [{"icon": "format_image_left", "icon_type": "image", "image": "d5a1290f-8819-4e7c-b292-bffe5b1c8274.jpg", "text": "Image Left", "value": "image_left"}, {"icon": "image", "icon_type": "image", "image": "8a652e52-a275-4dde-9fc5-edf2188afe56.jpg", "text": "Image Center", "value": "image_center"}, {"icon": "format_image_right", "icon_type": "image", "image": "fe7c7e04-5aac-4370-8bbd-6fd578d26ea1.jpg", "text": "Image Right", "value": "image_right"}], "gridSize": 3}, "readonly": false, "required": false, "sort": 13, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "layout", "table": "block_hero", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_hero", "field": "meta_header_block_hero", "type": "alias", "meta": {"collection": "block_hero", "conditions": null, "display": null, "display_options": null, "field": "meta_header_block_hero", "group": null, "hidden": false, "interface": "super-header", "note": null, "options": {"help": "<p>Creates a prominent section at the top of your page that immediately captures attention. Ideal for:</p>\n<ul>\n<li>Main page headers with impactful headlines</li>\n<li>Showcasing key messages with supporting text</li>\n<li>Featuring high-quality images that support your message</li>\n<li>Adding clear call-to-action buttons</li>\n</ul>\n<p>Tips for effective heroes:</p>\n<ul>\n<li>Keep headlines clear and concise (recommended 2-8 words)</li>\n<li>Use high-resolution images that work well with overlay text</li>\n<li>Consider image placement (left/center/right) based on your headline length</li>\n<li>Add a succinct tagline to provide additional context</li>\n</ul>", "icon": "aspect_ratio", "title": "Hero Block"}, "readonly": false, "required": false, "sort": 6, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "block_hero", "field": "date_created", "type": "timestamp", "meta": {"collection": "block_hero", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 2, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "block_hero", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_hero", "field": "user_created", "type": "uuid", "meta": {"collection": "block_hero", "conditions": null, "display": "user", "display_options": null, "field": "user_created", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 3, "special": ["user-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_created", "table": "block_hero", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "block_hero", "field": "date_updated", "type": "timestamp", "meta": {"collection": "block_hero", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 4, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "block_hero", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_hero", "field": "user_updated", "type": "uuid", "meta": {"collection": "block_hero", "conditions": null, "display": "user", "display_options": null, "field": "user_updated", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 5, "special": ["user-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_updated", "table": "block_hero", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "block_posts", "field": "id", "type": "uuid", "meta": {"collection": "block_posts", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": ["uuid"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "block_posts", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_posts", "field": "headline", "type": "text", "meta": {"collection": "block_posts", "conditions": null, "display": null, "display_options": null, "field": "headline", "group": null, "hidden": false, "interface": "input", "note": "Larger main headline for this page section.", "options": {"defaultView": {"bearing": 0, "center": {"lat": 0, "lng": 0}, "pitch": 0, "zoom": 0}, "geometryType": "Point"}, "readonly": false, "required": false, "sort": 8, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "headline", "table": "block_posts", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_posts", "field": "collection", "type": "string", "meta": {"collection": "block_posts", "conditions": null, "display": null, "display_options": null, "field": "collection", "group": null, "hidden": false, "interface": "select-radio", "note": "The collection of content to fetch and display on the page within this block.", "options": {"choices": [{"text": "Posts", "value": "posts"}]}, "readonly": false, "required": true, "sort": 9, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "collection", "table": "block_posts", "data_type": "character varying", "default_value": "posts", "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_posts", "field": "tagline", "type": "string", "meta": {"collection": "block_posts", "conditions": null, "display": null, "display_options": null, "field": "tagline", "group": null, "hidden": false, "interface": "input", "note": "Smaller copy shown above the headline to label a section or add extra context.", "options": null, "readonly": false, "required": false, "sort": 7, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "tagline", "table": "block_posts", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_posts", "field": "limit", "type": "integer", "meta": {"collection": "block_posts", "conditions": null, "display": null, "display_options": null, "field": "limit", "group": null, "hidden": false, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 10, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "limit", "table": "block_posts", "data_type": "integer", "default_value": 6, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_posts", "field": "meta_header_block_posts", "type": "alias", "meta": {"collection": "block_posts", "conditions": null, "display": null, "display_options": null, "field": "meta_header_block_posts", "group": null, "hidden": false, "interface": "super-header", "note": null, "options": {"help": "<p>Automatically displays your most recent blog posts in chronological order. Perfect for:</p>\n<ul>\n<li>Showing latest articles on your homepage</li>\n<li>Keeping your content fresh and up-to-date</li>\n<li>Creating dynamic content sections</li>\n<li>Showcasing your recent writing</li>\n</ul>\n<p>You can:</p>\n<ul>\n<li>Set the number of posts to display</li>\n<li>Add a custom headline and tagline to label the section</li>\n<li>Trust the system to automatically keep your content current</li>\n</ul>\n<p>The posts will automatically update as you publish new content, ensuring your pages always show your latest articles.</p>", "icon": "signpost", "title": "Posts Block"}, "readonly": false, "required": false, "sort": 6, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "block_posts", "field": "date_created", "type": "timestamp", "meta": {"collection": "block_posts", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 2, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "block_posts", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_posts", "field": "user_created", "type": "uuid", "meta": {"collection": "block_posts", "conditions": null, "display": "user", "display_options": null, "field": "user_created", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 3, "special": ["user-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_created", "table": "block_posts", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "block_posts", "field": "date_updated", "type": "timestamp", "meta": {"collection": "block_posts", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 4, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "block_posts", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_posts", "field": "user_updated", "type": "uuid", "meta": {"collection": "block_posts", "conditions": null, "display": "user", "display_options": null, "field": "user_updated", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 5, "special": ["user-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_updated", "table": "block_posts", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "block_pricing", "field": "id", "type": "uuid", "meta": {"collection": "block_pricing", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": ["uuid"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "block_pricing", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_pricing", "field": "headline", "type": "text", "meta": {"collection": "block_pricing", "conditions": null, "display": null, "display_options": null, "field": "headline", "group": null, "hidden": false, "interface": "input", "note": "Larger main headline for this page section.", "options": {"defaultView": {"bearing": 0, "center": {"lat": 0, "lng": 0}, "pitch": 0, "zoom": 0}, "geometryType": "Point"}, "readonly": false, "required": false, "sort": 8, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "headline", "table": "block_pricing", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_pricing", "field": "pricing_cards", "type": "alias", "meta": {"collection": "block_pricing", "conditions": null, "display": null, "display_options": null, "field": "pricing_cards", "group": null, "hidden": false, "interface": "list-o2m", "note": "The individual pricing cards to display.", "options": {"template": "{{title}} • {{price}}"}, "readonly": false, "required": false, "sort": 9, "special": ["o2m"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "block_pricing", "field": "tagline", "type": "string", "meta": {"collection": "block_pricing", "conditions": null, "display": null, "display_options": null, "field": "tagline", "group": null, "hidden": false, "interface": "input", "note": "Smaller copy shown above the headline to label a section or add extra context.", "options": null, "readonly": false, "required": false, "sort": 7, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "tagline", "table": "block_pricing", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_pricing", "field": "meta_header_block_pricing", "type": "alias", "meta": {"collection": "block_pricing", "conditions": null, "display": null, "display_options": null, "field": "meta_header_block_pricing", "group": null, "hidden": false, "interface": "super-header", "note": null, "options": {"help": "<p>Display your product or service pricing options in a clear, comparative format. Use this when:</p>\n<ul>\n<li>Showcasing different service tiers</li>\n<li>Highlighting feature differences between plans</li>\n<li>Promoting special offers or featured plans</li>\n<li>Presenting subscription options</li>\n</ul>\n<p>Best practices:</p>\n<ul>\n<li>Use clear, benefit-focused plan names</li>\n<li>Highlight your recommended or most popular option</li>\n<li>Keep feature lists scannable and easy to compare</li>\n<li>Include clear call-to-action buttons for each plan</li>\n</ul>", "icon": "attach_money", "title": "Pricing Block"}, "readonly": false, "required": false, "sort": 6, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "block_pricing", "field": "date_created", "type": "timestamp", "meta": {"collection": "block_pricing", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 2, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "block_pricing", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_pricing", "field": "user_created", "type": "uuid", "meta": {"collection": "block_pricing", "conditions": null, "display": "user", "display_options": null, "field": "user_created", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 3, "special": ["user-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_created", "table": "block_pricing", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "block_pricing", "field": "date_updated", "type": "timestamp", "meta": {"collection": "block_pricing", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 4, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "block_pricing", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_pricing", "field": "user_updated", "type": "uuid", "meta": {"collection": "block_pricing", "conditions": null, "display": "user", "display_options": null, "field": "user_updated", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 5, "special": ["user-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_updated", "table": "block_pricing", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "block_pricing_cards", "field": "id", "type": "uuid", "meta": {"collection": "block_pricing_cards", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": ["uuid"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "id", "table": "block_pricing_cards", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_pricing_cards", "field": "title", "type": "string", "meta": {"collection": "block_pricing_cards", "conditions": null, "display": null, "display_options": null, "field": "title", "group": null, "hidden": false, "interface": "input", "note": "Name of the pricing plan. Shown at the top of the card.", "options": {"placeholder": "Starter Plan"}, "readonly": false, "required": false, "sort": 7, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "title", "table": "block_pricing_cards", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_pricing_cards", "field": "description", "type": "text", "meta": {"collection": "block_pricing_cards", "conditions": null, "display": null, "display_options": null, "field": "description", "group": null, "hidden": false, "interface": "input-multiline", "note": "Short, one sentence description of the pricing plan and who it is for.", "options": {"placeholder": "For small businesses and indie hackers"}, "readonly": false, "required": false, "sort": 8, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "description", "table": "block_pricing_cards", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_pricing_cards", "field": "price", "type": "string", "meta": {"collection": "block_pricing_cards", "conditions": null, "display": null, "display_options": null, "field": "price", "group": null, "hidden": false, "interface": "input", "note": "Price and term for the pricing plan. (ie `$199/mo`)", "options": {"placeholder": "$199 /month"}, "readonly": false, "required": false, "sort": 9, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "price", "table": "block_pricing_cards", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_pricing_cards", "field": "badge", "type": "string", "meta": {"collection": "block_pricing_cards", "conditions": null, "display": null, "display_options": null, "field": "badge", "group": null, "hidden": false, "interface": "input", "note": "Badge that displays at the top of the pricing plan card to add helpful context.", "options": {"placeholder": "Most popular"}, "readonly": false, "required": false, "sort": 10, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "badge", "table": "block_pricing_cards", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_pricing_cards", "field": "features", "type": "json", "meta": {"collection": "block_pricing_cards", "conditions": null, "display": null, "display_options": null, "field": "features", "group": null, "hidden": false, "interface": "simple-list", "note": "Short list of features included in this plan. Press `Enter` to add another item to the list.", "options": {"limit": 10}, "readonly": false, "required": false, "sort": 11, "special": ["cast-json"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "features", "table": "block_pricing_cards", "data_type": "json", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_pricing_cards", "field": "button", "type": "uuid", "meta": {"collection": "block_pricing_cards", "conditions": null, "display": "related-values", "display_options": {"template": "{{label}} • {{type}}"}, "field": "button", "group": null, "hidden": false, "interface": "select-dropdown-m2o", "note": "The action button / link shown at the bottom of the pricing card.", "options": {"createRelatedItem": "always", "enableLink": true, "enableSelect": false, "template": "{{label}} • {{type}}"}, "readonly": false, "required": false, "sort": 12, "special": ["m2o"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "button", "table": "block_pricing_cards", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "block_button", "foreign_key_column": "id"}}, {"collection": "block_pricing_cards", "field": "pricing", "type": "uuid", "meta": {"collection": "block_pricing_cards", "conditions": null, "display": "related-values", "display_options": null, "field": "pricing", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": "The id of the pricing block this card belongs to.", "options": {"enableLink": true, "enableSelect": false}, "readonly": false, "required": false, "sort": 13, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "pricing", "table": "block_pricing_cards", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "block_pricing", "foreign_key_column": "id"}}, {"collection": "block_pricing_cards", "field": "is_highlighted", "type": "boolean", "meta": {"collection": "block_pricing_cards", "conditions": null, "display": null, "display_options": null, "field": "is_highlighted", "group": null, "hidden": false, "interface": "boolean", "note": "Add highlighted border around the pricing plan to make it stand out.", "options": {"label": "Highlighted"}, "readonly": false, "required": false, "sort": 14, "special": ["cast-boolean"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "is_highlighted", "table": "block_pricing_cards", "data_type": "boolean", "default_value": false, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_pricing_cards", "field": "sort", "type": "integer", "meta": {"collection": "block_pricing_cards", "conditions": null, "display": null, "display_options": null, "field": "sort", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "sort", "table": "block_pricing_cards", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_pricing_cards", "field": "date_created", "type": "timestamp", "meta": {"collection": "block_pricing_cards", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 3, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "block_pricing_cards", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_pricing_cards", "field": "user_created", "type": "uuid", "meta": {"collection": "block_pricing_cards", "conditions": null, "display": "user", "display_options": null, "field": "user_created", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 4, "special": ["user-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_created", "table": "block_pricing_cards", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "block_pricing_cards", "field": "date_updated", "type": "timestamp", "meta": {"collection": "block_pricing_cards", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 5, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "block_pricing_cards", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_pricing_cards", "field": "user_updated", "type": "uuid", "meta": {"collection": "block_pricing_cards", "conditions": null, "display": "user", "display_options": null, "field": "user_updated", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 6, "special": ["user-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_updated", "table": "block_pricing_cards", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "block_richtext", "field": "content", "type": "text", "meta": {"collection": "block_richtext", "conditions": null, "display": null, "display_options": null, "field": "content", "group": null, "hidden": false, "interface": "input-rich-text-html", "note": "Rich text content for this block.", "options": {"customFormats": null, "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9"}, "readonly": false, "required": false, "sort": 9, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "content", "table": "block_richtext", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_richtext", "field": "headline", "type": "string", "meta": {"collection": "block_richtext", "conditions": null, "display": "raw", "display_options": null, "field": "headline", "group": null, "hidden": false, "interface": "input", "note": "Larger main headline for this page section.", "options": null, "readonly": false, "required": false, "sort": 8, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "headline", "table": "block_richtext", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_richtext", "field": "id", "type": "uuid", "meta": {"collection": "block_richtext", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": ["uuid"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "block_richtext", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_richtext", "field": "alignment", "type": "string", "meta": {"collection": "block_richtext", "conditions": null, "display": "formatted-value", "display_options": null, "field": "alignment", "group": null, "hidden": false, "interface": "select-radio", "note": "Controls how the content block is positioned on the page. Choose \"Left\" to align the block against the left margin or \"Center\" to position the block in the middle of the page. This setting affects the entire content block's placement, not the text alignment within it.", "options": {"choices": [{"text": "Left", "value": "left"}, {"text": "Center", "value": "center"}]}, "readonly": false, "required": false, "sort": 10, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "alignment", "table": "block_richtext", "data_type": "character varying", "default_value": "center", "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_richtext", "field": "tagline", "type": "string", "meta": {"collection": "block_richtext", "conditions": null, "display": null, "display_options": null, "field": "tagline", "group": null, "hidden": false, "interface": "input", "note": "Smaller copy shown above the headline to label a section or add extra context.", "options": null, "readonly": false, "required": false, "sort": 7, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "tagline", "table": "block_richtext", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_richtext", "field": "meta_header_block_richtext", "type": "alias", "meta": {"collection": "block_richtext", "conditions": null, "display": null, "display_options": null, "field": "meta_header_block_richtext", "group": null, "hidden": false, "interface": "super-header", "note": null, "options": {"help": "<p>Perfect for creating formatted text content with headings, paragraphs, and lists. Use this block when you need to:</p>\n<ul>\n<li>Write longer form content with multiple paragraphs</li>\n<li>Create structured content with headings and subheadings</li>\n<li>Include inline formatting like bold, italic, or links</li>\n<li>Add bulleted or numbered lists</li>\n</ul>", "icon": "format_color_text", "title": "Rich Text Block"}, "readonly": false, "required": false, "sort": 6, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "block_richtext", "field": "date_created", "type": "timestamp", "meta": {"collection": "block_richtext", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 2, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "block_richtext", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_richtext", "field": "user_created", "type": "uuid", "meta": {"collection": "block_richtext", "conditions": null, "display": "user", "display_options": null, "field": "user_created", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 3, "special": ["user-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_created", "table": "block_richtext", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "block_richtext", "field": "date_updated", "type": "timestamp", "meta": {"collection": "block_richtext", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 4, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "block_richtext", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "block_richtext", "field": "user_updated", "type": "uuid", "meta": {"collection": "block_richtext", "conditions": null, "display": "user", "display_options": null, "field": "user_updated", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 5, "special": ["user-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_updated", "table": "block_richtext", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "directus_settings", "field": "command_palette_settings", "type": "json", "meta": {"collection": "directus_settings", "conditions": null, "display": "raw", "display_options": null, "field": "command_palette_settings", "group": null, "hidden": true, "interface": "input-code", "note": "Settings for the Command Palette Module.", "options": null, "readonly": false, "required": false, "sort": 1, "special": ["cast-json"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "command_palette_settings", "table": "directus_settings", "data_type": "json", "default_value": {}, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "directus_users", "field": "posts", "type": "alias", "meta": {"collection": "directus_users", "conditions": null, "display": "related-values", "display_options": null, "field": "posts", "group": null, "hidden": false, "interface": "list-o2m", "note": "Blog posts this user has authored.", "options": {"template": "{{title}}"}, "readonly": false, "required": false, "sort": 1, "special": ["o2m"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "form_fields", "field": "id", "type": "uuid", "meta": {"collection": "form_fields", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": ["uuid"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "id", "table": "form_fields", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "form_fields", "field": "name", "type": "string", "meta": {"collection": "form_fields", "conditions": null, "display": "formatted-value", "display_options": {"format": true}, "field": "name", "group": null, "hidden": false, "interface": "input", "note": "Unique field identifier, not shown to users (lowercase, hyphenated)", "options": {"iconLeft": "key", "placeholder": "full-name", "slug": true, "trim": true}, "readonly": false, "required": false, "sort": 8, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "name", "table": "form_fields", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "form_fields", "field": "type", "type": "string", "meta": {"collection": "form_fields", "conditions": null, "display": "labels", "display_options": {"choices": [{"icon": "format_color_text", "text": "Text", "value": "text"}, {"icon": "display_external_input", "text": "Textarea", "value": "textarea"}, {"icon": "check_box_outline_blank", "text": "Checkbox", "value": "checkbox"}, {"icon": "library_add_check", "text": "Checkbox Group", "value": "checkbox_group"}, {"icon": "radio_button_unchecked", "text": "Radio", "value": "radio"}, {"icon": "attach_file", "text": "File", "value": "file"}, {"icon": "text_select_move_down", "text": "Select", "value": "select"}, {"icon": "hide_source", "text": "Hidden", "value": "hidden"}]}, "field": "type", "group": null, "hidden": false, "interface": "radio-cards-interface", "note": "Input type for the field", "options": {"choices": [{"icon": "format_color_text", "icon_type": "icon", "text": "Text", "value": "text"}, {"icon": "display_external_input", "icon_type": "icon", "text": "Textarea", "value": "textarea"}, {"icon": "check_box_outline_blank", "icon_type": "icon", "text": "Checkbox", "value": "checkbox"}, {"icon": "library_add_check", "icon_type": "icon", "text": "Checkbox Group", "value": "checkbox_group"}, {"icon": "radio_button_unchecked", "icon_type": "icon", "text": "Radio", "value": "radio"}, {"icon": "attach_file", "icon_type": "icon", "text": "File", "value": "file"}, {"icon": "text_select_move_down", "icon_type": "icon", "text": "Select", "value": "select"}, {"icon": "hide_source", "icon_type": "icon", "text": "Hidden", "value": "hidden"}], "gridSize": 5}, "readonly": false, "required": false, "sort": 10, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "type", "table": "form_fields", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "form_fields", "field": "label", "type": "string", "meta": {"collection": "form_fields", "conditions": null, "display": null, "display_options": null, "field": "label", "group": null, "hidden": false, "interface": "input", "note": "Text label shown to form users.", "options": {"placeholder": "Your Full Name"}, "readonly": false, "required": false, "sort": 9, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "label", "table": "form_fields", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "form_fields", "field": "placeholder", "type": "string", "meta": {"collection": "form_fields", "conditions": null, "display": null, "display_options": null, "field": "placeholder", "group": null, "hidden": false, "interface": "input", "note": "Default text shown in empty input.", "options": {"placeholder": "<PERSON>"}, "readonly": false, "required": false, "sort": 12, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "placeholder", "table": "form_fields", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "form_fields", "field": "help", "type": "string", "meta": {"collection": "form_fields", "conditions": null, "display": null, "display_options": null, "field": "help", "group": null, "hidden": false, "interface": "input", "note": "Additional instructions shown below the input", "options": {"placeholder": "Use first and last name"}, "readonly": false, "required": false, "sort": 13, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "help", "table": "form_fields", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "form_fields", "field": "validation", "type": "string", "meta": {"collection": "form_fields", "conditions": null, "display": null, "display_options": null, "field": "validation", "group": null, "hidden": false, "interface": "input", "note": "Available rules: `email`, `url`, `min:5`, `max:20`, `length:10`. Combine with pipes example: `email|max:255`", "options": {"placeholder": "max:255", "trim": true}, "readonly": false, "required": false, "sort": 15, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "validation", "table": "form_fields", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "form_fields", "field": "width", "type": "string", "meta": {"collection": "form_fields", "conditions": null, "display": "formatted-value", "display_options": {"format": true, "suffix": "%"}, "field": "width", "group": null, "hidden": false, "interface": "select-radio", "note": "Field width on the form", "options": {"choices": [{"text": "100%", "value": "100"}, {"text": "67%", "value": "67"}, {"text": "50%", "value": "50"}, {"text": "33%", "value": "33"}]}, "readonly": false, "required": false, "sort": 16, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "width", "table": "form_fields", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "form_fields", "field": "choices", "type": "json", "meta": {"collection": "form_fields", "conditions": [{"hidden": false, "name": "Show Choices", "options": {}, "rule": {"_and": [{"_or": [{"type": {"_eq": "select"}}, {"type": {"_eq": "checkbox_group"}}, {"type": {"_eq": "radio"}}]}]}}], "display": "formatted-json-value", "display_options": {"format": "{{ label }}"}, "field": "choices", "group": null, "hidden": true, "interface": "list", "note": "Options for radio or select inputs", "options": {"fields": [{"field": "text", "meta": {"field": "text", "interface": "input", "note": "Displayed label to user", "options": {"placeholder": "Sales Team"}, "required": true, "type": "string", "width": "half"}, "name": "text", "type": "string"}, {"field": "value", "meta": {"field": "value", "interface": "input", "note": "Stored value", "options": {"placeholder": "sales"}, "required": true, "type": "string", "width": "half"}, "name": "value", "type": "string"}], "template": "{{ text }}"}, "readonly": false, "required": false, "sort": 11, "special": ["cast-json"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "choices", "table": "form_fields", "data_type": "json", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "form_fields", "field": "form", "type": "uuid", "meta": {"collection": "form_fields", "conditions": null, "display": "related-values", "display_options": {"template": "{{title}}"}, "field": "form", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": "Parent form this field belongs to.", "options": {"template": "{{title}}"}, "readonly": false, "required": false, "sort": 7, "special": ["m2o"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "form", "table": "form_fields", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "forms", "foreign_key_column": "id"}}, {"collection": "form_fields", "field": "sort", "type": "integer", "meta": {"collection": "form_fields", "conditions": null, "display": null, "display_options": null, "field": "sort", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "sort", "table": "form_fields", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "form_fields", "field": "required", "type": "boolean", "meta": {"collection": "form_fields", "conditions": null, "display": null, "display_options": null, "field": "required", "group": null, "hidden": false, "interface": "boolean", "note": "Make this field mandatory to complete.", "options": {"label": "Required"}, "readonly": false, "required": false, "sort": 14, "special": ["cast-boolean"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "required", "table": "form_fields", "data_type": "boolean", "default_value": false, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "form_fields", "field": "date_created", "type": "timestamp", "meta": {"collection": "form_fields", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 3, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "form_fields", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "form_fields", "field": "user_created", "type": "uuid", "meta": {"collection": "form_fields", "conditions": null, "display": "user", "display_options": null, "field": "user_created", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 4, "special": ["user-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_created", "table": "form_fields", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "form_fields", "field": "date_updated", "type": "timestamp", "meta": {"collection": "form_fields", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 5, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "form_fields", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "form_fields", "field": "user_updated", "type": "uuid", "meta": {"collection": "form_fields", "conditions": null, "display": "user", "display_options": null, "field": "user_updated", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 6, "special": ["user-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_updated", "table": "form_fields", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "form_submission_values", "field": "id", "type": "uuid", "meta": {"collection": "form_submission_values", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": ["uuid"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "id", "table": "form_submission_values", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "form_submission_values", "field": "form_submission", "type": "uuid", "meta": {"collection": "form_submission_values", "conditions": null, "display": null, "display_options": null, "field": "form_submission", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": "Parent form submission for this value.", "options": null, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "form_submission", "table": "form_submission_values", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "form_submissions", "foreign_key_column": "id"}}, {"collection": "form_submission_values", "field": "field", "type": "uuid", "meta": {"collection": "form_submission_values", "conditions": null, "display": "related-values", "display_options": {"template": "{{name}} • {{type}}"}, "field": "field", "group": null, "hidden": false, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{name}} • {{type}}"}, "readonly": false, "required": false, "sort": 5, "special": ["m2o"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "field", "table": "form_submission_values", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "form_fields", "foreign_key_column": "id"}}, {"collection": "form_submission_values", "field": "value", "type": "text", "meta": {"collection": "form_submission_values", "conditions": null, "display": null, "display_options": null, "field": "value", "group": null, "hidden": false, "interface": "input", "note": "The data entered by the user for this specific field in the form submission.", "options": null, "readonly": false, "required": false, "sort": 4, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "value", "table": "form_submission_values", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "form_submission_values", "field": "sort", "type": "integer", "meta": {"collection": "form_submission_values", "conditions": null, "display": null, "display_options": null, "field": "sort", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 6, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "sort", "table": "form_submission_values", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "form_submission_values", "field": "file", "type": "uuid", "meta": {"collection": "form_submission_values", "conditions": null, "display": "file", "display_options": null, "field": "file", "group": null, "hidden": false, "interface": "file", "note": null, "options": {"folder": "e6308546-92fb-4b10-b586-eefaf1d97f7f"}, "readonly": false, "required": false, "sort": 7, "special": ["file"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "file", "table": "form_submission_values", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_files", "foreign_key_column": "id"}}, {"collection": "form_submission_values", "field": "timestamp", "type": "timestamp", "meta": {"collection": "form_submission_values", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "timestamp", "group": null, "hidden": true, "interface": "datetime", "note": "Form submission date and time.", "options": null, "readonly": true, "required": false, "sort": 2, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "timestamp", "table": "form_submission_values", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "form_submissions", "field": "id", "type": "uuid", "meta": {"collection": "form_submissions", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": false, "interface": "input", "note": "Unique ID for this specific form submission", "options": {"iconLeft": "vpn_key"}, "readonly": true, "required": false, "sort": 3, "special": ["uuid"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "id", "table": "form_submissions", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "form_submissions", "field": "timestamp", "type": "timestamp", "meta": {"collection": "form_submissions", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "timestamp", "group": null, "hidden": false, "interface": "datetime", "note": "Form submission date and time.", "options": null, "readonly": true, "required": false, "sort": 4, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "timestamp", "table": "form_submissions", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "form_submissions", "field": "form", "type": "uuid", "meta": {"collection": "form_submissions", "conditions": null, "display": "related-values", "display_options": {"template": "{{title}}"}, "field": "form", "group": null, "hidden": false, "interface": "select-dropdown-m2o", "note": "Associated form for this submission.", "options": {"enableLink": true, "template": "{{title}}"}, "readonly": true, "required": false, "sort": 2, "special": ["m2o"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "form", "table": "form_submissions", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "forms", "foreign_key_column": "id"}}, {"collection": "form_submissions", "field": "values", "type": "alias", "meta": {"collection": "form_submissions", "conditions": null, "display": "related-values", "display_options": {"template": "{{value}} • {{field}}"}, "field": "values", "group": null, "hidden": false, "interface": "list-o2m", "note": "Submitted field responses", "options": {"enableCreate": false, "enableSearchFilter": true, "enableSelect": false, "fields": ["field.label", "value", "field.type", "file"], "filter": {"_and": [{"field": {"form": {"_eq": "{{form}}"}}}]}, "layout": "table", "limit": 25, "sort": "sort", "template": "{{value}} • {{field}}"}, "readonly": true, "required": false, "sort": 5, "special": ["o2m"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "form_submissions", "field": "meta_notice_submissions", "type": "alias", "meta": {"collection": "form_submissions", "conditions": null, "display": null, "display_options": null, "field": "meta_notice_submissions", "group": null, "hidden": false, "interface": "presentation-notice", "note": null, "options": {"text": "Form submissions are configured to be read-only for data integrity reasons. But they are a great candidate for use in [Flows](/admin/settings/flows). Directus Flows is a drag and drop, low-code automation builder to simplify tasks like automatic notifications or sending incoming requests to third party services. <a href=\"https://directus.io/docs/guides/automate/flows?ref=simple_website_cms\" target=\"_blank\">Learn more about Flows.</a>"}, "readonly": false, "required": false, "sort": 6, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "form_submissions", "field": "meta_header_form_submissions", "type": "alias", "meta": {"collection": "form_submissions", "conditions": null, "display": null, "display_options": null, "field": "meta_header_form_submissions", "group": null, "hidden": false, "interface": "super-header", "note": null, "options": {"help": "<h2>Form Submissions</h2>\n<p>Form submissions are read-only records of responses received through your site's forms. This ensures data integrity and provides a reliable audit trail of all submitted information.</p>\n<h3>Understanding Submissions</h3>\n<p>Each submission contains:</p>\n<ul>\n<li>Unique submission ID</li>\n<li>Timestamp of submission</li>\n<li>Reference to the parent form</li>\n<li>All submitted field values</li>\n</ul>\n<h3>Automating with Flows</h3>\n<p>While submissions are read-only, you can use Flows to automate actions when new submissions arrive:</p>\n<ul>\n<li>Send custom email notifications</li>\n<li>Push data to external services</li>\n<li>Create records in other collections</li>\n<li>Trigger webhooks</li>\n<li>Format and transform submission data</li>\n</ul>\n<p>Think of Flows as your automation toolkit - they let you act on submission data without modifying the original records.</p>\n<h3>Data Management</h3>\n<h4>Exporting Data</h4>\n<p>Export submissions to work with them outside the CMS:</p>\n<ul>\n<li>Download as CSV for spreadsheet analysis</li>\n<li>Include date ranges for periodic reports</li>\n<li>Select specific fields to include</li>\n<li>Export file attachments in bulk</li>\n</ul>\n<h4>Data Cleanup</h4>\n<p>Keep your submission data organized:</p>\n<ul>\n<li>Archive old submissions</li>\n<li>Monitor storage usage from file uploads</li>\n<li>Watch for spam patterns</li>\n<li>Maintain audit logs</li>\n</ul>\n<h3>Privacy &amp; Security</h3>\n<p>Since form submissions often contain personal data:</p>\n<ul>\n<li>Follow your data retention policy</li>\n<li>Handle personal information according to privacy laws</li>\n<li>Regularly review old submissions for deletion</li>\n<li>Consider automation for data cleanup</li>\n<li>Monitor who has access to submission data</li>\n</ul>", "subtitle": "Submitted at: {{timestamp}}", "title": "Form Submission"}, "readonly": false, "required": false, "sort": 1, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "forms", "field": "id", "type": "uuid", "meta": {"collection": "forms", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": {"iconLeft": "vpn_key"}, "readonly": true, "required": false, "sort": 1, "special": ["uuid"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "id", "table": "forms", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "forms", "field": "on_success", "type": "string", "meta": {"collection": "forms", "conditions": null, "display": "labels", "display_options": {"choices": [{"text": "Redirect to URL", "value": "redirect"}, {"text": "Show Message", "value": "message"}]}, "field": "on_success", "group": "meta_fields", "hidden": false, "interface": "select-dropdown", "note": "Action after successful submission.", "options": {"choices": [{"icon": "arrow_outward", "text": "Redirect to URL", "value": "redirect"}, {"icon": "chat", "text": "Show Message", "value": "message"}]}, "readonly": false, "required": false, "sort": 4, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "on_success", "table": "forms", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "forms", "field": "sort", "type": "integer", "meta": {"collection": "forms", "conditions": null, "display": null, "display_options": null, "field": "sort", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "sort", "table": "forms", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "forms", "field": "submit_label", "type": "string", "meta": {"collection": "forms", "conditions": null, "display": "raw", "display_options": null, "field": "submit_label", "group": "meta_fields", "hidden": false, "interface": "input", "note": "Text shown on submit button.", "options": {"iconLeft": "smart_button", "placeholder": "Sign Up Now"}, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "submit_label", "table": "forms", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "forms", "field": "success_message", "type": "text", "meta": {"collection": "forms", "conditions": [{"hidden": false, "name": "If Message", "options": {"font": "sans-serif", "toolbar": ["bold", "italic", "underline", "h1", "h2", "h3", "numlist", "bullist", "removeformat", "blockquote", "customLink", "customImage", "customMedia", "hr", "code", "fullscreen"]}, "readonly": false, "rule": {"_and": [{"on_success": {"_eq": "message"}}]}}], "display": null, "display_options": null, "field": "success_message", "group": "meta_fields", "hidden": true, "interface": "input-multiline", "note": "Message shown after successful submission.", "options": {"placeholder": "Thanks for reaching out! We'll be in touch soon."}, "readonly": false, "required": false, "sort": 5, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "success_message", "table": "forms", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "forms", "field": "title", "type": "string", "meta": {"collection": "forms", "conditions": null, "display": null, "display_options": null, "field": "title", "group": null, "hidden": false, "interface": "input", "note": "Form name (for internal reference).", "options": null, "readonly": false, "required": false, "sort": 8, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "title", "table": "forms", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "forms", "field": "success_redirect_url", "type": "string", "meta": {"collection": "forms", "conditions": [{"hidden": false, "name": "If Redirect", "options": {"clear": false, "font": "sans-serif", "masked": false, "slug": false, "trim": false}, "rule": {"_and": [{"on_success": {"_eq": "redirect"}}]}}], "display": "raw", "display_options": null, "field": "success_redirect_url", "group": "meta_fields", "hidden": true, "interface": "input", "note": "Destination URL after successful submission.", "options": {"iconLeft": "link"}, "readonly": false, "required": false, "sort": 6, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "success_redirect_url", "table": "forms", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "forms", "field": "fields", "type": "alias", "meta": {"collection": "forms", "conditions": null, "display": "related-values", "display_options": {"template": "{{name}} • {{type}}"}, "field": "fields", "group": "meta_fields", "hidden": false, "interface": "list-o2m", "note": "Form structure and input fields", "options": {"enableSelect": false, "sort": "sort", "template": "{{name}} • {{type}}"}, "readonly": false, "required": false, "sort": 2, "special": ["o2m"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "forms", "field": "submissions", "type": "alias", "meta": {"collection": "forms", "conditions": null, "display": "related-values", "display_options": {"template": "{{values.value}}"}, "field": "submissions", "group": "meta_submissions", "hidden": false, "interface": "list-o2m", "note": "Received form responses.", "options": {"enableCreate": false, "enableLink": true, "enableSearchFilter": true, "enableSelect": false, "fields": ["timestamp", "values.value", "values.field.name"], "layout": "table", "limit": 25, "sort": "timestamp"}, "readonly": true, "required": false, "sort": 2, "special": ["o2m"], "translations": null, "validation": null, "validation_message": null, "width": "fill"}}, {"collection": "forms", "field": "meta_tabs", "type": "alias", "meta": {"collection": "forms", "conditions": null, "display": null, "display_options": null, "field": "meta_tabs", "group": null, "hidden": false, "interface": "group-tabs", "note": null, "options": null, "readonly": false, "required": false, "sort": 10, "special": ["alias", "no-data", "group"], "translations": [{"language": "en-US", "translation": "Tabs"}], "validation": null, "validation_message": null, "width": "full"}}, {"collection": "forms", "field": "meta_fields", "type": "alias", "meta": {"collection": "forms", "conditions": null, "display": null, "display_options": null, "field": "meta_fields", "group": "meta_tabs", "hidden": false, "interface": "group-raw", "note": null, "options": null, "readonly": false, "required": false, "sort": 1, "special": ["alias", "no-data", "group"], "translations": [{"language": "en-US", "translation": "Form Fields"}], "validation": null, "validation_message": null, "width": "full"}}, {"collection": "forms", "field": "is_active", "type": "boolean", "meta": {"collection": "forms", "conditions": null, "display": "boolean", "display_options": {"labelOff": "Inactive", "labelOn": "Active"}, "field": "is_active", "group": null, "hidden": false, "interface": "boolean", "note": "Show or hide this form from the site.", "options": {"label": "Active"}, "readonly": false, "required": false, "sort": 9, "special": ["cast-boolean"], "translations": [{"language": "en-US", "translation": "Active"}], "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "is_active", "table": "forms", "data_type": "boolean", "default_value": true, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "forms", "field": "emails", "type": "json", "meta": {"collection": "forms", "conditions": null, "display": "formatted-json-value", "display_options": {"format": "To: {{ to }} • Subject: {{ subject }}"}, "field": "emails", "group": "meta_emails", "hidden": false, "interface": "list", "note": "Setup email notifications when forms are submitted.", "options": {"addLabel": "Add New Email", "fields": [{"field": "to", "meta": {"display": "labels", "display_options": {"format": false}, "field": "to", "interface": "tags", "note": "Add an email address and press enter. To use a form field as the email address use merge tags `{# #}` (e.g. `{# email #}.", "options": {"choices": null}, "required": true, "type": "json", "width": "full"}, "name": "to", "type": "json"}, {"field": "subject", "meta": {"display": "formatted-value", "display_options": {}, "field": "subject", "interface": "input", "note": "Email subject line. You can use merge tags like this `Hi {# first-name #}`.", "required": true, "type": "string", "width": "full"}, "name": "subject", "type": "string"}, {"field": "message", "meta": {"field": "message", "interface": "input-rich-text-html", "note": "Include a message for the email body. To merge form responses use merge tags with the field name like `{# first-name #}`.", "options": {"folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "toolbar": ["undo", "redo", "bold", "italic", "underline", "h1", "h2", "h3", "numlist", "bullist", "removeformat", "blockquote", "customLink", "customImage", "table", "hr", "code", "fullscreen"]}, "required": true, "type": "text", "width": "full"}, "name": "message", "type": "text"}], "template": "To: {{ to }} • Subject: {{ subject }}"}, "readonly": false, "required": false, "sort": 2, "special": ["cast-json"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "emails", "table": "forms", "data_type": "json", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "forms", "field": "meta_submissions", "type": "alias", "meta": {"collection": "forms", "conditions": null, "display": null, "display_options": null, "field": "meta_submissions", "group": "meta_tabs", "hidden": false, "interface": "group-raw", "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": ["alias", "no-data", "group"], "translations": [{"language": "en-US", "translation": "Submissions"}], "validation": null, "validation_message": null, "width": "full"}}, {"collection": "forms", "field": "meta_emails", "type": "alias", "meta": {"collection": "forms", "conditions": null, "display": null, "display_options": null, "field": "meta_emails", "group": "meta_tabs", "hidden": false, "interface": "group-raw", "note": null, "options": null, "readonly": false, "required": false, "sort": 3, "special": ["alias", "no-data", "group"], "translations": [{"language": "en-US", "translation": "Emails"}], "validation": null, "validation_message": null, "width": "full"}}, {"collection": "forms", "field": "meta_notice_form_fields", "type": "alias", "meta": {"collection": "forms", "conditions": null, "display": null, "display_options": null, "field": "meta_notice_form_fields", "group": "meta_fields", "hidden": false, "interface": "presentation-notice", "note": null, "options": {"text": " Create custom forms by adding and configuring your desired input fields below. No coding required."}, "readonly": false, "required": false, "sort": 1, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "forms", "field": "meta_notice_form_responses", "type": "alias", "meta": {"collection": "forms", "conditions": null, "display": null, "display_options": null, "field": "meta_notice_form_responses", "group": "meta_submissions", "hidden": false, "interface": "presentation-notice", "note": null, "options": {"text": "This table displays all responses submitted through this form. Each entry includes the submission timestamp and the values for each form field."}, "readonly": false, "required": false, "sort": 1, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "forms", "field": "meta_notice_form_emails", "type": "alias", "meta": {"collection": "forms", "conditions": null, "display": null, "display_options": null, "field": "meta_notice_form_emails", "group": "meta_emails", "hidden": false, "interface": "presentation-notice", "note": null, "options": {"text": "Set up automatic emails to notify your team members when forms are submitted, or send confirmation receipts to people who complete your forms."}, "readonly": false, "required": false, "sort": 1, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "forms", "field": "meta_header_forms", "type": "alias", "meta": {"collection": "forms", "conditions": null, "display": null, "display_options": null, "field": "meta_header_forms", "group": null, "hidden": false, "interface": "super-header", "note": null, "options": {"actions": [], "help": "<h2>Understanding Forms</h2>\n<p>Forms are reusable components that you create once and can place anywhere on your site using Form blocks. Think of the Forms collection as your form template library - you build the forms here, then use Form blocks to display them on your pages.</p>\n<h3>The Forms &amp; Form Blocks Relationship</h3>\n<ol>\n<li>First, create and configure your form in the Forms collection</li>\n<li>Then, add a Form block to any page where you want the form to appear</li>\n<li>Select your form from the dropdown in the Form block settings</li>\n<li>Customize the block's headline and tagline to match your page context</li>\n</ol>\n<p>This approach lets you:</p>\n<ul>\n<li>Reuse the same form across multiple pages</li>\n<li>Update the form once to change it everywhere</li>\n<li>Maintain consistent data collection</li>\n<li>Track submissions from all instances of the form</li>\n</ul>\n<h2>Creating Forms</h2>\n<h3>Basic Settings</h3>\n<h4>Form Title</h4>\n<p>Internal name to identify your form in the admin panel. Choose something descriptive like:</p>\n<ul>\n<li>\"Newsletter Signup 2024\"</li>\n<li>\"Contact Form - Main\"</li>\n<li>\"Event Registration Form\"</li>\n</ul>\n<h4>Submit Button</h4>\n<p>Customize the text on your submit button. Use action-oriented phrases like:</p>\n<ul>\n<li>\"Send Message\"</li>\n<li>\"Sign Up Now\"</li>\n<li>\"Register Today\"</li>\n</ul>\n<h4>Success Handling</h4>\n<p>Choose what happens after submission:</p>\n<p><strong>Show Message</strong></p>\n<ul>\n<li>Display a custom thank you message</li>\n<li>Confirm the submission was received</li>\n<li>Set expectations for next steps</li>\n</ul>\n<p><strong>Redirect</strong></p>\n<ul>\n<li>Send users to a specific thank you page</li>\n<li>Guide them to relevant content</li>\n<li>Start the next step in their journey</li>\n</ul>\n<h4>Email Notifications</h4>\n<p>Configure automated emails for form submissions:</p>\n<ul>\n<li>Add multiple recipient email addresses</li>\n<li>Customize subject lines and messages</li>\n<li>Include form submission details in the email</li>\n</ul>\n<h4>Active Status</h4>\n<p>Control form availability:</p>\n<ul>\n<li>Enable/disable without deleting</li>\n<li>Temporarily pause data collection</li>\n<li>Test before making public</li>\n</ul>\n<h3>Building Your Form</h3>\n<h4>Available Field Types</h4>\n<ul>\n<li><strong>Text</strong>: Single line text input</li>\n<li><strong>Textarea</strong>: Multi-line text input</li>\n<li><strong>Checkbox</strong>: Single yes/no option</li>\n<li><strong>Checkbox Group</strong>: Multiple selectable options</li>\n<li><strong>Radio</strong>: Single choice from options</li>\n<li><strong>Select</strong>: Dropdown menu</li>\n<li><strong>File</strong>: File upload capability</li>\n<li><strong>Hidden</strong>: Invisible fields for tracking</li>\n</ul>\n<h4>Field Configuration</h4>\n<p><strong>Label</strong> The visible field name shown to users</p>\n<p><strong>Placeholder</strong> Optional hint text inside empty fields</p>\n<p><strong>Help Text</strong> Additional instructions below the field</p>\n<p><strong>Required Fields</strong> Mark essential fields that must be filled out</p>\n<p><strong>Width Options</strong> Control field layout:</p>\n<ul>\n<li>Full width (100%)</li>\n<li>Two-thirds (67%)</li>\n<li>Half (50%)</li>\n<li>One-third (33%)</li>\n</ul>\n<p><strong>Validation:</strong> Here's the available rules</p>\n<ul>\n<li>Email format: <code>email</code></li>\n<li>URL format: <code>url</code></li>\n<li>Minimum length: <code>min:5</code> (replace 5 with desired length)</li>\n<li>Maximum length: <code>max:20</code> (replace 20 with desired length)</li>\n<li>Exact length: <code>length:10</code> (replace 10 with desired length)</li>\n<li>Required fields: Set \"Required\" toggle to true</li>\n</ul>\n<p>You can combine rules with pipes: <code>email|max:255</code></p>\n<h2>Using Forms with Form Blocks</h2>\n<h3>Adding Forms to Pages</h3>\n<ol>\n<li>Add a Form block to your page</li>\n<li>Select your form from the dropdown</li>\n<li>Add an optional headline and tagline</li>\n<li>Preview to check the layout</li>\n</ol>\n<h3>Form Block Customization</h3>\n<ul>\n<li>Add context with a headline</li>\n<li>Provide instructions in the tagline</li>\n<li>Match the form to your page content</li>\n<li>Ensure the form fits your layout</li>\n</ul>\n<h2>Best Practices</h2>\n<h3>Form Design &amp; Experience</h3>\n<ul>\n<li>Keep forms as short as possible - only include essential fields</li>\n<li>Group related fields together logically</li>\n<li>Use clear labels and mark required fields</li>\n<li>Ensure error messages are helpful and specific</li>\n<li>Test on both desktop and mobile devices</li>\n</ul>\n<h3>Data Collection</h3>\n<ul>\n<li>Only collect information you'll actually use</li>\n<li>Follow data protection guidelines</li>\n<li>Review submissions regularly</li>\n</ul>\n<h2>Managing Form Submissions</h2>\n<p>All submissions are stored in the <a href=\"/admin/content/form_submissions\">Form Submissions collection</a>.</p>\n<ul>\n<li>Export data as needed</li>\n<li>Configure email notifications</li>\n<li>Track submission sources</li>\n<li>Monitor submission trends</li>\n</ul>", "title": "{{title}}"}, "readonly": false, "required": false, "sort": 7, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "forms", "field": "date_created", "type": "timestamp", "meta": {"collection": "forms", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 3, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "forms", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "forms", "field": "user_created", "type": "uuid", "meta": {"collection": "forms", "conditions": null, "display": "user", "display_options": null, "field": "user_created", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 4, "special": ["user-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_created", "table": "forms", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "forms", "field": "date_updated", "type": "timestamp", "meta": {"collection": "forms", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 5, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "forms", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "forms", "field": "user_updated", "type": "uuid", "meta": {"collection": "forms", "conditions": null, "display": "user", "display_options": null, "field": "user_updated", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 6, "special": ["user-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_updated", "table": "forms", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "globals", "field": "id", "type": "uuid", "meta": {"collection": "globals", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": ["uuid"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "globals", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "globals", "field": "description", "type": "text", "meta": {"collection": "globals", "conditions": null, "display": null, "display_options": null, "field": "description", "group": null, "hidden": false, "interface": "input-multiline", "note": "Site summary for search results.", "options": {"softLength": 160}, "readonly": false, "required": false, "sort": 16, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "description", "table": "globals", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "globals", "field": "tagline", "type": "string", "meta": {"collection": "globals", "conditions": null, "display": null, "display_options": null, "field": "tagline", "group": null, "hidden": false, "interface": "input", "note": "Short phrase describing the site.", "options": null, "readonly": false, "required": false, "sort": 15, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "tagline", "table": "globals", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "globals", "field": "title", "type": "string", "meta": {"collection": "globals", "conditions": null, "display": null, "display_options": null, "field": "title", "group": null, "hidden": false, "interface": "input", "note": "Main site title", "options": null, "readonly": false, "required": false, "sort": 8, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "title", "table": "globals", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "globals", "field": "url", "type": "string", "meta": {"collection": "globals", "conditions": null, "display": null, "display_options": null, "field": "url", "group": null, "hidden": false, "interface": "input", "note": "Public URL for the website", "options": null, "readonly": false, "required": false, "sort": 9, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "url", "table": "globals", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "globals", "field": "social_links", "type": "json", "meta": {"collection": "globals", "conditions": null, "display": "labels", "display_options": {}, "field": "social_links", "group": null, "hidden": false, "interface": "inline-repeater-interface", "note": "Social media profile URLs", "options": {"addLabel": "Add New Link", "fields": [{"field": "url", "meta": {"field": "url", "interface": "input", "note": "Full profile URL (not just username)", "options": {"placeholder": "https://www.linkedin.com/in/fullprofile", "trim": true}, "type": "string", "width": "half"}, "name": "url", "type": "string"}, {"field": "service", "meta": {"display": "labels", "display_options": {"choices": [{"icon": "facebook", "text": "Facebook", "value": "facebook"}, {"icon": "instagram", "text": "Instagram", "value": "instagram"}, {"icon": "linkedin", "text": "LinkedIn", "value": "linkedin"}, {"icon": "twitter", "text": "X", "value": "x"}, {"icon": "vimeo", "text": "Vimeo", "value": "vimeo"}, {"icon": "youtube", "text": "YouTube", "value": "youtube"}, {"icon": "github", "text": "GitHub", "value": "github"}, {"icon": "discord", "text": "Discord", "value": "discord"}, {"icon": "docker", "text": "<PERSON>er", "value": "docker"}]}, "field": "service", "interface": "select-dropdown", "note": "Social media platform name", "options": {"choices": [{"icon": "facebook", "text": "Facebook", "value": "facebook"}, {"icon": "instagram", "text": "Instagram", "value": "instagram"}, {"icon": "linkedin", "text": "LinkedIn", "value": "linkedin"}, {"icon": "twitter", "text": "X", "value": "x"}, {"icon": "vimeo", "text": "Vimeo", "value": "vimeo"}, {"icon": "youtube", "text": "YouTube", "value": "youtube"}, {"icon": "github", "text": "GitHub", "value": "github"}, {"icon": "discord", "text": "Discord", "value": "discord"}, {"icon": "docker", "text": "<PERSON>er", "value": "docker"}]}, "required": null, "type": "string", "width": "half"}, "name": "service", "type": "string"}], "template": "{{ service }}"}, "readonly": false, "required": false, "sort": 18, "special": ["cast-json"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "social_links", "table": "globals", "data_type": "json", "default_value": [], "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "globals", "field": "meta_divider_globals", "type": "alias", "meta": {"collection": "globals", "conditions": null, "display": null, "display_options": null, "field": "meta_divider_globals", "group": null, "hidden": false, "interface": "presentation-divider", "note": null, "options": {"color": "#A2B5CD", "icon": "link", "inlineTitle": true, "title": null}, "readonly": false, "required": false, "sort": 17, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "globals", "field": "favicon", "type": "uuid", "meta": {"collection": "globals", "conditions": null, "display": null, "display_options": null, "field": "favicon", "group": null, "hidden": false, "interface": "file-image", "note": "Small icon for browser tabs. 1:1 ratio. No larger than 512px × 512px.", "options": {"crop": false, "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9"}, "readonly": false, "required": false, "sort": 13, "special": ["file"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "favicon", "table": "globals", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_files", "foreign_key_column": "id"}}, {"collection": "globals", "field": "logo", "type": "uuid", "meta": {"collection": "globals", "conditions": null, "display": null, "display_options": null, "field": "logo", "group": null, "hidden": false, "interface": "file-image", "note": "Main logo shown on the site (for light mode).", "options": {"crop": false, "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9"}, "readonly": false, "required": false, "sort": 11, "special": ["file"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "logo", "table": "globals", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_files", "foreign_key_column": "id"}}, {"collection": "globals", "field": "divider_logo", "type": "alias", "meta": {"collection": "globals", "conditions": null, "display": null, "display_options": null, "field": "divider_logo", "group": null, "hidden": false, "interface": "presentation-divider", "note": null, "options": {"color": "#A2B5CD", "icon": "imagesmode", "inlineTitle": true}, "readonly": false, "required": false, "sort": 10, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "globals", "field": "openai_api_key", "type": "string", "meta": {"collection": "globals", "conditions": null, "display": "formatted-value", "display_options": {"masked": true}, "field": "openai_api_key", "group": "meta_credentials", "hidden": false, "interface": "input", "note": "Secret OpenAI API key. Don't share with anyone outside your team.", "options": {"iconLeft": "vpn_key_alert", "masked": true, "trim": true}, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "openai_api_key", "table": "globals", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "globals", "field": "directus_url", "type": "string", "meta": {"collection": "globals", "conditions": null, "display": null, "display_options": null, "field": "directus_url", "group": "meta_credentials", "hidden": false, "interface": "input", "note": "The public URL for this Directus instance. Used in Flows.", "options": null, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "directus_url", "table": "globals", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "globals", "field": "meta_credentials", "type": "alias", "meta": {"collection": "globals", "conditions": null, "display": null, "display_options": null, "field": "meta_credentials", "group": null, "hidden": false, "interface": "group-detail", "note": null, "options": {"headerIcon": "warning", "start": "closed"}, "readonly": false, "required": false, "sort": 19, "special": ["alias", "no-data", "group"], "translations": [{"language": "en-US", "translation": "Credentials"}], "validation": null, "validation_message": null, "width": "full"}}, {"collection": "globals", "field": "meta_notice_globals", "type": "alias", "meta": {"collection": "globals", "conditions": null, "display": null, "display_options": null, "field": "meta_notice_globals", "group": null, "hidden": false, "interface": "presentation-notice", "note": null, "options": {"text": "Globals are settings that are applied across your entire site. Globals use what we call a `singleton` collection type. <a href=\"https://directus.io/features/global-settings\" target=\"_blank\">Learn more about globals and singletons.</a>"}, "readonly": false, "required": false, "sort": 7, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "globals", "field": "meta_notice_security", "type": "alias", "meta": {"collection": "globals", "conditions": null, "display": null, "display_options": null, "field": "meta_notice_security", "group": "meta_credentials", "hidden": false, "interface": "presentation-notice", "note": null, "options": {"color": "warning", "icon": "warning", "text": "Be careful about changing the access policies and permissions for the `globals` collection so that you don't expose your private API keys."}, "readonly": false, "required": false, "sort": 1, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "globals", "field": "logo_dark_mode", "type": "uuid", "meta": {"collection": "globals", "conditions": null, "display": null, "display_options": null, "field": "logo_dark_mode", "group": null, "hidden": false, "interface": "file-image", "note": "Main logo shown on the site (for dark mode).", "options": {"crop": false, "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9"}, "readonly": false, "required": false, "sort": 12, "special": ["file"], "translations": [{"language": "en-US", "translation": "Dark Mode Logo"}], "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "logo_dark_mode", "table": "globals", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_files", "foreign_key_column": "id"}}, {"collection": "globals", "field": "accent_color", "type": "string", "meta": {"collection": "globals", "conditions": null, "display": null, "display_options": null, "field": "accent_color", "group": null, "hidden": false, "interface": "select-color", "note": "Accent color for the website (used on buttons, links, etc).", "options": null, "readonly": false, "required": false, "sort": 14, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "accent_color", "table": "globals", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "globals", "field": "meta_header_globals", "type": "alias", "meta": {"collection": "globals", "conditions": null, "display": null, "display_options": null, "field": "meta_header_globals", "group": null, "hidden": false, "interface": "super-header", "note": null, "options": {"help": "<h2>Global Settings</h2>\n<p>These settings control site-wide elements that affect your entire website. There's only one global settings record.</p>\n<h3>Site Info</h3>\n<ul>\n<li><strong>Title</strong>: Your website's name, appears in browser tabs and search results</li>\n<li><strong>Tagline</strong>: Short description of your site used in search results</li>\n<li><strong>Description</strong>: Longer site summary for SEO and social sharing</li>\n<li><strong>Website URL</strong>: Your site's public address</li>\n<li><strong>CMS URL</strong>: Address of your admin system</li>\n</ul>\n<h3>Brand Assets</h3>\n<ul>\n<li><strong>Logo</strong>: Main site logo for light backgrounds</li>\n<li><strong>Dark Mode Logo</strong>: Alternate logo for dark themes</li>\n<li><strong>Favicon</strong>: Small icon for browser tabs (max 512&times;512px)</li>\n<li><strong>Accent Color</strong>: Your brand's primary color for buttons and highlights</li>\n</ul>\n<h3>Social Media Links</h3>\n<p>Add URLs for your social profiles:</p>\n<ul>\n<li>Facebook, Instagram, LinkedIn</li>\n<li>Twitter, YouTube, Vimeo</li>\n<li>GitHub, Discord, Docker</li>\n</ul>\n<h3>API Settings</h3>\n<ul>\n<li><strong>OpenAI API Key</strong>: Required for AI-powered features</li>\n</ul>", "subtitle": "{{url}}", "title": "{{title}} • Site Settings"}, "readonly": false, "required": false, "sort": 6, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "globals", "field": "date_created", "type": "timestamp", "meta": {"collection": "globals", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 2, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "globals", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "globals", "field": "user_created", "type": "uuid", "meta": {"collection": "globals", "conditions": null, "display": "user", "display_options": null, "field": "user_created", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 3, "special": ["user-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_created", "table": "globals", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "globals", "field": "date_updated", "type": "timestamp", "meta": {"collection": "globals", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 4, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "globals", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "globals", "field": "user_updated", "type": "uuid", "meta": {"collection": "globals", "conditions": null, "display": "user", "display_options": null, "field": "user_updated", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 5, "special": ["user-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_updated", "table": "globals", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "navigation", "field": "id", "type": "string", "meta": {"collection": "navigation", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": false, "interface": "input", "note": "Unique identifier for this menu. Can't be edited after creation.", "options": {"iconLeft": "vpn_key"}, "readonly": false, "required": false, "sort": 7, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "id", "table": "navigation", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "navigation", "field": "title", "type": "string", "meta": {"collection": "navigation", "conditions": null, "display": null, "display_options": null, "field": "title", "group": null, "hidden": false, "interface": "input", "note": "What is the name of this menu? Only used internally.", "options": null, "readonly": false, "required": false, "sort": 6, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "title", "table": "navigation", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "navigation", "field": "items", "type": "alias", "meta": {"collection": "navigation", "conditions": null, "display": "related-values", "display_options": {"template": "{{title}} • {{type}}"}, "field": "items", "group": null, "hidden": false, "interface": "list-o2m", "note": "Links within the menu.", "options": {"enableLink": true, "filter": {"_and": [{"_and": [{"navigation": {"_null": true}}, {"parent": {"navigation": {"_null": true}}}]}]}, "template": "{{title}} • {{type}}"}, "readonly": false, "required": false, "sort": 10, "special": ["o2m"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "navigation", "field": "is_active", "type": "boolean", "meta": {"collection": "navigation", "conditions": null, "display": "boolean", "display_options": {"labelOff": "Inactive", "labelOn": "Active"}, "field": "is_active", "group": null, "hidden": false, "interface": "boolean", "note": "Show or hide this menu from the site.", "options": {"label": "Active"}, "readonly": false, "required": false, "sort": 8, "special": ["cast-boolean"], "translations": [{"language": "en-US", "translation": "Active"}], "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "is_active", "table": "navigation", "data_type": "boolean", "default_value": true, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "navigation", "field": "meta_notice_navigation", "type": "alias", "meta": {"collection": "navigation", "conditions": null, "display": null, "display_options": null, "field": "meta_notice_navigation", "group": null, "hidden": false, "interface": "presentation-notice", "note": null, "options": {"text": "Create and manage navigation menus for your website. Each menu can contain multiple links organized into a hierarchy."}, "readonly": false, "required": false, "sort": 9, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "navigation", "field": "meta_header_navigation", "type": "alias", "meta": {"collection": "navigation", "conditions": null, "display": null, "display_options": null, "field": "meta_header_navigation", "group": null, "hidden": false, "interface": "super-header", "note": null, "options": {"help": "<h2>Understanding Navigation</h2>\n<p>Navigation menus help visitors find their way around your site. You can create multiple menus (like main navigation, footer links, or utility menus) and manage them all from this collection.</p>\n<h3>Navigation Structure</h3>\n<p>Each navigation menu consists of:</p>\n<ol>\n<li>A menu container (Navigation)</li>\n<li>Menu items within it (Navigation Items)</li>\n</ol>\n<h2>Creating Navigation Menus</h2>\n<h3>Menu Settings</h3>\n<h4>Title</h4>\n<p>Internal name for your menu (only visible in admin). Examples:</p>\n<ul>\n<li>\"Main Navigation\"</li>\n<li>\"Footer Links\"</li>\n<li>\"Header Utility Menu\"</li>\n</ul>\n<h4>Active Status</h4>\n<p>Toggle menus on/off without deleting them. Useful for:</p>\n<ul>\n<li>Testing new menu structures</li>\n<li>Seasonal navigation changes</li>\n<li>Temporary menu updates</li>\n</ul>\n<h2>Adding Menu Items</h2>\n<h3>Item Types</h3>\n<h4>Page</h4>\n<ul>\n<li>Link to any page in your site</li>\n<li>Automatically updates if page URL changes</li>\n<li>Maintains internal link structure</li>\n</ul>\n<h4>Post</h4>\n<ul>\n<li>Link to a specific blog post</li>\n<li>Useful for featuring important articles</li>\n<li>Updates automatically if post URL changes</li>\n</ul>\n<h4>URL</h4>\n<ul>\n<li>Link to any external website (e.g., \"<a href=\"https://example.com/\">https://example.com</a>\")</li>\n<li>Create social media links</li>\n<li>Link to specific file downloads</li>\n<li>Add internal anchor links (e.g., \"#contact-section\")</li>\n<li>Point to email addresses (e.g., \"mailto:<a href=\"mailto:<EMAIL>\"><EMAIL></a>\")</li>\n<li>Link to phone numbers (e.g., \"tel:+1234567890\")</li>\n</ul>\n<h4>Group</h4>\n<ul>\n<li>Creates a container for other menu items</li>\n<li>Appears as a dropdown menu when hovered</li>\n<li>Can contain any mix of Pages, Posts, URLs, or other Groups</li>\n<li>Perfect for organizing related content (e.g., \"Services\", \"Products\")</li>\n<li>Can be nested to create multi-level navigation</li>\n<li>Doesn't require a URL - acts as a category header</li>\n</ul>\n<p>Examples of Group Usage:</p>\n<div>\n<pre>About (Group) \n└─ Our Story (Page)\n└─ Team (Page)\n└─ Careers (URL - External job board)<br>\nServices (Group) \n└─ Consulting (Page) \n└─ Training (Group) \n└─ Online Courses (URL) \n└─ Workshops (Page) \n└─ Support (Page)\n</pre>\n<h3>Item Settings</h3>\n<h4>Title</h4>\n<p>The text shown to visitors in the menu</p>\n<h4>Parent Item</h4>\n<p>For creating nested navigation:</p>\n<ul>\n<li>Select another item as parent</li>\n<li>Creates dropdown menus</li>\n<li>Builds hierarchical structure</li>\n</ul>\n<h4>Sort Order</h4>\n<p>Control the sequence of menu items:</p>\n<ul>\n<li>Drag and drop to reorder</li>\n<li>Items display in ascending order</li>\n<li>Groups show as dropdowns</li>\n</ul>\n<h2>Best Practices</h2>\n<h3>Structure</h3>\n<ul>\n<li>Keep main navigation simple</li>\n<li>Limit dropdown levels to 2-3</li>\n<li>Group related items logically</li>\n<li>Use clear, concise labels</li>\n</ul>\n<h3>Usability</h3>\n<ul>\n<li>Ensure all items are reachable</li>\n<li>Test on mobile devices</li>\n<li>Review links regularly</li>\n<li>Keep labels brief but descriptive</li>\n</ul>\n</div>", "title": "{{title}}"}, "readonly": false, "required": false, "sort": 5, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "navigation", "field": "date_created", "type": "timestamp", "meta": {"collection": "navigation", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "navigation", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "navigation", "field": "user_created", "type": "uuid", "meta": {"collection": "navigation", "conditions": null, "display": "user", "display_options": null, "field": "user_created", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 2, "special": ["user-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_created", "table": "navigation", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "navigation", "field": "date_updated", "type": "timestamp", "meta": {"collection": "navigation", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 3, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "navigation", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "navigation", "field": "user_updated", "type": "uuid", "meta": {"collection": "navigation", "conditions": null, "display": "user", "display_options": null, "field": "user_updated", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 4, "special": ["user-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_updated", "table": "navigation", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "navigation_items", "field": "id", "type": "uuid", "meta": {"collection": "navigation_items", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": ["uuid"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "id", "table": "navigation_items", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "navigation_items", "field": "navigation", "type": "string", "meta": {"collection": "navigation_items", "conditions": null, "display": null, "display_options": null, "field": "navigation", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": "Navigation menu that the individual links belong to.", "options": null, "readonly": false, "required": false, "sort": 7, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "navigation", "table": "navigation_items", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "navigation", "foreign_key_column": "id"}}, {"collection": "navigation_items", "field": "page", "type": "uuid", "meta": {"collection": "navigation_items", "conditions": [{"hidden": false, "name": "IF page", "options": {"enableCreate": true, "enableSelect": true}, "rule": {"_and": [{"type": {"_eq": "page"}}]}}, {"hidden": true, "name": "Hide If Has Children", "options": {"enableCreate": true, "enableSelect": true}, "rule": {"_and": [{"has_children": {"_eq": true}}]}}], "display": "related-values", "display_options": {"template": "{{title}}"}, "field": "page", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": "The internal page to link to.", "options": {"template": "{{title}}"}, "readonly": false, "required": false, "sort": 10, "special": ["m2o"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "page", "table": "navigation_items", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "pages", "foreign_key_column": "id"}}, {"collection": "navigation_items", "field": "parent", "type": "uuid", "meta": {"collection": "navigation_items", "conditions": null, "display": "related-values", "display_options": {"template": "{{title}}"}, "field": "parent", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": "The parent navigation item.", "options": {"template": "{{title}}"}, "readonly": false, "required": false, "sort": 14, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "parent", "table": "navigation_items", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "navigation_items", "foreign_key_column": "id"}}, {"collection": "navigation_items", "field": "sort", "type": "integer", "meta": {"collection": "navigation_items", "conditions": null, "display": null, "display_options": null, "field": "sort", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "sort", "table": "navigation_items", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "navigation_items", "field": "title", "type": "string", "meta": {"collection": "navigation_items", "conditions": null, "display": "raw", "display_options": null, "field": "title", "group": null, "hidden": false, "interface": "input", "note": "Label shown to the user for the menu item.", "options": null, "readonly": false, "required": true, "sort": 8, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "title", "table": "navigation_items", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "navigation_items", "field": "type", "type": "string", "meta": {"collection": "navigation_items", "conditions": [{"hidden": true, "name": "Hide if Children", "options": {"allowOther": false, "iconOff": "radio_button_unchecked", "iconOn": "radio_button_checked"}, "rule": {"_and": [{"has_children": {"_eq": true}}]}}], "display": "labels", "display_options": {"choices": [{"icon": "web_asset", "text": "Page", "value": "page"}, {"icon": "article", "text": "Post", "value": "post"}, {"icon": "link", "text": "URL", "value": "url"}, {"icon": "tab_group", "text": "Group", "value": "group"}]}, "field": "type", "group": null, "hidden": false, "interface": "select-dropdown", "note": "What type of link is this? Page and Post allow you to link to internal content. URL is for external content. Group can contain other menu items.", "options": {"choices": [{"icon": "web_asset", "text": "Page", "value": "page"}, {"icon": "article", "text": "Post", "value": "post"}, {"icon": "link", "text": "URL", "value": "url"}, {"icon": "tab_group", "text": "Group", "value": "group"}]}, "readonly": false, "required": false, "sort": 9, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "type", "table": "navigation_items", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "navigation_items", "field": "url", "type": "string", "meta": {"collection": "navigation_items", "conditions": [{"hidden": false, "name": "IF url", "options": {"clear": false, "font": "sans-serif", "masked": false, "slug": false, "trim": false}, "rule": {"_and": [{"type": {"_eq": "url"}}]}}, {"hidden": true, "name": "Hide If Had Children", "options": {"clear": false, "font": "sans-serif", "masked": false, "slug": false, "trim": false}, "rule": {"_and": [{"has_children": {"_eq": true}}]}}], "display": "raw", "display_options": null, "field": "url", "group": null, "hidden": true, "interface": "input", "note": "The URL to link to. Could be relative (ie `/my-page`) or a full external URL (ie `https://docs.directus.io`)", "options": null, "readonly": false, "required": false, "sort": 12, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "url", "table": "navigation_items", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "navigation_items", "field": "children", "type": "alias", "meta": {"collection": "navigation_items", "conditions": [{"hidden": false, "name": "Show If Group", "options": {"enableCreate": true, "enableLink": false, "enableSearchFilter": false, "enableSelect": true, "layout": "list", "limit": 15, "sortDirection": "+"}, "rule": {"_and": [{"type": {"_eq": "group"}}]}}], "display": "related-values", "display_options": {"template": "{{title}} • {{type}}"}, "field": "children", "group": null, "hidden": true, "interface": "list-o2m", "note": "Add child menu items within the group.", "options": {"filter": {"_and": [{"_and": [{"navigation": {"_null": true}}, {"parent": {"navigation": {"_null": true}}}]}]}, "template": "{{title}} • {{type}}"}, "readonly": false, "required": false, "sort": 13, "special": ["o2m"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "navigation_items", "field": "post", "type": "uuid", "meta": {"collection": "navigation_items", "conditions": [{"hidden": false, "name": "Show If Type = Post", "options": {"enableCreate": true, "enableSelect": true}, "rule": {"_and": [{"type": {"_eq": "post"}}]}}], "display": null, "display_options": null, "field": "post", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": "The internal post to link to.", "options": {"template": "{{title}}"}, "readonly": false, "required": false, "sort": 11, "special": ["m2o"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "post", "table": "navigation_items", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "posts", "foreign_key_column": "id"}}, {"collection": "navigation_items", "field": "date_created", "type": "timestamp", "meta": {"collection": "navigation_items", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 3, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "navigation_items", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "navigation_items", "field": "user_created", "type": "uuid", "meta": {"collection": "navigation_items", "conditions": null, "display": "user", "display_options": null, "field": "user_created", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 4, "special": ["user-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_created", "table": "navigation_items", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "navigation_items", "field": "date_updated", "type": "timestamp", "meta": {"collection": "navigation_items", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 5, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "navigation_items", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "navigation_items", "field": "user_updated", "type": "uuid", "meta": {"collection": "navigation_items", "conditions": null, "display": "user", "display_options": null, "field": "user_updated", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 6, "special": ["user-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_updated", "table": "navigation_items", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "page_blocks", "field": "id", "type": "uuid", "meta": {"collection": "page_blocks", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": ["uuid"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "id", "table": "page_blocks", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "page_blocks", "field": "sort", "type": "integer", "meta": {"collection": "page_blocks", "conditions": null, "display": null, "display_options": null, "field": "sort", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "sort", "table": "page_blocks", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "page_blocks", "field": "page", "type": "uuid", "meta": {"collection": "page_blocks", "conditions": null, "display": null, "display_options": null, "field": "page", "group": null, "hidden": true, "interface": null, "note": "The id of the page that this block belongs to.", "options": null, "readonly": false, "required": false, "sort": 7, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "page", "table": "page_blocks", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "pages", "foreign_key_column": "id"}}, {"collection": "page_blocks", "field": "item", "type": "string", "meta": {"collection": "page_blocks", "conditions": null, "display": null, "display_options": null, "field": "item", "group": null, "hidden": true, "interface": null, "note": "The data for the block.", "options": null, "readonly": false, "required": false, "sort": 8, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "item", "table": "page_blocks", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "page_blocks", "field": "collection", "type": "string", "meta": {"collection": "page_blocks", "conditions": null, "display": "labels", "display_options": {"border": true, "color": "#18222F", "format": true}, "field": "collection", "group": null, "hidden": true, "interface": null, "note": "The collection (type of block).", "options": null, "readonly": false, "required": false, "sort": 9, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "collection", "table": "page_blocks", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "page_blocks", "field": "hide_block", "type": "boolean", "meta": {"collection": "page_blocks", "conditions": null, "display": null, "display_options": null, "field": "hide_block", "group": null, "hidden": false, "interface": "boolean", "note": "Temporarily hide this block on the website without having to remove it from your page.", "options": null, "readonly": false, "required": false, "sort": 10, "special": ["cast-boolean"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "hide_block", "table": "page_blocks", "data_type": "boolean", "default_value": false, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "page_blocks", "field": "background", "type": "string", "meta": {"collection": "page_blocks", "conditions": null, "display": null, "display_options": null, "field": "background", "group": null, "hidden": false, "interface": "select-dropdown", "note": "Background color for the block to create contrast. Does not control dark or light mode for the entire site.", "options": {"choices": [{"icon": "light_mode", "text": "<PERSON><PERSON><PERSON>", "value": "light"}, {"icon": "dark_mode", "text": "Dark", "value": "dark"}]}, "readonly": false, "required": false, "sort": 11, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "background", "table": "page_blocks", "data_type": "character varying", "default_value": "light", "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "page_blocks", "field": "date_created", "type": "timestamp", "meta": {"collection": "page_blocks", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 3, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "page_blocks", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "page_blocks", "field": "user_created", "type": "uuid", "meta": {"collection": "page_blocks", "conditions": null, "display": "user", "display_options": null, "field": "user_created", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 4, "special": ["user-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_created", "table": "page_blocks", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "page_blocks", "field": "date_updated", "type": "timestamp", "meta": {"collection": "page_blocks", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 5, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "page_blocks", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "page_blocks", "field": "user_updated", "type": "uuid", "meta": {"collection": "page_blocks", "conditions": null, "display": "user", "display_options": null, "field": "user_updated", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 6, "special": ["user-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_updated", "table": "page_blocks", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "pages", "field": "id", "type": "uuid", "meta": {"collection": "pages", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": ["uuid"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "id", "table": "pages", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "pages", "field": "sort", "type": "integer", "meta": {"collection": "pages", "conditions": null, "display": null, "display_options": null, "field": "sort", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "sort", "table": "pages", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "pages", "field": "title", "type": "string", "meta": {"collection": "pages", "conditions": null, "display": null, "display_options": null, "field": "title", "group": "meta_content", "hidden": false, "interface": "input", "note": "The title of this page.", "options": {"placeholder": "About Us"}, "readonly": false, "required": true, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "title", "table": "pages", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "pages", "field": "permalink", "type": "string", "meta": {"collection": "pages", "conditions": null, "display": "formatted-value", "display_options": {"font": "monospace"}, "field": "permalink", "group": "meta_content", "hidden": false, "interface": "input", "note": "Unique URL for this page (start with `/`, can have multiple segments `/about/me`)).", "options": {"font": "monospace", "placeholder": "/about-us", "trim": true}, "readonly": false, "required": true, "sort": 2, "special": null, "translations": null, "validation": {"_and": [{"permalink": {"_regex": "^/(?:[a-z0-9]+(?:-[a-z0-9]+)*(?:/[a-z0-9]+(?:-[a-z0-9]+)*)*)?$"}}]}, "validation_message": "Please use lowercase letters, numbers, and hyphens in your permalink, starting with a slash (/) and without a trailing slash", "width": "half"}, "schema": {"name": "permalink", "table": "pages", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": true, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "pages", "field": "blocks", "type": "alias", "meta": {"collection": "pages", "conditions": null, "display": "related-values", "display_options": {"template": "{{collection}}"}, "field": "blocks", "group": "meta_content", "hidden": false, "interface": "list-m2a", "note": "Create and arrange different content blocks (like text, images, or videos) to build your page.", "options": {"enableSelect": false, "prefix": null}, "readonly": false, "required": false, "sort": 7, "special": ["m2a"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "pages", "field": "status", "type": "string", "meta": {"collection": "pages", "conditions": null, "display": "labels", "display_options": {"choices": [{"color": "#A2B5CD", "icon": "draft_orders", "text": "$t:draft", "value": "draft"}, {"color": "#FFA439", "icon": "rate_review", "text": "In Review", "value": "in_review"}, {"color": "#2ECDA7", "icon": "check", "text": "$t:published", "value": "published"}]}, "field": "status", "group": "meta_content", "hidden": false, "interface": "select-dropdown", "note": "Is this page published?", "options": {"choices": [{"color": "#A2B5CD", "icon": "draft_orders", "text": "$t:draft", "value": "draft"}, {"color": "#FFA439", "icon": "rate_review", "text": "In Review", "value": "in_review"}, {"color": "#2ECDA7", "icon": "check", "text": "$t:published", "value": "published"}]}, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "status", "table": "pages", "data_type": "character varying", "default_value": "draft", "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "pages", "field": "meta_m2a_button", "type": "alias", "meta": {"collection": "pages", "conditions": null, "display": null, "display_options": null, "field": "meta_m2a_button", "group": "meta_content", "hidden": false, "interface": "directus-labs-experimental-m2a-interface", "note": null, "options": {"target": "above"}, "readonly": false, "required": false, "sort": 8, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "pages", "field": "meta_divider_content", "type": "alias", "meta": {"collection": "pages", "conditions": null, "display": null, "display_options": null, "field": "meta_divider_content", "group": "meta_content", "hidden": false, "interface": "presentation-divider", "note": null, "options": {"color": "#A2B5CD", "icon": "format_paragraph", "inlineTitle": true}, "readonly": false, "required": false, "sort": 5, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "pages", "field": "published_at", "type": "timestamp", "meta": {"collection": "pages", "conditions": [{"hidden": false, "name": "Show If Status = Published", "options": {"includeSeconds": false, "use24": true}, "rule": {"_and": [{"status": {"_eq": "published"}}]}}], "display": "datetime", "display_options": {"format": "short", "relative": true}, "field": "published_at", "group": "meta_content", "hidden": false, "interface": "datetime", "note": "Publish now or schedule for later.", "options": {"use24": false}, "readonly": false, "required": false, "sort": 4, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "published_at", "table": "pages", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "pages", "field": "meta_notice_pagebuilder", "type": "alias", "meta": {"collection": "pages", "conditions": null, "display": null, "display_options": null, "field": "meta_notice_pagebuilder", "group": "meta_content", "hidden": false, "interface": "presentation-notice", "note": null, "options": {"text": "Build dynamic pages quickly using ready-made blocks. <a href=\"https://directus.io/docs/tutorials/getting-started/create-reusable-blocks-with-many-to-any-relationships?ref=simple_cms_notices\" target=\"_blank\">See our documentation to learn more about the Page Builder</a>."}, "readonly": false, "required": false, "sort": 6, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "pages", "field": "meta_header_pages", "type": "alias", "meta": {"collection": "pages", "conditions": null, "display": null, "display_options": null, "field": "meta_header_pages", "group": null, "hidden": false, "interface": "super-header", "note": null, "options": {"actions": [{"actionType": "link", "icon": "arrow_outward", "label": "View on Website", "type": "normal", "url": "http://localhost:3000{{permalink}}"}, {"actionType": "link", "icon": "edit", "label": "Open in Visual Editor", "type": "normal", "url": "/admin/visual/http://localhost:3000{{permalink}}?visual-editing=true"}, {"actionType": "flow", "flow": {"collection": "directus_flows", "key": "a23110e1-700b-41b8-9f9e-ca0998b84a76"}, "icon": "copy_all", "label": "Duplicate Page", "type": "normal"}], "help": "<h2>Creating and Managing Pages</h2>\n<p>Pages are the foundation of your website, where you'll combine different content blocks to create engaging layouts. Each page can be built using a mix of blocks like Hero sections, Rich Text, Forms, and more.</p>\n<h3>Page Settings</h3>\n<h4>Title</h4>\n<p>The page title appears in browser tabs and search results. Make it clear and descriptive of the page's content. This is what visitors will see when they bookmark your page or share it on social media.</p>\n<h4>URL (Permalink)</h4>\n<ul>\n<li>Always starts with a forward slash (/)</li>\n<li>Use simple, lowercase words separated by hyphens</li>\n<li>Keep it short but descriptive</li>\n<li>Examples:\n<ul>\n<li>/about</li>\n<li>/contact-us</li>\n<li>/services/web-design</li>\n</ul>\n</li>\n</ul>\n<h4>Description</h4>\n<p>A brief summary of the page that appears in search results. Write 1-2 sentences that clearly explain what visitors will find on this page. Good descriptions help with SEO and encourage people to click through to your site.</p>\n<h4>Publishing Options</h4>\n<ul>\n<li><strong>Draft</strong>: Work on the page without making it public</li>\n<li><strong>In Review</strong>: Submit the page for review by other team members</li>\n<li><strong>Published</strong>: Make the page visible to the public</li>\n<li><strong>Schedule</strong>: Set a future publication date</li>\n</ul>\n<p><img src=\"/assets/5e93050a-6f17-4314-a7e5-f78bda425fea.png?width=2076&amp;height=1551\" alt=\"Help Blocks Content\" loading=\"lazy\"></p>\n<h2>Working with Blocks</h2>\n<p>Your page content is built by adding and arranging blocks. Think of blocks as building pieces that you can stack and rearrange to create your perfect page layout.</p>\n<h4>Tips for Building with Blocks</h4>\n<ul>\n<li>Start with a Hero block for main landing pages</li>\n<li>Use consistent block types for similar content across pages</li>\n<li>Consider the natural flow of information when ordering blocks</li>\n<li>Preview your page on both desktop and mobile before publishing</li>\n<li>You can temporarily hide blocks without deleting them using the \"Hide block\" toggle</li>\n</ul>\n<h4>Block Background Options</h4>\n<p>Each block can have either a light or dark background:</p>\n<ul>\n<li>Default (light): Default option, best for regular content sections</li>\n<li>Dark: Creates visual contrast, good for highlighting important sections</li>\n</ul>\n<h4>General Tips for All Blocks</h4>\n<ul>\n<li>Preview your changes before publishing</li>\n<li>Consider mobile viewing experience</li>\n<li>Keep related content in the same block type for consistency</li>\n<li>Use clear headlines and taglines to guide your visitors</li>\n<li>Make sure images are optimized for web use</li>\n<li>Test all interactive elements like forms and buttons</li>\n</ul>", "subtitle": "{{status}} • Published At: {{published_at}}", "title": "{{title}}"}, "readonly": false, "required": false, "sort": 7, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "fill"}}, {"collection": "pages", "field": "meta_tabs", "type": "alias", "meta": {"collection": "pages", "conditions": null, "display": null, "display_options": null, "field": "meta_tabs", "group": null, "hidden": false, "interface": "group-tabs", "note": null, "options": {"fillWidth": true}, "readonly": false, "required": false, "sort": 8, "special": ["alias", "no-data", "group"], "translations": [{"language": "en-US", "translation": "Tabs"}], "validation": null, "validation_message": null, "width": "full"}}, {"collection": "pages", "field": "meta_content", "type": "alias", "meta": {"collection": "pages", "conditions": null, "display": null, "display_options": null, "field": "meta_content", "group": "meta_tabs", "hidden": false, "interface": "group-raw", "note": null, "options": null, "readonly": false, "required": false, "sort": 1, "special": ["alias", "no-data", "group"], "translations": [{"language": "en-US", "translation": "Content"}], "validation": null, "validation_message": null, "width": "full"}}, {"collection": "pages", "field": "meta_seo", "type": "alias", "meta": {"collection": "pages", "conditions": null, "display": null, "display_options": null, "field": "meta_seo", "group": "meta_tabs", "hidden": false, "interface": "group-raw", "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": ["alias", "no-data", "group"], "translations": [{"language": "en-US", "translation": "SEO"}], "validation": null, "validation_message": null, "width": "full"}}, {"collection": "pages", "field": "seo", "type": "json", "meta": {"collection": "pages", "conditions": null, "display": "seo-display", "display_options": {"showSearchPreview": true}, "field": "seo", "group": "meta_seo", "hidden": false, "interface": "seo-interface", "note": null, "options": {"showOgImage": true, "titleTemplate": "{{title}}"}, "readonly": false, "required": false, "sort": 1, "special": ["cast-json"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "seo", "table": "pages", "data_type": "json", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "pages", "field": "date_created", "type": "timestamp", "meta": {"collection": "pages", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 3, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "pages", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "pages", "field": "user_created", "type": "uuid", "meta": {"collection": "pages", "conditions": null, "display": "user", "display_options": null, "field": "user_created", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 4, "special": ["user-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_created", "table": "pages", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "pages", "field": "date_updated", "type": "timestamp", "meta": {"collection": "pages", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 5, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "pages", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "pages", "field": "user_updated", "type": "uuid", "meta": {"collection": "pages", "conditions": null, "display": "user", "display_options": null, "field": "user_updated", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 6, "special": ["user-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_updated", "table": "pages", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "posts", "field": "content", "type": "text", "meta": {"collection": "posts", "conditions": null, "display": "formatted-value", "display_options": {"format": true}, "field": "content", "group": "meta_content", "hidden": false, "interface": "input-rich-text-html", "note": "Rich text content of your blog post.", "options": {"folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "toolbar": ["blockquote", "bold", "bullist", "code", "customImage", "customLink", "customMedia", "fullscreen", "h1", "h2", "h3", "hr", "italic", "numlist", "redo", "removeformat", "underline", "undo"]}, "readonly": false, "required": false, "sort": 11, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "content", "table": "posts", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "posts", "field": "id", "type": "uuid", "meta": {"collection": "posts", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": ["uuid"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "id", "table": "posts", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "posts", "field": "image", "type": "uuid", "meta": {"collection": "posts", "conditions": null, "display": "image", "display_options": null, "field": "image", "group": "meta_content", "hidden": false, "interface": "file-image", "note": "Featured image for this post. Used in cards linking to the post and in the post detail page.", "options": {"crop": false, "folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9"}, "readonly": false, "required": false, "sort": 9, "special": ["file"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "image", "table": "posts", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_files", "foreign_key_column": "id"}}, {"collection": "posts", "field": "slug", "type": "string", "meta": {"collection": "posts", "conditions": null, "display": "formatted-value", "display_options": {"font": "monospace"}, "field": "slug", "group": "meta_content", "hidden": false, "interface": "extension-wpslug", "note": "Unique URL for this post (e.g., `yoursite.com/posts/{{your-slug}}`)", "options": {"font": "monospace", "placeholder": null, "template": "{{title}}"}, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "slug", "table": "posts", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "posts", "field": "sort", "type": "integer", "meta": {"collection": "posts", "conditions": null, "display": null, "display_options": null, "field": "sort", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "sort", "table": "posts", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "posts", "field": "status", "type": "string", "meta": {"collection": "posts", "conditions": null, "display": "labels", "display_options": {"choices": [{"color": "#A2B5CD", "icon": "draft_orders", "text": "$t:draft", "value": "draft"}, {"color": "#FFA439", "icon": "rate_review", "text": "In Review", "value": "in_review"}, {"color": "#2ECDA7", "icon": "check", "text": "$t:published", "value": "published"}]}, "field": "status", "group": "meta_content", "hidden": false, "interface": "select-dropdown", "note": "Is this post published?", "options": {"choices": [{"color": "#A2B5CD", "icon": "draft_orders", "text": "$t:draft", "value": "draft"}, {"color": "#FFA439", "icon": "rate_review", "text": "In Review", "value": "in_review"}, {"color": "#2ECDA7", "icon": "check", "text": "$t:published", "value": "published"}]}, "readonly": false, "required": false, "sort": 4, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "status", "table": "posts", "data_type": "character varying", "default_value": "draft", "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "posts", "field": "title", "type": "string", "meta": {"collection": "posts", "conditions": null, "display": null, "display_options": null, "field": "title", "group": "meta_content", "hidden": false, "interface": "input", "note": "Title of the blog post (used in page title and meta tags)", "options": {"placeholder": "Essential tips for first-time home buyers"}, "readonly": false, "required": true, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "title", "table": "posts", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "posts", "field": "description", "type": "text", "meta": {"collection": "posts", "conditions": null, "display": null, "display_options": null, "field": "description", "group": "meta_content", "hidden": false, "interface": "input-multiline", "note": "Short summary of the blog post to entice readers.", "options": {"placeholder": "Discover key strategies for navigating the home buying process, from budgeting to closing. Learn how to avoid common pitfalls and make informed decisions."}, "readonly": false, "required": false, "sort": 6, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "description", "table": "posts", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "posts", "field": "author", "type": "uuid", "meta": {"collection": "posts", "conditions": null, "display": "user", "display_options": {"circle": true}, "field": "author", "group": "meta_content", "hidden": false, "interface": "select-dropdown-m2o", "note": "Select the team member who wrote this post", "options": {"template": "{{avatar.$thumbnail}} {{first_name}} {{last_name}}"}, "readonly": false, "required": false, "sort": 7, "special": ["m2o"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "author", "table": "posts", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "posts", "field": "published_at", "type": "timestamp", "meta": {"collection": "posts", "conditions": [{"hidden": false, "name": "Show If Status = Published", "options": {"includeSeconds": false, "use24": true}, "rule": {"_and": [{"status": {"_eq": "published"}}]}}], "display": "datetime", "display_options": {"format": "short", "relative": true}, "field": "published_at", "group": "meta_content", "hidden": false, "interface": "datetime", "note": "Publish now or schedule for later.", "options": {"relative": true, "use24": false}, "readonly": false, "required": false, "sort": 5, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "published_at", "table": "posts", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "posts", "field": "meta_divider_info", "type": "alias", "meta": {"collection": "posts", "conditions": null, "display": null, "display_options": null, "field": "meta_divider_info", "group": "meta_content", "hidden": false, "interface": "presentation-divider", "note": null, "options": {"color": "#A2B5CD", "icon": "calendar_month", "inlineTitle": true}, "readonly": false, "required": false, "sort": 3, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "posts", "field": "meta_header_content", "type": "alias", "meta": {"collection": "posts", "conditions": null, "display": null, "display_options": null, "field": "meta_header_content", "group": "meta_content", "hidden": false, "interface": "super-header", "note": null, "options": {"actions": [{"actionType": "flow", "flow": {"collection": "directus_flows", "key": "5915dd55-fff8-4d47-b48c-a0e42e5033c1"}, "icon": "text_increase", "label": "AI Ghostwriter", "type": "normal"}], "help": "<p><strong>Need inspiration?</strong> Use Flows like AI Ghostwriter to help draft your content.</p>\n<p><em>Note: This uses OpenAI so you'll need to add your API key in the&nbsp;<a href=\"/admin/content/globals\">Globals</a> collection first.</em></p>", "icon": "text_snippet", "title": "Content"}, "readonly": false, "required": false, "sort": 10, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "posts", "field": "meta_header_image", "type": "alias", "meta": {"collection": "posts", "conditions": null, "display": null, "display_options": null, "field": "meta_header_image", "group": "meta_content", "hidden": false, "interface": "super-header", "note": null, "options": {"actions": [{"actionType": "flow", "flow": {"collection": "directus_flows", "key": "d4bbac48-a444-49e0-aedb-9af5273b88df"}, "icon": "image", "label": "AI Image Generator", "type": "normal"}], "help": "<p><strong>Need inspiration?</strong> Use Flows like AI Image Generator to generate on-brand images for your post using AI.</p>\n<p><em>Note: This uses OpenAI so you'll need to add your API key in the&nbsp;<a href=\"/admin/content/globals\">Globals</a> collection first.</em></p>", "icon": "image", "title": "Image"}, "readonly": false, "required": false, "sort": 8, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "posts", "field": "meta_header_posts", "type": "alias", "meta": {"collection": "posts", "conditions": null, "display": null, "display_options": null, "field": "meta_header_posts", "group": null, "hidden": false, "interface": "super-header", "note": null, "options": {"actions": [{"actionType": "link", "icon": "arrow_outward", "label": "View on Website", "type": "normal", "url": "http://localhost:3000/blog/{{slug}}"}, {"actionType": "link", "icon": "edit", "label": "Open in Visual Editor", "type": "normal", "url": "/admin/visual/http://localhost:3000/blog/{{slug}}?visual-editing=true"}, {"actionType": "flow", "flow": {"collection": "directus_flows", "key": "3172d021-0b0f-4d4d-bcca-c5c46eb8fadf"}, "icon": "approval_delegation", "label": "Request Review", "type": "normal"}], "help": "<h2>Managing Blog Posts</h2>\n<p>Blog posts are a powerful way to share your content, news, and updates with your audience. Each post can include rich text content, images, and metadata to help with organization and discovery.</p>\n<h3>Post Settings</h3>\n<h4>Title</h4>\n<p>The main headline of your blog post. This appears at the top of the post and in preview cards across your site. Make it:</p>\n<ul>\n<li>Engaging and descriptive</li>\n<li>Clear about the post's content</li>\n<li>Optimized for search engines (aim for 50-60 characters)</li>\n</ul>\n<h4>Description</h4>\n<p>A brief preview of your post that appears in blog listings and search results. Write 1-2 compelling sentences that:</p>\n<ul>\n<li>Summarize the main point of your article</li>\n<li>Entice readers to click through</li>\n<li>Include relevant keywords naturally</li>\n</ul>\n<h4>Featured Image</h4>\n<p>The main visual for your post that appears:</p>\n<ul>\n<li>At the top of the post</li>\n<li>In preview cards across your site</li>\n<li>When sharing on social media Use high-quality, relevant images that capture attention and support your content.</li>\n</ul>\n<h4>URL Slug</h4>\n<p>The unique portion of the URL for this post. For example, in \"yoursite.com/posts/my-first-post\", \"my-first-post\" is the slug.</p>\n<ul>\n<li>Use lowercase letters and hyphens</li>\n<li>Keep it short but descriptive</li>\n<li>Include relevant keywords when possible</li>\n<li>Avoid special characters</li>\n</ul>\n<h4>Content</h4>\n<p>The main body of your post, supporting rich text formatting including:</p>\n<ul>\n<li>Headings and subheadings</li>\n<li>Paragraphs with formatting (bold, italic, etc.)</li>\n<li>Links to other content</li>\n<li>Bulleted and numbered lists</li>\n<li>Images and media</li>\n</ul>\n<h4>Author</h4>\n<p>Select the team member who wrote the post. This helps:</p>\n<ul>\n<li>Give credit to your writers</li>\n<li>Build authority for your content</li>\n<li>Allow readers to find more posts by the same author</li>\n</ul>\n<h4>Publishing Options</h4>\n<ul>\n<li><strong>Draft</strong>: Work on the post privately</li>\n<li><strong>In Review</strong>: Submit for team review</li>\n<li><strong>Published</strong>: Make the post public</li>\n<li><strong>Schedule</strong>: Set a future publication date and time</li>\n</ul>\n<h3>Best Practices for Blog Posts</h3>\n<h4>Writing Tips</h4>\n<ul>\n<li>Start with a strong opening paragraph</li>\n<li>Use subheadings to break up long content</li>\n<li>Keep paragraphs short (3-4 sentences)</li>\n<li>Include relevant internal and external links</li>\n<li>End with a clear conclusion or call to action</li>\n</ul>\n<h4>SEO Considerations</h4>\n<ul>\n<li>Include your main keyword in the title</li>\n<li>Use descriptive alt text for images</li>\n<li>Write naturally for your audience</li>\n<li>Link to related content on your site</li>\n<li>Use appropriate heading hierarchy</li>\n</ul>\n<h4>Image Guidelines</h4>\n<ul>\n<li>Choose relevant, high-quality featured images</li>\n<li>Optimize images for web performance</li>\n<li>Use consistent image sizes</li>\n<li>Include descriptive alt text</li>\n<li>Consider image placement within content</li>\n</ul>", "icon": null, "subtitle": "{{status}} • Published At: {{published_at}}", "title": "{{title}}"}, "readonly": false, "required": false, "sort": 7, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "fill"}}, {"collection": "posts", "field": "meta_tabs", "type": "alias", "meta": {"collection": "posts", "conditions": null, "display": null, "display_options": null, "field": "meta_tabs", "group": null, "hidden": false, "interface": "group-tabs", "note": null, "options": {"fillWidth": true}, "readonly": false, "required": false, "sort": 8, "special": ["alias", "no-data", "group"], "translations": [{"language": "en-US", "translation": "Tabs"}], "validation": null, "validation_message": null, "width": "full"}}, {"collection": "posts", "field": "meta_content", "type": "alias", "meta": {"collection": "posts", "conditions": null, "display": null, "display_options": null, "field": "meta_content", "group": "meta_tabs", "hidden": false, "interface": "group-raw", "note": null, "options": null, "readonly": false, "required": false, "sort": 1, "special": ["alias", "no-data", "group"], "translations": [{"language": "en-US", "translation": "Content"}], "validation": null, "validation_message": null, "width": "full"}}, {"collection": "posts", "field": "meta_seo", "type": "alias", "meta": {"collection": "posts", "conditions": null, "display": null, "display_options": null, "field": "meta_seo", "group": "meta_tabs", "hidden": false, "interface": "group-raw", "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": ["alias", "no-data", "group"], "translations": [{"language": "en-US", "translation": "SEO"}], "validation": null, "validation_message": null, "width": "full"}}, {"collection": "posts", "field": "seo", "type": "json", "meta": {"collection": "posts", "conditions": null, "display": "seo-display", "display_options": {"showSearchPreview": true}, "field": "seo", "group": "meta_seo", "hidden": false, "interface": "seo-interface", "note": null, "options": {"additionalFields": null, "descriptionTemplate": "{{description}}", "showOgImage": true, "titleTemplate": "{{title}}"}, "readonly": false, "required": false, "sort": 1, "special": ["cast-json"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "seo", "table": "posts", "data_type": "json", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "posts", "field": "date_created", "type": "timestamp", "meta": {"collection": "posts", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 3, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "posts", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "posts", "field": "user_created", "type": "uuid", "meta": {"collection": "posts", "conditions": null, "display": "user", "display_options": null, "field": "user_created", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 4, "special": ["user-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_created", "table": "posts", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "posts", "field": "date_updated", "type": "timestamp", "meta": {"collection": "posts", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 5, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "posts", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "posts", "field": "user_updated", "type": "uuid", "meta": {"collection": "posts", "conditions": null, "display": "user", "display_options": null, "field": "user_updated", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 6, "special": ["user-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_updated", "table": "posts", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "redirects", "field": "id", "type": "uuid", "meta": {"collection": "redirects", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": ["uuid"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "redirects", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "redirects", "field": "response_code", "type": "string", "meta": {"collection": "redirects", "conditions": null, "display": "labels", "display_options": {"choices": [{"description": "<PERSON> has permanently moved to the new URL.", "icon": "sync_lock", "icon_type": "icon", "text": "Permanent (301)", "value": "301"}, {"description": "Temporarily redirect traffic to the page to the new URL.", "icon": "build_circle", "icon_type": "icon", "text": "Temporary (302)", "value": "302"}], "conditionalFormatting": null}, "field": "response_code", "group": null, "hidden": false, "interface": "radio-cards-interface", "note": null, "options": {"choices": [{"description": "<PERSON> has permanently moved to the new URL.", "icon": "sync_lock", "icon_type": "icon", "text": "Permanent (301)", "value": "301"}, {"description": "Temporarily redirect traffic to the page to the new URL.", "icon": "build_circle", "icon_type": "icon", "text": "Temporary (302)", "value": "302"}], "gridSize": 2}, "readonly": false, "required": false, "sort": 9, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "response_code", "table": "redirects", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "redirects", "field": "url_from", "type": "string", "meta": {"collection": "redirects", "conditions": null, "display": "formatted-value", "display_options": {"font": "monospace"}, "field": "url_from", "group": null, "hidden": false, "interface": "input", "note": "Old URL has to be relative to the site (ie `/blog` or `/news`). It cannot be a full url like (https://example.com/blog)", "options": {"font": "monospace", "iconLeft": "link"}, "readonly": false, "required": false, "sort": 7, "special": null, "translations": [{"language": "en-US", "translation": "Original URL"}], "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "url_from", "table": "redirects", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "redirects", "field": "url_to", "type": "string", "meta": {"collection": "redirects", "conditions": null, "display": "formatted-value", "display_options": {"font": "monospace"}, "field": "url_to", "group": null, "hidden": false, "interface": "input", "note": "The URL you're redirecting to. This can be a relative url (/resources/matt-is-cool) or a full url (https://example.com/blog).", "options": {"font": "monospace", "iconLeft": "add_link"}, "readonly": false, "required": false, "sort": 8, "special": null, "translations": [{"language": "en-US", "translation": "Redirect To"}], "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "url_to", "table": "redirects", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "redirects", "field": "meta_header_redirects", "type": "alias", "meta": {"collection": "redirects", "conditions": null, "display": null, "display_options": null, "field": "meta_header_redirects", "group": null, "hidden": false, "interface": "super-header", "note": null, "options": {"help": "<h2>What Are Redirects?</h2>\n<p>Redirects automatically send visitors from old URLs to new ones when content moves or changes.</p>\n<h2>Setting Up Redirects</h2>\n<ul>\n<li><strong>URL From</strong>: The old path (must be relative, like <code>/old-about-us</code>)</li>\n<li><strong>URL To</strong>: Where visitors should go (can be relative or full URL)</li>\n<li><strong>Response Code</strong>: Choose 301 (permanent) or 302 (temporary)</li>\n</ul>\n<h2>Automatic Redirects</h2>\n<p>When you change a published page's URL, there's a Flow that automatically creates redirects to prevent broken links. You can disable this in the Settings -&gt; Flows.</p>\n<h2>Best Practices</h2>\n<ul>\n<li>Keep URLs descriptive but concise</li>\n<li>Regularly review and clean up old redirects</li>\n<li>Avoid redirect chains</li>\n<li>Test redirects before publishing</li>\n</ul>", "subtitle": "Redirecting to: {{url_to}}", "title": "{{url_from}} • {{response_code}}"}, "readonly": false, "required": false, "sort": 6, "special": ["alias", "no-data"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "redirects", "field": "note", "type": "text", "meta": {"collection": "redirects", "conditions": null, "display": null, "display_options": null, "field": "note", "group": null, "hidden": false, "interface": "input-multiline", "note": "Short explanation of why the redirect was created.", "options": {"placeholder": null}, "readonly": false, "required": false, "sort": 10, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "note", "table": "redirects", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "redirects", "field": "date_created", "type": "timestamp", "meta": {"collection": "redirects", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 2, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "redirects", "data_type": "timestamp with time zone", "default_value": "CURRENT_TIMESTAMP", "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "redirects", "field": "user_created", "type": "uuid", "meta": {"collection": "redirects", "conditions": null, "display": "user", "display_options": null, "field": "user_created", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 3, "special": ["user-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_created", "table": "redirects", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "redirects", "field": "date_updated", "type": "timestamp", "meta": {"collection": "redirects", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 4, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "redirects", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "redirects", "field": "user_updated", "type": "uuid", "meta": {"collection": "redirects", "conditions": null, "display": "user", "display_options": null, "field": "user_updated", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 5, "special": ["user-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_updated", "table": "redirects", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}], "relations": [{"collection": "ai_prompts", "field": "user_created", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "ai_prompts", "many_field": "user_created", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "ai_prompts", "column": "user_created", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "ai_prompts_user_created_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "ai_prompts", "field": "user_updated", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "ai_prompts", "many_field": "user_updated", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "ai_prompts", "column": "user_updated", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "ai_prompts_user_updated_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_button", "field": "page", "related_collection": "pages", "meta": {"junction_field": null, "many_collection": "block_button", "many_field": "page", "one_allowed_collections": null, "one_collection": "pages", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_button", "column": "page", "foreign_key_table": "pages", "foreign_key_column": "id", "constraint_name": "block_button_page_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_button", "field": "post", "related_collection": "posts", "meta": {"junction_field": null, "many_collection": "block_button", "many_field": "post", "one_allowed_collections": null, "one_collection": "posts", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_button", "column": "post", "foreign_key_table": "posts", "foreign_key_column": "id", "constraint_name": "block_button_post_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_button", "field": "button_group", "related_collection": "block_button_group", "meta": {"junction_field": null, "many_collection": "block_button", "many_field": "button_group", "one_allowed_collections": null, "one_collection": "block_button_group", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": "buttons", "sort_field": "sort"}, "schema": {"table": "block_button", "column": "button_group", "foreign_key_table": "block_button_group", "foreign_key_column": "id", "constraint_name": "block_button_button_group_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_button", "field": "user_created", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "block_button", "many_field": "user_created", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_button", "column": "user_created", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "block_button_user_created_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_button", "field": "user_updated", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "block_button", "many_field": "user_updated", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_button", "column": "user_updated", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "block_button_user_updated_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_button_group", "field": "user_created", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "block_button_group", "many_field": "user_created", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_button_group", "column": "user_created", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "block_button_group_user_created_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_button_group", "field": "user_updated", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "block_button_group", "many_field": "user_updated", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_button_group", "column": "user_updated", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "block_button_group_user_updated_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_form", "field": "form", "related_collection": "forms", "meta": {"junction_field": null, "many_collection": "block_form", "many_field": "form", "one_allowed_collections": null, "one_collection": "forms", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_form", "column": "form", "foreign_key_table": "forms", "foreign_key_column": "id", "constraint_name": "block_form_form_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_form", "field": "user_created", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "block_form", "many_field": "user_created", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_form", "column": "user_created", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "block_form_user_created_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_form", "field": "user_updated", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "block_form", "many_field": "user_updated", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_form", "column": "user_updated", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "block_form_user_updated_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_gallery", "field": "user_created", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "block_gallery", "many_field": "user_created", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_gallery", "column": "user_created", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "block_gallery_user_created_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_gallery", "field": "user_updated", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "block_gallery", "many_field": "user_updated", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_gallery", "column": "user_updated", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "block_gallery_user_updated_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_gallery_items", "field": "directus_file", "related_collection": "directus_files", "meta": {"junction_field": "block_gallery", "many_collection": "block_gallery_items", "many_field": "directus_file", "one_allowed_collections": null, "one_collection": "directus_files", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_gallery_items", "column": "directus_file", "foreign_key_table": "directus_files", "foreign_key_column": "id", "constraint_name": "block_gallery_items_directus_file_foreign", "on_update": "NO ACTION", "on_delete": "CASCADE"}}, {"collection": "block_gallery_items", "field": "block_gallery", "related_collection": "block_gallery", "meta": {"junction_field": "directus_file", "many_collection": "block_gallery_items", "many_field": "block_gallery", "one_allowed_collections": null, "one_collection": "block_gallery", "one_collection_field": null, "one_deselect_action": "delete", "one_field": "items", "sort_field": "sort"}, "schema": {"table": "block_gallery_items", "column": "block_gallery", "foreign_key_table": "block_gallery", "foreign_key_column": "id", "constraint_name": "block_gallery_items_block_gallery_foreign", "on_update": "NO ACTION", "on_delete": "CASCADE"}}, {"collection": "block_gallery_items", "field": "user_created", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "block_gallery_items", "many_field": "user_created", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_gallery_items", "column": "user_created", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "block_gallery_items_user_created_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_gallery_items", "field": "user_updated", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "block_gallery_items", "many_field": "user_updated", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_gallery_items", "column": "user_updated", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "block_gallery_items_user_updated_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_hero", "field": "image", "related_collection": "directus_files", "meta": {"junction_field": null, "many_collection": "block_hero", "many_field": "image", "one_allowed_collections": null, "one_collection": "directus_files", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_hero", "column": "image", "foreign_key_table": "directus_files", "foreign_key_column": "id", "constraint_name": "block_hero_image_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_hero", "field": "button_group", "related_collection": "block_button_group", "meta": {"junction_field": null, "many_collection": "block_hero", "many_field": "button_group", "one_allowed_collections": null, "one_collection": "block_button_group", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_hero", "column": "button_group", "foreign_key_table": "block_button_group", "foreign_key_column": "id", "constraint_name": "block_hero_button_group_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_hero", "field": "user_created", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "block_hero", "many_field": "user_created", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_hero", "column": "user_created", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "block_hero_user_created_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_hero", "field": "user_updated", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "block_hero", "many_field": "user_updated", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_hero", "column": "user_updated", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "block_hero_user_updated_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_posts", "field": "user_created", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "block_posts", "many_field": "user_created", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_posts", "column": "user_created", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "block_posts_user_created_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_posts", "field": "user_updated", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "block_posts", "many_field": "user_updated", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_posts", "column": "user_updated", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "block_posts_user_updated_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_pricing", "field": "user_created", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "block_pricing", "many_field": "user_created", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_pricing", "column": "user_created", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "block_pricing_user_created_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_pricing", "field": "user_updated", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "block_pricing", "many_field": "user_updated", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_pricing", "column": "user_updated", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "block_pricing_user_updated_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_pricing_cards", "field": "button", "related_collection": "block_button", "meta": {"junction_field": null, "many_collection": "block_pricing_cards", "many_field": "button", "one_allowed_collections": null, "one_collection": "block_button", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_pricing_cards", "column": "button", "foreign_key_table": "block_button", "foreign_key_column": "id", "constraint_name": "block_pricing_cards_button_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_pricing_cards", "field": "pricing", "related_collection": "block_pricing", "meta": {"junction_field": null, "many_collection": "block_pricing_cards", "many_field": "pricing", "one_allowed_collections": null, "one_collection": "block_pricing", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": "pricing_cards", "sort_field": "sort"}, "schema": {"table": "block_pricing_cards", "column": "pricing", "foreign_key_table": "block_pricing", "foreign_key_column": "id", "constraint_name": "block_pricing_cards_pricing_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_pricing_cards", "field": "user_created", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "block_pricing_cards", "many_field": "user_created", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_pricing_cards", "column": "user_created", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "block_pricing_cards_user_created_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_pricing_cards", "field": "user_updated", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "block_pricing_cards", "many_field": "user_updated", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_pricing_cards", "column": "user_updated", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "block_pricing_cards_user_updated_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_richtext", "field": "user_created", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "block_richtext", "many_field": "user_created", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_richtext", "column": "user_created", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "block_richtext_user_created_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "block_richtext", "field": "user_updated", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "block_richtext", "many_field": "user_updated", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "block_richtext", "column": "user_updated", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "block_richtext_user_updated_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "form_fields", "field": "form", "related_collection": "forms", "meta": {"junction_field": null, "many_collection": "form_fields", "many_field": "form", "one_allowed_collections": null, "one_collection": "forms", "one_collection_field": null, "one_deselect_action": "delete", "one_field": "fields", "sort_field": "sort"}, "schema": {"table": "form_fields", "column": "form", "foreign_key_table": "forms", "foreign_key_column": "id", "constraint_name": "form_fields_form_foreign", "on_update": "NO ACTION", "on_delete": "CASCADE"}}, {"collection": "form_fields", "field": "user_created", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "form_fields", "many_field": "user_created", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "form_fields", "column": "user_created", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "form_fields_user_created_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "form_fields", "field": "user_updated", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "form_fields", "many_field": "user_updated", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "form_fields", "column": "user_updated", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "form_fields_user_updated_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "form_submission_values", "field": "form_submission", "related_collection": "form_submissions", "meta": {"junction_field": null, "many_collection": "form_submission_values", "many_field": "form_submission", "one_allowed_collections": null, "one_collection": "form_submissions", "one_collection_field": null, "one_deselect_action": "delete", "one_field": "values", "sort_field": "sort"}, "schema": {"table": "form_submission_values", "column": "form_submission", "foreign_key_table": "form_submissions", "foreign_key_column": "id", "constraint_name": "form_submission_values_form_submission_foreign", "on_update": "NO ACTION", "on_delete": "CASCADE"}}, {"collection": "form_submission_values", "field": "field", "related_collection": "form_fields", "meta": {"junction_field": null, "many_collection": "form_submission_values", "many_field": "field", "one_allowed_collections": null, "one_collection": "form_fields", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "form_submission_values", "column": "field", "foreign_key_table": "form_fields", "foreign_key_column": "id", "constraint_name": "form_submission_values_field_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "form_submission_values", "field": "file", "related_collection": "directus_files", "meta": {"junction_field": null, "many_collection": "form_submission_values", "many_field": "file", "one_allowed_collections": null, "one_collection": "directus_files", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "form_submission_values", "column": "file", "foreign_key_table": "directus_files", "foreign_key_column": "id", "constraint_name": "form_submission_values_file_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "form_submissions", "field": "form", "related_collection": "forms", "meta": {"junction_field": null, "many_collection": "form_submissions", "many_field": "form", "one_allowed_collections": null, "one_collection": "forms", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": "submissions", "sort_field": null}, "schema": {"table": "form_submissions", "column": "form", "foreign_key_table": "forms", "foreign_key_column": "id", "constraint_name": "form_submissions_form_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "forms", "field": "user_created", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "forms", "many_field": "user_created", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "forms", "column": "user_created", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "forms_user_created_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "forms", "field": "user_updated", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "forms", "many_field": "user_updated", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "forms", "column": "user_updated", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "forms_user_updated_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "globals", "field": "favicon", "related_collection": "directus_files", "meta": {"junction_field": null, "many_collection": "globals", "many_field": "favicon", "one_allowed_collections": null, "one_collection": "directus_files", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "globals", "column": "favicon", "foreign_key_table": "directus_files", "foreign_key_column": "id", "constraint_name": "globals_favicon_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "globals", "field": "logo", "related_collection": "directus_files", "meta": {"junction_field": null, "many_collection": "globals", "many_field": "logo", "one_allowed_collections": null, "one_collection": "directus_files", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "globals", "column": "logo", "foreign_key_table": "directus_files", "foreign_key_column": "id", "constraint_name": "globals_logo_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "globals", "field": "logo_dark_mode", "related_collection": "directus_files", "meta": {"junction_field": null, "many_collection": "globals", "many_field": "logo_dark_mode", "one_allowed_collections": null, "one_collection": "directus_files", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "globals", "column": "logo_dark_mode", "foreign_key_table": "directus_files", "foreign_key_column": "id", "constraint_name": "globals_logo_dark_mode_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "globals", "field": "user_created", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "globals", "many_field": "user_created", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "globals", "column": "user_created", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "globals_user_created_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "globals", "field": "user_updated", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "globals", "many_field": "user_updated", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "globals", "column": "user_updated", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "globals_user_updated_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "navigation", "field": "user_created", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "navigation", "many_field": "user_created", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "navigation", "column": "user_created", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "navigation_user_created_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "navigation", "field": "user_updated", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "navigation", "many_field": "user_updated", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "navigation", "column": "user_updated", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "navigation_user_updated_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "navigation_items", "field": "navigation", "related_collection": "navigation", "meta": {"junction_field": null, "many_collection": "navigation_items", "many_field": "navigation", "one_allowed_collections": null, "one_collection": "navigation", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": "items", "sort_field": "sort"}, "schema": {"table": "navigation_items", "column": "navigation", "foreign_key_table": "navigation", "foreign_key_column": "id", "constraint_name": "navigation_items_navigation_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "navigation_items", "field": "page", "related_collection": "pages", "meta": {"junction_field": null, "many_collection": "navigation_items", "many_field": "page", "one_allowed_collections": null, "one_collection": "pages", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "navigation_items", "column": "page", "foreign_key_table": "pages", "foreign_key_column": "id", "constraint_name": "navigation_items_page_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "navigation_items", "field": "parent", "related_collection": "navigation_items", "meta": {"junction_field": null, "many_collection": "navigation_items", "many_field": "parent", "one_allowed_collections": null, "one_collection": "navigation_items", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": "children", "sort_field": "sort"}, "schema": {"table": "navigation_items", "column": "parent", "foreign_key_table": "navigation_items", "foreign_key_column": "id", "constraint_name": "navigation_items_parent_foreign", "on_update": "NO ACTION", "on_delete": "NO ACTION"}}, {"collection": "navigation_items", "field": "post", "related_collection": "posts", "meta": {"junction_field": null, "many_collection": "navigation_items", "many_field": "post", "one_allowed_collections": null, "one_collection": "posts", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "navigation_items", "column": "post", "foreign_key_table": "posts", "foreign_key_column": "id", "constraint_name": "navigation_items_post_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "navigation_items", "field": "user_created", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "navigation_items", "many_field": "user_created", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "navigation_items", "column": "user_created", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "navigation_items_user_created_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "navigation_items", "field": "user_updated", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "navigation_items", "many_field": "user_updated", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "navigation_items", "column": "user_updated", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "navigation_items_user_updated_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "page_blocks", "field": "page", "related_collection": "pages", "meta": {"junction_field": "item", "many_collection": "page_blocks", "many_field": "page", "one_allowed_collections": null, "one_collection": "pages", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": "blocks", "sort_field": "sort"}, "schema": {"table": "page_blocks", "column": "page", "foreign_key_table": "pages", "foreign_key_column": "id", "constraint_name": "page_blocks_pages_id_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "page_blocks", "field": "item", "related_collection": null, "meta": {"junction_field": "page", "many_collection": "page_blocks", "many_field": "item", "one_allowed_collections": ["block_hero", "block_richtext", "block_form", "block_posts", "block_gallery", "block_pricing"], "one_collection": null, "one_collection_field": "collection", "one_deselect_action": "nullify", "one_field": null, "sort_field": null}}, {"collection": "page_blocks", "field": "user_created", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "page_blocks", "many_field": "user_created", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "page_blocks", "column": "user_created", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "page_blocks_user_created_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "page_blocks", "field": "user_updated", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "page_blocks", "many_field": "user_updated", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "page_blocks", "column": "user_updated", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "page_blocks_user_updated_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "pages", "field": "user_created", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "pages", "many_field": "user_created", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "pages", "column": "user_created", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "pages_user_created_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "pages", "field": "user_updated", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "pages", "many_field": "user_updated", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "pages", "column": "user_updated", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "pages_user_updated_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "posts", "field": "image", "related_collection": "directus_files", "meta": {"junction_field": null, "many_collection": "posts", "many_field": "image", "one_allowed_collections": null, "one_collection": "directus_files", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "posts", "column": "image", "foreign_key_table": "directus_files", "foreign_key_column": "id", "constraint_name": "posts_image_foreign", "on_update": "NO ACTION", "on_delete": "NO ACTION"}}, {"collection": "posts", "field": "author", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "posts", "many_field": "author", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": "posts", "sort_field": "sort"}, "schema": {"table": "posts", "column": "author", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "posts_author_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "posts", "field": "user_created", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "posts", "many_field": "user_created", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "posts", "column": "user_created", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "posts_user_created_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "posts", "field": "user_updated", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "posts", "many_field": "user_updated", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "posts", "column": "user_updated", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "posts_user_updated_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "redirects", "field": "user_created", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "redirects", "many_field": "user_created", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "redirects", "column": "user_created", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "redirects_user_created_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "redirects", "field": "user_updated", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "redirects", "many_field": "user_updated", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "redirects", "column": "user_updated", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "redirects_user_updated_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}]}