{"id": 1, "project_name": "CMS", "project_url": null, "project_color": "#6644FF", "project_logo": null, "public_foreground": null, "public_background": null, "public_note": null, "auth_login_attempts": 25, "auth_password_policy": "/^.{8,}$/", "storage_asset_transform": "all", "storage_asset_presets": [{"key": "800w", "fit": "inside", "width": 800, "quality": 90, "withoutEnlargement": true, "format": "webp", "transforms": [], "height": null}, {"key": "1200w", "fit": "inside", "width": 1200, "quality": 90, "withoutEnlargement": true, "format": "webp", "transforms": []}], "custom_css": ".v-divider.inlineTitle.large.add-margin-top {\n    margin-top: 0 !important;\n}\n\n#sidebar .v-divider.inlineTitle {\n    background-color: var(--theme--background) !important;\n    box-shadow: none !important;\n}\n\nbody {\n    font-size: 16px !important;\n}\n\n.drawer-item-content {\n   --theme--form--row-gap: 16px;\n   --theme--form--column-gap: 32px;\n}\n\n#overlay-outlet {\n   --theme--form--row-gap: 16px;\n   --theme--form--column-gap: 32px;\n}\n    ", "storage_default_folder": "ece7bab9-5433-4a63-b9f7-bde8b517d6d9", "basemaps": null, "mapbox_key": null, "module_bar": [{"type": "module", "id": "content", "enabled": true}, {"type": "module", "id": "visual", "enabled": true}, {"type": "module", "id": "users", "enabled": true}, {"type": "module", "id": "files", "enabled": true}, {"type": "module", "id": "insights", "enabled": true}, {"type": "link", "id": "docs", "name": "<PERSON>us <PERSON>s", "url": "https://docs.directus.io", "icon": "help_outline", "enabled": true}, {"type": "module", "id": "settings", "enabled": true, "locked": true}, {"type": "module", "id": "global-search", "enabled": true}], "project_descriptor": null, "default_language": "en-US", "custom_aspect_ratios": null, "public_favicon": null, "default_appearance": "auto", "default_theme_light": "<PERSON><PERSON>", "theme_light_overrides": {"form": {"rowGap": "16px", "field": {"input": {"height": "48px", "background": "var(--theme--background)"}}}, "borderWidth": "1px", "navigation": {"modules": {"background": "color-mix(in srgb, var(--theme--foreground), var(--theme--primary) 80%)", "button": {"foreground": "color-mix(in srgb, var(--theme--background), var(--theme--primary) 20%)", "foregroundActive": "var(--theme--primary)", "backgroundActive": "var(--theme--background)"}}, "background": "var(--theme--background)", "project": {"background": "transparent"}, "borderColor": "color-mix(in srgb, var(--theme--foreground), var(--theme--background) 95%)", "borderWidth": "1px", "list": {"divider": {"borderColor": "var(--theme--border-color-subdued)"}, "backgroundHover": "color-mix(in srgb, var(--theme--background), var(--theme--primary) 10%)", "backgroundActive": "color-mix(in srgb, var(--theme--background), var(--theme--primary) 15%)"}}, "fonts": {"display": {"fontFamily": "\"Space Grotesk\", system-ui", "fontWeight": "600"}, "sans": {"fontFamily": "\"DM Sans\", system-ui"}}, "borderRadius": "6px", "sidebar": {"background": "var(--theme--background)", "borderWidth": "1px", "borderColor": "var(--theme--border-color-subdued)", "section": {"toggle": {"borderColor": "var(--theme--border-color-subdued)", "borderWidth": "1px", "background": "var(--theme--background)", "foreground": "var(--theme--foreground)", "foregroundHover": "var(--theme--foreground)", "icon": {"foreground": "var(--theme--foreground)", "foregroundHover": "var(--theme--foreground)", "foregroundActive": "var(--theme--foreground-accent)"}}}}, "backgroundAccent": "color-mix(in srgb, var(--theme--background), var(--theme--primary) 15%)", "foregroundSubdued": "color-mix(in srgb, var(--theme--background), var(--theme--foreground) 60%)"}, "default_theme_dark": "<PERSON><PERSON>", "theme_dark_overrides": {"form": {"rowGap": "16px", "field": {"input": {"height": "48px", "background": "var(--theme--background)"}}}, "borderWidth": "1px", "navigation": {"modules": {"background": "color-mix(in srgb, var(--theme--background), var(--theme--primary) 80%)", "button": {"foreground": "color-mix(in srgb, var(--theme--foreground), var(--theme--primary) 20%)", "foregroundActive": "#fff", "backgroundActive": "color-mix(in srgb, var(--theme--primary), var(--theme--background) 40%)"}}, "background": "var(--theme--background)", "project": {"background": "transparent"}, "borderColor": "color-mix(in srgb, var(--theme--foreground), var(--theme--background) 95%)", "borderWidth": "1px", "list": {"divider": {"borderColor": "var(--theme--border-color-subdued)"}, "backgroundHover": "color-mix(in srgb, var(--theme--background), var(--theme--primary) 10%)", "backgroundActive": "color-mix(in srgb, var(--theme--background), var(--theme--primary) 15%)"}}, "fonts": {"display": {"fontFamily": "\"Space Grotesk\", system-ui"}, "sans": {"fontFamily": "\"DM Sans\", system-ui"}}, "borderRadius": "6px", "sidebar": {"background": "var(--theme--background)", "borderWidth": "1px", "borderColor": "var(--theme--border-color-subdued)", "section": {"toggle": {"borderColor": "var(--theme--border-color-subdued)", "borderWidth": "1px", "foreground": "var(--theme--foreground)", "foregroundHover": "var(--theme--foreground)", "icon": {"foreground": "var(--theme--foreground)", "foregroundHover": "var(--theme--foreground)", "foregroundActive": "var(--theme--foreground-accent)"}, "background": "var(--theme--background)"}}}, "foregroundSubdued": "color-mix(in srgb, var(--theme--background), var(--theme--foreground) 60%)"}, "report_error_url": null, "report_bug_url": null, "report_feature_url": null, "public_registration": false, "public_registration_verify_email": true, "public_registration_role": null, "public_registration_email_filter": null, "command_palette_settings": {"searchMode": "as_you_type", "collections": [{"collection": "pages", "displayTemplate": "{{title}}", "descriptionField": "permalink", "fields": ["title", "permalink"], "limit": 10, "availableGlobally": false}, {"collection": "posts", "displayTemplate": "{{title}}", "descriptionField": "description", "fields": ["title", "description"], "limit": 10, "availableGlobally": true}, {"collection": "forms", "displayTemplate": "{{title}}", "descriptionField": null, "fields": ["values.field_name", "title"], "limit": 10, "availableGlobally": false}], "triggerRate": 250, "commandPaletteEnabled": true}, "visual_editor_urls": [{"url": "http://localhost:3000?visual-editing=true"}, {"url": "http://localhost:3000/blog?visual-editing=true"}]}