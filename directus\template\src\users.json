[{"id": "88a6e8cf-f0f8-41db-a3a2-8a9741c086cc", "first_name": "Frontend", "last_name": "Bot", "email": null, "password": null, "location": null, "title": "For server-to-server communication", "description": "This user has API only access and is meant for communicating securely with Directus from your frontend.\n\nThis user has elevated permissions over the Public to:\n- Submit Forms \n- Upload Files \n\nFrom a security perspective, you would typically not want to allow public access to upload files or submit forms.\n\n- Generate a static token access below and include it in API calls to Directus when submitting forms or uploading files from a form.\n- Be careful to only use the token server side to prevent the static access token from being exposed to the client side.", "tags": ["API"], "avatar": "14594872-a805-4251-8dfd-b93bb2effbc0", "language": null, "tfa_secret": null, "status": "active", "role": null, "token": "**********", "last_access": null, "last_page": null, "provider": "default", "external_identifier": null, "auth_data": null, "email_notifications": true, "appearance": null, "theme_dark": null, "theme_light": null, "theme_light_overrides": null, "theme_dark_overrides": null, "posts": null, "policies": null}, {"id": "9a105323-5eec-48d4-8a79-4681fdc94276", "first_name": "Content", "last_name": "Writer", "email": "<EMAIL>", "password": "**********", "location": null, "title": null, "description": null, "tags": null, "avatar": "5f35b7e3-0357-47c3-807f-f132cca95e3f", "language": null, "tfa_secret": null, "status": "active", "role": "3a4464fb-2189-4710-a164-2503eed88ae7", "token": null, "last_access": "2025-04-02T19:38:38.817Z", "last_page": "/content/posts/ba559a1e-5b0c-4932-ab97-b1ab4ca69955", "provider": "default", "external_identifier": null, "auth_data": null, "email_notifications": true, "appearance": null, "theme_dark": null, "theme_light": null, "theme_light_overrides": null, "theme_dark_overrides": null, "posts": null, "policies": null}, {"id": "d56956bf-6ed0-465e-bb4a-ec9bde65c5f0", "first_name": "Webmaster", "last_name": null, "email": "<EMAIL>", "password": "**********", "location": null, "title": null, "description": null, "tags": null, "avatar": "d627d585-2c14-4bbf-89ca-34581083cc1d", "language": null, "tfa_secret": null, "status": "active", "role": "ef049c8b-546b-4bbc-9cd7-b05d77e58b66", "token": "**********", "last_access": "2025-05-07T19:37:40.249Z", "last_page": "/settings/extensions", "provider": "default", "external_identifier": null, "auth_data": null, "email_notifications": true, "appearance": null, "theme_dark": null, "theme_light": null, "theme_light_overrides": null, "theme_dark_overrides": null, "posts": null, "policies": null}]