"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-sidecar";
exports.ids = ["vendor-chunks/use-sidecar"];
exports.modules = {

/***/ "(ssr)/./node_modules/use-sidecar/dist/es5/config.js":
/*!*****************************************************!*\
  !*** ./node_modules/use-sidecar/dist/es5/config.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.setConfig = exports.config = void 0;\nexports.config = {\n    onError: function (e) { return console.error(e); },\n};\nvar setConfig = function (conf) {\n    Object.assign(exports.config, conf);\n};\nexports.setConfig = setConfig;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLXNpZGVjYXIvZGlzdC9lczUvY29uZmlnLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGlCQUFpQixHQUFHLGNBQWM7QUFDbEMsY0FBYztBQUNkLDRCQUE0QiwwQkFBMEI7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21tdW5pdHktZm9ydW0tcHdhLy4vbm9kZV9tb2R1bGVzL3VzZS1zaWRlY2FyL2Rpc3QvZXM1L2NvbmZpZy5qcz85NjMxIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5zZXRDb25maWcgPSBleHBvcnRzLmNvbmZpZyA9IHZvaWQgMDtcbmV4cG9ydHMuY29uZmlnID0ge1xuICAgIG9uRXJyb3I6IGZ1bmN0aW9uIChlKSB7IHJldHVybiBjb25zb2xlLmVycm9yKGUpOyB9LFxufTtcbnZhciBzZXRDb25maWcgPSBmdW5jdGlvbiAoY29uZikge1xuICAgIE9iamVjdC5hc3NpZ24oZXhwb3J0cy5jb25maWcsIGNvbmYpO1xufTtcbmV4cG9ydHMuc2V0Q29uZmlnID0gc2V0Q29uZmlnO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-sidecar/dist/es5/config.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-sidecar/dist/es5/env.js":
/*!**************************************************!*\
  !*** ./node_modules/use-sidecar/dist/es5/env.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.env = void 0;\nvar detect_node_es_1 = __webpack_require__(/*! detect-node-es */ \"(ssr)/./node_modules/detect-node-es/es5/node.js\");\nexports.env = {\n    isNode: detect_node_es_1.isNode,\n    forceCache: false,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLXNpZGVjYXIvZGlzdC9lczUvZW52LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELFdBQVc7QUFDWCx1QkFBdUIsbUJBQU8sQ0FBQyx1RUFBZ0I7QUFDL0MsV0FBVztBQUNYO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbW11bml0eS1mb3J1bS1wd2EvLi9ub2RlX21vZHVsZXMvdXNlLXNpZGVjYXIvZGlzdC9lczUvZW52LmpzPzEzYTAiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmVudiA9IHZvaWQgMDtcbnZhciBkZXRlY3Rfbm9kZV9lc18xID0gcmVxdWlyZShcImRldGVjdC1ub2RlLWVzXCIpO1xuZXhwb3J0cy5lbnYgPSB7XG4gICAgaXNOb2RlOiBkZXRlY3Rfbm9kZV9lc18xLmlzTm9kZSxcbiAgICBmb3JjZUNhY2hlOiBmYWxzZSxcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-sidecar/dist/es5/env.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-sidecar/dist/es5/exports.js":
/*!******************************************************!*\
  !*** ./node_modules/use-sidecar/dist/es5/exports.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.exportSidecar = void 0;\nvar tslib_1 = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\nvar React = tslib_1.__importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar SideCar = function (_a) {\n    var sideCar = _a.sideCar, rest = tslib_1.__rest(_a, [\"sideCar\"]);\n    if (!sideCar) {\n        throw new Error('Sidecar: please provide `sideCar` property to import the right car');\n    }\n    var Target = sideCar.read();\n    if (!Target) {\n        throw new Error('Sidecar medium not found');\n    }\n    return React.createElement(Target, tslib_1.__assign({}, rest));\n};\nSideCar.isSideCarExport = true;\nfunction exportSidecar(medium, exported) {\n    medium.useMedium(exported);\n    return SideCar;\n}\nexports.exportSidecar = exportSidecar;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLXNpZGVjYXIvZGlzdC9lczUvZXhwb3J0cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxxQkFBcUI7QUFDckIsY0FBYyxtQkFBTyxDQUFDLHVEQUFPO0FBQzdCLGlDQUFpQyxtQkFBTyxDQUFDLHdHQUFPO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBEQUEwRDtBQUMxRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21tdW5pdHktZm9ydW0tcHdhLy4vbm9kZV9tb2R1bGVzL3VzZS1zaWRlY2FyL2Rpc3QvZXM1L2V4cG9ydHMuanM/MjFiYyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuZXhwb3J0U2lkZWNhciA9IHZvaWQgMDtcbnZhciB0c2xpYl8xID0gcmVxdWlyZShcInRzbGliXCIpO1xudmFyIFJlYWN0ID0gdHNsaWJfMS5fX2ltcG9ydFN0YXIocmVxdWlyZShcInJlYWN0XCIpKTtcbnZhciBTaWRlQ2FyID0gZnVuY3Rpb24gKF9hKSB7XG4gICAgdmFyIHNpZGVDYXIgPSBfYS5zaWRlQ2FyLCByZXN0ID0gdHNsaWJfMS5fX3Jlc3QoX2EsIFtcInNpZGVDYXJcIl0pO1xuICAgIGlmICghc2lkZUNhcikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1NpZGVjYXI6IHBsZWFzZSBwcm92aWRlIGBzaWRlQ2FyYCBwcm9wZXJ0eSB0byBpbXBvcnQgdGhlIHJpZ2h0IGNhcicpO1xuICAgIH1cbiAgICB2YXIgVGFyZ2V0ID0gc2lkZUNhci5yZWFkKCk7XG4gICAgaWYgKCFUYXJnZXQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdTaWRlY2FyIG1lZGl1bSBub3QgZm91bmQnKTtcbiAgICB9XG4gICAgcmV0dXJuIFJlYWN0LmNyZWF0ZUVsZW1lbnQoVGFyZ2V0LCB0c2xpYl8xLl9fYXNzaWduKHt9LCByZXN0KSk7XG59O1xuU2lkZUNhci5pc1NpZGVDYXJFeHBvcnQgPSB0cnVlO1xuZnVuY3Rpb24gZXhwb3J0U2lkZWNhcihtZWRpdW0sIGV4cG9ydGVkKSB7XG4gICAgbWVkaXVtLnVzZU1lZGl1bShleHBvcnRlZCk7XG4gICAgcmV0dXJuIFNpZGVDYXI7XG59XG5leHBvcnRzLmV4cG9ydFNpZGVjYXIgPSBleHBvcnRTaWRlY2FyO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-sidecar/dist/es5/exports.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-sidecar/dist/es5/hoc.js":
/*!**************************************************!*\
  !*** ./node_modules/use-sidecar/dist/es5/hoc.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.sidecar = void 0;\nvar tslib_1 = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\nvar React = tslib_1.__importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar hook_1 = __webpack_require__(/*! ./hook */ \"(ssr)/./node_modules/use-sidecar/dist/es5/hook.js\");\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction sidecar(importer, errorComponent) {\n    var ErrorCase = function () { return errorComponent; };\n    return function Sidecar(props) {\n        var _a = (0, hook_1.useSidecar)(importer, props.sideCar), Car = _a[0], error = _a[1];\n        if (error && errorComponent) {\n            return ErrorCase;\n        }\n        // @ts-expect-error type shenanigans\n        return Car ? React.createElement(Car, tslib_1.__assign({}, props)) : null;\n    };\n}\nexports.sidecar = sidecar;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLXNpZGVjYXIvZGlzdC9lczUvaG9jLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGVBQWU7QUFDZixjQUFjLG1CQUFPLENBQUMsdURBQU87QUFDN0IsaUNBQWlDLG1CQUFPLENBQUMsd0dBQU87QUFDaEQsYUFBYSxtQkFBTyxDQUFDLGlFQUFRO0FBQzdCO0FBQ0E7QUFDQSxrQ0FBa0M7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWlFO0FBQ2pFO0FBQ0E7QUFDQSxlQUFlIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29tbXVuaXR5LWZvcnVtLXB3YS8uL25vZGVfbW9kdWxlcy91c2Utc2lkZWNhci9kaXN0L2VzNS9ob2MuanM/OWNlOSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuc2lkZWNhciA9IHZvaWQgMDtcbnZhciB0c2xpYl8xID0gcmVxdWlyZShcInRzbGliXCIpO1xudmFyIFJlYWN0ID0gdHNsaWJfMS5fX2ltcG9ydFN0YXIocmVxdWlyZShcInJlYWN0XCIpKTtcbnZhciBob29rXzEgPSByZXF1aXJlKFwiLi9ob29rXCIpO1xuLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9iYW4tdHlwZXNcbmZ1bmN0aW9uIHNpZGVjYXIoaW1wb3J0ZXIsIGVycm9yQ29tcG9uZW50KSB7XG4gICAgdmFyIEVycm9yQ2FzZSA9IGZ1bmN0aW9uICgpIHsgcmV0dXJuIGVycm9yQ29tcG9uZW50OyB9O1xuICAgIHJldHVybiBmdW5jdGlvbiBTaWRlY2FyKHByb3BzKSB7XG4gICAgICAgIHZhciBfYSA9ICgwLCBob29rXzEudXNlU2lkZWNhcikoaW1wb3J0ZXIsIHByb3BzLnNpZGVDYXIpLCBDYXIgPSBfYVswXSwgZXJyb3IgPSBfYVsxXTtcbiAgICAgICAgaWYgKGVycm9yICYmIGVycm9yQ29tcG9uZW50KSB7XG4gICAgICAgICAgICByZXR1cm4gRXJyb3JDYXNlO1xuICAgICAgICB9XG4gICAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgdHlwZSBzaGVuYW5pZ2Fuc1xuICAgICAgICByZXR1cm4gQ2FyID8gUmVhY3QuY3JlYXRlRWxlbWVudChDYXIsIHRzbGliXzEuX19hc3NpZ24oe30sIHByb3BzKSkgOiBudWxsO1xuICAgIH07XG59XG5leHBvcnRzLnNpZGVjYXIgPSBzaWRlY2FyO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-sidecar/dist/es5/hoc.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-sidecar/dist/es5/hook.js":
/*!***************************************************!*\
  !*** ./node_modules/use-sidecar/dist/es5/hook.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useSidecar = void 0;\nvar react_1 = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar env_1 = __webpack_require__(/*! ./env */ \"(ssr)/./node_modules/use-sidecar/dist/es5/env.js\");\nvar cache = new WeakMap();\nvar NO_OPTIONS = {};\nfunction useSidecar(importer, effect) {\n    var options = (effect && effect.options) || NO_OPTIONS;\n    if (env_1.env.isNode && !options.ssr) {\n        return [null, null];\n    }\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    return useRealSidecar(importer, effect);\n}\nexports.useSidecar = useSidecar;\nfunction useRealSidecar(importer, effect) {\n    var options = (effect && effect.options) || NO_OPTIONS;\n    var couldUseCache = env_1.env.forceCache || (env_1.env.isNode && !!options.ssr) || !options.async;\n    var _a = (0, react_1.useState)(couldUseCache ? function () { return cache.get(importer); } : undefined), Car = _a[0], setCar = _a[1];\n    var _b = (0, react_1.useState)(null), error = _b[0], setError = _b[1];\n    (0, react_1.useEffect)(function () {\n        if (!Car) {\n            importer().then(function (car) {\n                var resolved = effect ? effect.read() : car.default || car;\n                if (!resolved) {\n                    console.error('Sidecar error: with importer', importer);\n                    var error_1;\n                    if (effect) {\n                        console.error('Sidecar error: with medium', effect);\n                        error_1 = new Error('Sidecar medium was not found');\n                    }\n                    else {\n                        error_1 = new Error('Sidecar was not found in exports');\n                    }\n                    setError(function () { return error_1; });\n                    throw error_1;\n                }\n                cache.set(importer, resolved);\n                setCar(function () { return resolved; });\n            }, function (e) { return setError(function () { return e; }); });\n        }\n    }, []);\n    return [Car, error];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-sidecar/dist/es5/hook.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-sidecar/dist/es5/index.js":
/*!****************************************************!*\
  !*** ./node_modules/use-sidecar/dist/es5/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.exportSidecar = exports.renderCar = exports.createSidecarMedium = exports.createMedium = exports.setConfig = exports.useSidecar = exports.sidecar = void 0;\nvar hoc_1 = __webpack_require__(/*! ./hoc */ \"(ssr)/./node_modules/use-sidecar/dist/es5/hoc.js\");\nObject.defineProperty(exports, \"sidecar\", ({ enumerable: true, get: function () { return hoc_1.sidecar; } }));\nvar hook_1 = __webpack_require__(/*! ./hook */ \"(ssr)/./node_modules/use-sidecar/dist/es5/hook.js\");\nObject.defineProperty(exports, \"useSidecar\", ({ enumerable: true, get: function () { return hook_1.useSidecar; } }));\nvar config_1 = __webpack_require__(/*! ./config */ \"(ssr)/./node_modules/use-sidecar/dist/es5/config.js\");\nObject.defineProperty(exports, \"setConfig\", ({ enumerable: true, get: function () { return config_1.setConfig; } }));\nvar medium_1 = __webpack_require__(/*! ./medium */ \"(ssr)/./node_modules/use-sidecar/dist/es5/medium.js\");\nObject.defineProperty(exports, \"createMedium\", ({ enumerable: true, get: function () { return medium_1.createMedium; } }));\nObject.defineProperty(exports, \"createSidecarMedium\", ({ enumerable: true, get: function () { return medium_1.createSidecarMedium; } }));\nvar renderProp_1 = __webpack_require__(/*! ./renderProp */ \"(ssr)/./node_modules/use-sidecar/dist/es5/renderProp.js\");\nObject.defineProperty(exports, \"renderCar\", ({ enumerable: true, get: function () { return renderProp_1.renderCar; } }));\nvar exports_1 = __webpack_require__(/*! ./exports */ \"(ssr)/./node_modules/use-sidecar/dist/es5/exports.js\");\nObject.defineProperty(exports, \"exportSidecar\", ({ enumerable: true, get: function () { return exports_1.exportSidecar; } }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-sidecar/dist/es5/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-sidecar/dist/es5/medium.js":
/*!*****************************************************!*\
  !*** ./node_modules/use-sidecar/dist/es5/medium.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createSidecarMedium = exports.createMedium = void 0;\nvar tslib_1 = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\nfunction ItoI(a) {\n    return a;\n}\nfunction innerCreateMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    var buffer = [];\n    var assigned = false;\n    var medium = {\n        read: function () {\n            if (assigned) {\n                throw new Error('Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.');\n            }\n            if (buffer.length) {\n                return buffer[buffer.length - 1];\n            }\n            return defaults;\n        },\n        useMedium: function (data) {\n            var item = middleware(data, assigned);\n            buffer.push(item);\n            return function () {\n                buffer = buffer.filter(function (x) { return x !== item; });\n            };\n        },\n        assignSyncMedium: function (cb) {\n            assigned = true;\n            while (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n            }\n            buffer = {\n                push: function (x) { return cb(x); },\n                filter: function () { return buffer; },\n            };\n        },\n        assignMedium: function (cb) {\n            assigned = true;\n            var pendingQueue = [];\n            if (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n                pendingQueue = buffer;\n            }\n            var executeQueue = function () {\n                var cbs = pendingQueue;\n                pendingQueue = [];\n                cbs.forEach(cb);\n            };\n            var cycle = function () { return Promise.resolve().then(executeQueue); };\n            cycle();\n            buffer = {\n                push: function (x) {\n                    pendingQueue.push(x);\n                    cycle();\n                },\n                filter: function (filter) {\n                    pendingQueue = pendingQueue.filter(filter);\n                    return buffer;\n                },\n            };\n        },\n    };\n    return medium;\n}\nfunction createMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    return innerCreateMedium(defaults, middleware);\n}\nexports.createMedium = createMedium;\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction createSidecarMedium(options) {\n    if (options === void 0) { options = {}; }\n    var medium = innerCreateMedium(null);\n    medium.options = tslib_1.__assign({ async: true, ssr: false }, options);\n    return medium;\n}\nexports.createSidecarMedium = createSidecarMedium;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLXNpZGVjYXIvZGlzdC9lczUvbWVkaXVtLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELDJCQUEyQixHQUFHLG9CQUFvQjtBQUNsRCxjQUFjLG1CQUFPLENBQUMsdURBQU87QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNEQUFzRCxvQkFBb0I7QUFDMUU7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxlQUFlO0FBQ3BELHNDQUFzQyxnQkFBZ0I7QUFDdEQ7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQztBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0Esb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQSw4QkFBOEI7QUFDOUI7QUFDQSx3Q0FBd0MseUJBQXlCO0FBQ2pFO0FBQ0E7QUFDQSwyQkFBMkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21tdW5pdHktZm9ydW0tcHdhLy4vbm9kZV9tb2R1bGVzL3VzZS1zaWRlY2FyL2Rpc3QvZXM1L21lZGl1bS5qcz8wMTA2Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5jcmVhdGVTaWRlY2FyTWVkaXVtID0gZXhwb3J0cy5jcmVhdGVNZWRpdW0gPSB2b2lkIDA7XG52YXIgdHNsaWJfMSA9IHJlcXVpcmUoXCJ0c2xpYlwiKTtcbmZ1bmN0aW9uIEl0b0koYSkge1xuICAgIHJldHVybiBhO1xufVxuZnVuY3Rpb24gaW5uZXJDcmVhdGVNZWRpdW0oZGVmYXVsdHMsIG1pZGRsZXdhcmUpIHtcbiAgICBpZiAobWlkZGxld2FyZSA9PT0gdm9pZCAwKSB7IG1pZGRsZXdhcmUgPSBJdG9JOyB9XG4gICAgdmFyIGJ1ZmZlciA9IFtdO1xuICAgIHZhciBhc3NpZ25lZCA9IGZhbHNlO1xuICAgIHZhciBtZWRpdW0gPSB7XG4gICAgICAgIHJlYWQ6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgIGlmIChhc3NpZ25lZCkge1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignU2lkZWNhcjogY291bGQgbm90IGByZWFkYCBmcm9tIGFuIGBhc3NpZ25lZGAgbWVkaXVtLiBgcmVhZGAgY291bGQgYmUgdXNlZCBvbmx5IHdpdGggYHVzZU1lZGl1bWAuJyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoYnVmZmVyLmxlbmd0aCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBidWZmZXJbYnVmZmVyLmxlbmd0aCAtIDFdO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIGRlZmF1bHRzO1xuICAgICAgICB9LFxuICAgICAgICB1c2VNZWRpdW06IGZ1bmN0aW9uIChkYXRhKSB7XG4gICAgICAgICAgICB2YXIgaXRlbSA9IG1pZGRsZXdhcmUoZGF0YSwgYXNzaWduZWQpO1xuICAgICAgICAgICAgYnVmZmVyLnB1c2goaXRlbSk7XG4gICAgICAgICAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgICAgIGJ1ZmZlciA9IGJ1ZmZlci5maWx0ZXIoZnVuY3Rpb24gKHgpIHsgcmV0dXJuIHggIT09IGl0ZW07IH0pO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfSxcbiAgICAgICAgYXNzaWduU3luY01lZGl1bTogZnVuY3Rpb24gKGNiKSB7XG4gICAgICAgICAgICBhc3NpZ25lZCA9IHRydWU7XG4gICAgICAgICAgICB3aGlsZSAoYnVmZmVyLmxlbmd0aCkge1xuICAgICAgICAgICAgICAgIHZhciBjYnMgPSBidWZmZXI7XG4gICAgICAgICAgICAgICAgYnVmZmVyID0gW107XG4gICAgICAgICAgICAgICAgY2JzLmZvckVhY2goY2IpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYnVmZmVyID0ge1xuICAgICAgICAgICAgICAgIHB1c2g6IGZ1bmN0aW9uICh4KSB7IHJldHVybiBjYih4KTsgfSxcbiAgICAgICAgICAgICAgICBmaWx0ZXI6IGZ1bmN0aW9uICgpIHsgcmV0dXJuIGJ1ZmZlcjsgfSxcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0sXG4gICAgICAgIGFzc2lnbk1lZGl1bTogZnVuY3Rpb24gKGNiKSB7XG4gICAgICAgICAgICBhc3NpZ25lZCA9IHRydWU7XG4gICAgICAgICAgICB2YXIgcGVuZGluZ1F1ZXVlID0gW107XG4gICAgICAgICAgICBpZiAoYnVmZmVyLmxlbmd0aCkge1xuICAgICAgICAgICAgICAgIHZhciBjYnMgPSBidWZmZXI7XG4gICAgICAgICAgICAgICAgYnVmZmVyID0gW107XG4gICAgICAgICAgICAgICAgY2JzLmZvckVhY2goY2IpO1xuICAgICAgICAgICAgICAgIHBlbmRpbmdRdWV1ZSA9IGJ1ZmZlcjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHZhciBleGVjdXRlUXVldWUgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICAgICAgdmFyIGNicyA9IHBlbmRpbmdRdWV1ZTtcbiAgICAgICAgICAgICAgICBwZW5kaW5nUXVldWUgPSBbXTtcbiAgICAgICAgICAgICAgICBjYnMuZm9yRWFjaChjYik7XG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgdmFyIGN5Y2xlID0gZnVuY3Rpb24gKCkgeyByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKCkudGhlbihleGVjdXRlUXVldWUpOyB9O1xuICAgICAgICAgICAgY3ljbGUoKTtcbiAgICAgICAgICAgIGJ1ZmZlciA9IHtcbiAgICAgICAgICAgICAgICBwdXNoOiBmdW5jdGlvbiAoeCkge1xuICAgICAgICAgICAgICAgICAgICBwZW5kaW5nUXVldWUucHVzaCh4KTtcbiAgICAgICAgICAgICAgICAgICAgY3ljbGUoKTtcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIGZpbHRlcjogZnVuY3Rpb24gKGZpbHRlcikge1xuICAgICAgICAgICAgICAgICAgICBwZW5kaW5nUXVldWUgPSBwZW5kaW5nUXVldWUuZmlsdGVyKGZpbHRlcik7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBidWZmZXI7XG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0sXG4gICAgfTtcbiAgICByZXR1cm4gbWVkaXVtO1xufVxuZnVuY3Rpb24gY3JlYXRlTWVkaXVtKGRlZmF1bHRzLCBtaWRkbGV3YXJlKSB7XG4gICAgaWYgKG1pZGRsZXdhcmUgPT09IHZvaWQgMCkgeyBtaWRkbGV3YXJlID0gSXRvSTsgfVxuICAgIHJldHVybiBpbm5lckNyZWF0ZU1lZGl1bShkZWZhdWx0cywgbWlkZGxld2FyZSk7XG59XG5leHBvcnRzLmNyZWF0ZU1lZGl1bSA9IGNyZWF0ZU1lZGl1bTtcbi8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvYmFuLXR5cGVzXG5mdW5jdGlvbiBjcmVhdGVTaWRlY2FyTWVkaXVtKG9wdGlvbnMpIHtcbiAgICBpZiAob3B0aW9ucyA9PT0gdm9pZCAwKSB7IG9wdGlvbnMgPSB7fTsgfVxuICAgIHZhciBtZWRpdW0gPSBpbm5lckNyZWF0ZU1lZGl1bShudWxsKTtcbiAgICBtZWRpdW0ub3B0aW9ucyA9IHRzbGliXzEuX19hc3NpZ24oeyBhc3luYzogdHJ1ZSwgc3NyOiBmYWxzZSB9LCBvcHRpb25zKTtcbiAgICByZXR1cm4gbWVkaXVtO1xufVxuZXhwb3J0cy5jcmVhdGVTaWRlY2FyTWVkaXVtID0gY3JlYXRlU2lkZWNhck1lZGl1bTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-sidecar/dist/es5/medium.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-sidecar/dist/es5/renderProp.js":
/*!*********************************************************!*\
  !*** ./node_modules/use-sidecar/dist/es5/renderProp.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.renderCar = void 0;\nvar tslib_1 = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\nvar React = tslib_1.__importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar react_1 = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nfunction renderCar(WrappedComponent, defaults) {\n    function State(_a) {\n        var stateRef = _a.stateRef, props = _a.props;\n        var renderTarget = (0, react_1.useCallback)(function SideTarget() {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            (0, react_1.useLayoutEffect)(function () {\n                stateRef.current(args);\n            });\n            return null;\n        }, []);\n        // @ts-ignore\n        return React.createElement(WrappedComponent, tslib_1.__assign({}, props, { children: renderTarget }));\n    }\n    var Children = React.memo(function (_a) {\n        var stateRef = _a.stateRef, defaultState = _a.defaultState, children = _a.children;\n        var _b = (0, react_1.useState)(defaultState.current), state = _b[0], setState = _b[1];\n        (0, react_1.useEffect)(function () {\n            stateRef.current = setState;\n        }, []);\n        return children.apply(void 0, state);\n    }, function () { return true; });\n    return function Combiner(props) {\n        var defaultState = React.useRef(defaults(props));\n        var ref = React.useRef(function (state) { return (defaultState.current = state); });\n        return (React.createElement(React.Fragment, null,\n            React.createElement(State, { stateRef: ref, props: props }),\n            React.createElement(Children, { stateRef: ref, defaultState: defaultState, children: props.children })));\n    };\n}\nexports.renderCar = renderCar;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-sidecar/dist/es5/renderProp.js\n");

/***/ })

};
;