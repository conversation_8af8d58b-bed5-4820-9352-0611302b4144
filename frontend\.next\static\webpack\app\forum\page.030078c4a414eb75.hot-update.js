"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forum/page",{

/***/ "(app-pages-browser)/./lib/directus.ts":
/*!*************************!*\
  !*** ./lib/directus.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: function() { return /* binding */ api; },\n/* harmony export */   auth: function() { return /* binding */ auth; },\n/* harmony export */   authAPI: function() { return /* binding */ authAPI; },\n/* harmony export */   authenticatedAPI: function() { return /* binding */ authenticatedAPI; },\n/* harmony export */   createAuthenticatedDirectus: function() { return /* binding */ createAuthenticatedDirectus; },\n/* harmony export */   directus: function() { return /* binding */ directus; },\n/* harmony export */   getAuthenticatedDirectus: function() { return /* binding */ getAuthenticatedDirectus; },\n/* harmony export */   testConnection: function() { return /* binding */ testConnection; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\n// Directus configuration\nconst DIRECTUS_URL = \"http://localhost:8055\" || 0;\nconst DIRECTUS_TOKEN = \"wHf_Vc3dv3UslEFsPhjflRI__tU_Xkx0\";\n// Helper function for Directus API calls with proper URL encoding\nconst directusFetch = async function(collection) {\n    let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const baseUrl = \"\".concat(DIRECTUS_URL, \"/items/\").concat(collection);\n    const queryParams = Object.entries(params).map((param)=>{\n        let [key, value] = param;\n        if (key.includes(\"[\") && key.includes(\"]\")) {\n            // Handle filter parameters with special encoding\n            const encodedKey = key.replace(/\\[/g, \"%5B\").replace(/\\]/g, \"%5D\");\n            return \"\".concat(encodedKey, \"=\").concat(encodeURIComponent(value));\n        }\n        return \"\".concat(key, \"=\").concat(encodeURIComponent(value));\n    }).join(\"&\");\n    const url = queryParams ? \"\".concat(baseUrl, \"?\").concat(queryParams) : baseUrl;\n    const response = await fetch(url, {\n        headers: {\n            \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n            \"Content-Type\": \"application/json\"\n        }\n    });\n    if (!response.ok) {\n        throw new Error(\"HTTP error! status: \".concat(response.status));\n    }\n    return response.json();\n};\n// Create axios instance for Directus API (public token)\nconst directus = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: \"\".concat(DIRECTUS_URL, \"/items\"),\n    headers: {\n        \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Create authenticated axios instance (user token)\nconst createAuthenticatedDirectus = (userToken)=>{\n    return axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n        baseURL: \"\".concat(DIRECTUS_URL, \"/items\"),\n        headers: {\n            \"Authorization\": \"Bearer \".concat(userToken),\n            \"Content-Type\": \"application/json\"\n        }\n    });\n};\n// Get authenticated instance from localStorage\nconst getAuthenticatedDirectus = ()=>{\n    const token = localStorage.getItem(\"directus_access_token\");\n    if (!token) {\n        throw new Error(\"No authentication token found\");\n    }\n    return createAuthenticatedDirectus(token);\n};\n// Authentication endpoints\nconst auth = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: \"\".concat(DIRECTUS_URL, \"/auth\"),\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Test connection function\nconst testConnection = async ()=>{\n    try {\n        // Test basic connection by fetching server info\n        const serverResponse = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(DIRECTUS_URL, \"/server/info\"));\n        console.log(\"Server info:\", serverResponse.data);\n        // Test actual collections with public token\n        const testResults = {\n            server: serverResponse.data,\n            collections: {}\n        };\n        // Test each collection\n        const collectionsToTest = [\n            \"Posts\",\n            \"Categories\",\n            \"Events\",\n            \"Event_Categories\",\n            \"Banner_Slider\",\n            \"Features\",\n            \"Testimonials\",\n            \"Social_media\"\n        ];\n        for (const collection of collectionsToTest){\n            try {\n                var _response_data_data;\n                const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(DIRECTUS_URL, \"/items/\").concat(collection), {\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n                        \"Content-Type\": \"application/json\"\n                    },\n                    params: {\n                        limit: 5,\n                        fields: \"id,status,Title,Category\"\n                    }\n                });\n                testResults.collections[collection] = {\n                    success: true,\n                    count: ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.length) || 0,\n                    data: response.data.data || []\n                };\n            } catch (collectionError) {\n                testResults.collections[collection] = {\n                    success: false,\n                    error: collectionError instanceof Error ? collectionError.message : \"Unknown error\"\n                };\n            }\n        }\n        return {\n            success: true,\n            ...testResults\n        };\n    } catch (error) {\n        console.error(\"Connection test failed:\", error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        };\n    }\n};\n// Authentication API functions\nconst authAPI = {\n    // Login with Directus\n    login: async (email, password)=>{\n        try {\n            const response = await fetch(\"\".concat(DIRECTUS_URL, \"/auth/login\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            if (!response.ok) {\n                var _error_errors_, _error_errors;\n                const error = await response.json();\n                throw new Error(((_error_errors = error.errors) === null || _error_errors === void 0 ? void 0 : (_error_errors_ = _error_errors[0]) === null || _error_errors_ === void 0 ? void 0 : _error_errors_.message) || \"Login failed\");\n            }\n            const data = await response.json();\n            return data.data;\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            throw error;\n        }\n    },\n    // Register new user with Writer role\n    register: async (userData)=>{\n        try {\n            // First, get the Writer role ID\n            const rolesResponse = await fetch(\"\".concat(DIRECTUS_URL, \"/roles?filter[name][_eq]=Writer\"), {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            let writerRoleId = null;\n            if (rolesResponse.ok) {\n                var _rolesData_data_, _rolesData_data;\n                const rolesData = await rolesResponse.json();\n                writerRoleId = (_rolesData_data = rolesData.data) === null || _rolesData_data === void 0 ? void 0 : (_rolesData_data_ = _rolesData_data[0]) === null || _rolesData_data_ === void 0 ? void 0 : _rolesData_data_.id;\n            }\n            // If Writer role not found, we'll let Directus handle the default role\n            const userPayload = {\n                ...userData,\n                status: \"active\",\n                ...writerRoleId && {\n                    role: writerRoleId\n                }\n            };\n            const response = await fetch(\"\".concat(DIRECTUS_URL, \"/users\"), {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(userPayload)\n            });\n            if (!response.ok) {\n                var _error_errors_, _error_errors;\n                const error = await response.json();\n                throw new Error(((_error_errors = error.errors) === null || _error_errors === void 0 ? void 0 : (_error_errors_ = _error_errors[0]) === null || _error_errors_ === void 0 ? void 0 : _error_errors_.message) || \"Registration failed\");\n            }\n            const data = await response.json();\n            return data.data;\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            throw error;\n        }\n    },\n    // Get current user info\n    getCurrentUser: async (token)=>{\n        try {\n            const response = await fetch(\"\".concat(DIRECTUS_URL, \"/users/me\"), {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to get user info\");\n            }\n            const data = await response.json();\n            return data.data;\n        } catch (error) {\n            console.error(\"Get user error:\", error);\n            throw error;\n        }\n    },\n    // Refresh token\n    refresh: async (refreshToken)=>{\n        try {\n            const response = await fetch(\"\".concat(DIRECTUS_URL, \"/auth/refresh\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refresh_token: refreshToken\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to refresh token\");\n            }\n            const data = await response.json();\n            return data.data;\n        } catch (error) {\n            console.error(\"Refresh token error:\", error);\n            throw error;\n        }\n    },\n    // Logout\n    logout: async (refreshToken)=>{\n        try {\n            await fetch(\"\".concat(DIRECTUS_URL, \"/auth/logout\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refresh_token: refreshToken\n                })\n            });\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        // Don't throw error for logout, just log it\n        }\n    }\n};\n// API functions\nconst api = {\n    // Posts\n    getPosts: async function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, status = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"published\", categoryId = arguments.length > 3 ? arguments[3] : void 0, isAuthenticated = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : false;\n        try {\n            // Start with basic fields that should have public access\n            const params = {\n                limit,\n                offset,\n                sort: \"-date_created\",\n                \"filter[status][_eq]\": status,\n                fields: \"id,Title,Description,date_created\"\n            };\n            // Add category filter if specified (filter by category ID through M2M relationship)\n            if (categoryId) {\n                params[\"filter[Categories][id][_eq]\"] = categoryId;\n            }\n            // Filter based on authentication status and Is_Public field\n            if (!isAuthenticated) {\n                // For unauthenticated users, only show public posts (Is_Public = true)\n                params[\"filter[Is_Public][_eq]\"] = true;\n            }\n            // For authenticated users, show all posts regardless of Is_Public value\n            console.log(\"Fetching posts with params:\", params);\n            const response = await directus.get(\"/Posts\", {\n                params\n            });\n            console.log(\"Posts response:\", response);\n            return response.data;\n        } catch (error) {\n            console.error(\"Error fetching posts:\", error);\n            // Fallback to even more minimal fields\n            try {\n                var _response_data;\n                const params = {\n                    limit,\n                    offset,\n                    sort: \"-date_created\",\n                    \"filter[status][_eq]\": status,\n                    fields: \"id,Title,Description\"\n                };\n                // Add category filter for fallback too\n                if (categoryId) {\n                    params[\"filter[Categories][id][_eq]\"] = categoryId;\n                }\n                // Try without Is_Public filter first in fallback\n                console.log(\"Fallback: Fetching posts with params:\", params);\n                const response = await directus.get(\"/Posts\", {\n                    params\n                });\n                console.log(\"Fallback posts response:\", response);\n                // Filter client-side if needed for unauthenticated users\n                let posts = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data) || response.data || [];\n                if (!isAuthenticated && Array.isArray(posts)) {\n                    // Client-side filtering as last resort\n                    posts = posts.filter((post)=>post.Is_Public !== false);\n                }\n                return {\n                    data: posts\n                };\n            } catch (fallbackError) {\n                console.error(\"Fallback error:\", fallbackError);\n                return {\n                    data: []\n                }; // Return empty array instead of throwing\n            }\n        }\n    },\n    getPost: async function(id) {\n        let isAuthenticated = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _response_data;\n            const response = await directus.get(\"/Posts/\".concat(id), {\n                params: {\n                    fields: \"id,Title,Description,date_created\"\n                }\n            });\n            console.log(\"Post response:\", response);\n            const post = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data) || response.data;\n            // For now, allow access to all posts since we can't reliably check Is_Public\n            // TODO: Implement proper Is_Public checking when permissions are configured\n            return {\n                data: post\n            };\n        } catch (error) {\n            console.error(\"Error fetching post:\", error);\n            // Fallback to even more minimal fields\n            try {\n                var _response_data1;\n                const response = await directus.get(\"/Posts/\".concat(id), {\n                    params: {\n                        fields: \"id,Title,Description\"\n                    }\n                });\n                console.log(\"Fallback post response:\", response);\n                const post = ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data) || response.data;\n                return {\n                    data: post\n                };\n            } catch (fallbackError) {\n                console.error(\"Fallback error:\", fallbackError);\n                throw fallbackError;\n            }\n        }\n    },\n    createPost: async (postData)=>{\n        const response = await directus.post(\"/Posts\", postData);\n        return response.data;\n    },\n    updatePost: async (id, postData)=>{\n        const response = await directus.patch(\"/Posts/\".concat(id), postData);\n        return response.data;\n    },\n    deletePost: async (id)=>{\n        const response = await directus.delete(\"/Posts/\".concat(id));\n        return response.data;\n    },\n    // Categories\n    getCategories: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        const response = await directus.get(\"/Categories\", {\n            params: {\n                \"filter[status][_eq]\": status,\n                sort: \"Category\",\n                fields: \"*\"\n            }\n        });\n        return response.data;\n    },\n    // Categories with proper post counts based on authentication\n    getCategoriesWithCounts: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\", isAuthenticated = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _categoriesResponse_data;\n            // Get categories first\n            const categoriesResponse = await directus.get(\"/Categories\", {\n                params: {\n                    \"filter[status][_eq]\": status,\n                    sort: \"Category\",\n                    fields: \"id,Category,status\"\n                }\n            });\n            console.log(\"Categories response:\", categoriesResponse);\n            // Handle different response structures\n            const categoriesData = ((_categoriesResponse_data = categoriesResponse.data) === null || _categoriesResponse_data === void 0 ? void 0 : _categoriesResponse_data.data) || categoriesResponse.data || [];\n            if (!Array.isArray(categoriesData)) {\n                console.error(\"Categories data is not an array:\", categoriesData);\n                return {\n                    data: []\n                };\n            }\n            // Simplified approach: Get all posts and count client-side\n            try {\n                var _allPostsResponse_data;\n                // Get all posts with minimal fields to avoid permission issues\n                const allPostsParams = {\n                    \"filter[status][_eq]\": \"published\",\n                    fields: \"id\",\n                    limit: 1000\n                };\n                // Apply Is_Public filter for unauthenticated users\n                if (!isAuthenticated) {\n                    allPostsParams[\"filter[Is_Public][_eq]\"] = true;\n                }\n                console.log(\"Fetching all posts with params:\", allPostsParams);\n                const allPostsResponse = await directus.get(\"/Posts\", {\n                    params: allPostsParams\n                });\n                console.log(\"All posts response:\", allPostsResponse);\n                const allPosts = ((_allPostsResponse_data = allPostsResponse.data) === null || _allPostsResponse_data === void 0 ? void 0 : _allPostsResponse_data.data) || allPostsResponse.data || [];\n                // For now, since we can't reliably get category relationships,\n                // let's use a simple approach: count all posts and distribute evenly\n                // or use the junction table approach\n                const totalPosts = Array.isArray(allPosts) ? allPosts.length : 0;\n                console.log(\"Total posts found: \".concat(totalPosts));\n                // Try to get category-post relationships from junction table\n                const categoriesWithCounts = await Promise.all(categoriesData.map(async (category)=>{\n                    try {\n                        // Try to query the junction table directly\n                        const junctionParams = {\n                            \"filter[Categories_id][_eq]\": category.id,\n                            fields: \"Posts_id\",\n                            limit: 1000\n                        };\n                        console.log(\"Querying junction table for category \".concat(category.id, \":\"), junctionParams);\n                        try {\n                            var _junctionResponse_data;\n                            const junctionResponse = await directus.get(\"/Categories_Posts\", {\n                                params: junctionParams\n                            });\n                            const junctionData = ((_junctionResponse_data = junctionResponse.data) === null || _junctionResponse_data === void 0 ? void 0 : _junctionResponse_data.data) || junctionResponse.data || [];\n                            let postCount = 0;\n                            if (Array.isArray(junctionData)) {\n                                // Get unique post IDs\n                                const postIds = junctionData.map((item)=>item.Posts_id).filter(Boolean);\n                                if (postIds.length > 0 && !isAuthenticated) {\n                                    // For unauthenticated users, we need to check if these posts are public\n                                    // This is complex, so for now let's use a simpler approach\n                                    postCount = postIds.length; // Assume all are public for now\n                                } else {\n                                    postCount = postIds.length;\n                                }\n                            }\n                            console.log(\"Category \".concat(category.Category, \" (ID: \").concat(category.id, \") has \").concat(postCount, \" posts via junction table\"));\n                            return {\n                                ...category,\n                                postCount: postCount\n                            };\n                        } catch (junctionError) {\n                            console.warn(\"Junction table query failed for category \".concat(category.id, \":\"), junctionError);\n                            // Fallback: distribute posts evenly among categories as a rough estimate\n                            const estimatedCount = Math.floor(totalPosts / categoriesData.length);\n                            console.log(\"Using estimated count \".concat(estimatedCount, \" for category \").concat(category.Category));\n                            return {\n                                ...category,\n                                postCount: estimatedCount\n                            };\n                        }\n                    } catch (error) {\n                        console.error(\"Error processing category \".concat(category.id, \":\"), error);\n                        return {\n                            ...category,\n                            postCount: 0\n                        };\n                    }\n                }));\n                return {\n                    data: categoriesWithCounts\n                };\n            } catch (postsError) {\n                console.error(\"Error fetching posts for counting:\", postsError);\n                // Fallback: try to count posts individually for each category\n                const categoriesWithCounts = await Promise.all(categoriesData.map(async (category)=>{\n                    try {\n                        // Try different approaches for category filtering\n                        const approaches = [\n                            // Approach 1: Direct category filter\n                            {\n                                \"filter[status][_eq]\": \"published\",\n                                \"filter[Categories][id][_eq]\": category.id,\n                                fields: \"id\",\n                                limit: 1000\n                            },\n                            // Approach 2: Using junction table\n                            {\n                                \"filter[status][_eq]\": \"published\",\n                                \"filter[Categories][Categories_id][_eq]\": category.id,\n                                fields: \"id\",\n                                limit: 1000\n                            },\n                            // Approach 3: Simple filter\n                            {\n                                \"filter[status][_eq]\": \"published\",\n                                fields: \"id,Categories\",\n                                limit: 1000\n                            }\n                        ];\n                        for (const params of approaches){\n                            try {\n                                var _postsResponse_data;\n                                if (!isAuthenticated) {\n                                    params[\"filter[Is_Public][_eq]\"] = true;\n                                }\n                                console.log(\"Trying approach for category \".concat(category.id, \":\"), params);\n                                const postsResponse = await directus.get(\"/Posts\", {\n                                    params\n                                });\n                                const posts = ((_postsResponse_data = postsResponse.data) === null || _postsResponse_data === void 0 ? void 0 : _postsResponse_data.data) || postsResponse.data || [];\n                                let postCount = 0;\n                                if (Array.isArray(posts)) {\n                                    if (params.fields === \"id,Categories\") {\n                                        // Filter client-side for approach 3\n                                        postCount = posts.filter((post)=>{\n                                            if (post.Categories && Array.isArray(post.Categories)) {\n                                                return post.Categories.some((cat)=>cat.id === category.id);\n                                            } else if (post.Categories && typeof post.Categories === \"object\") {\n                                                return post.Categories.id === category.id;\n                                            }\n                                            return false;\n                                        }).length;\n                                    } else {\n                                        postCount = posts.length;\n                                    }\n                                }\n                                console.log(\"Category \".concat(category.Category, \" (approach \").concat(approaches.indexOf(params) + 1, \"): \").concat(postCount, \" posts\"));\n                                return {\n                                    ...category,\n                                    postCount: postCount\n                                };\n                            } catch (approachError) {\n                                console.warn(\"Approach \".concat(approaches.indexOf(params) + 1, \" failed for category \").concat(category.id, \":\"), approachError);\n                                continue;\n                            }\n                        }\n                        // If all approaches fail, return 0\n                        return {\n                            ...category,\n                            postCount: 0\n                        };\n                    } catch (error) {\n                        console.error(\"Error processing category \".concat(category.id, \":\"), error);\n                        return {\n                            ...category,\n                            postCount: 0\n                        };\n                    }\n                }));\n                return {\n                    data: categoriesWithCounts\n                };\n            }\n        } catch (error) {\n            console.error(\"Error fetching categories with counts:\", error);\n            // Fallback to basic categories without counts\n            try {\n                var _fallbackResponse_data;\n                const fallbackResponse = await directus.get(\"/Categories\", {\n                    params: {\n                        \"filter[status][_eq]\": status,\n                        sort: \"Category\",\n                        fields: \"id,Category,status\"\n                    }\n                });\n                const fallbackData = ((_fallbackResponse_data = fallbackResponse.data) === null || _fallbackResponse_data === void 0 ? void 0 : _fallbackResponse_data.data) || fallbackResponse.data || [];\n                const categoriesWithZeroCounts = Array.isArray(fallbackData) ? fallbackData.map((cat)=>({\n                        ...cat,\n                        postCount: 0\n                    })) : [];\n                return {\n                    data: categoriesWithZeroCounts\n                };\n            } catch (fallbackError) {\n                console.error(\"Fallback categories fetch failed:\", fallbackError);\n                return {\n                    data: []\n                };\n            }\n        }\n    },\n    createCategory: async (categoryData)=>{\n        const response = await directus.post(\"/Categories\", categoryData);\n        return response.data;\n    },\n    // Events\n    getEvents: async function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, status = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"published\";\n        const response = await directus.get(\"/Events\", {\n            params: {\n                limit,\n                offset,\n                sort: \"Start_date\",\n                \"filter[status][_eq]\": status,\n                fields: \"*,Event_Categories.Category\"\n            }\n        });\n        return response.data;\n    },\n    getEvent: async (id)=>{\n        const response = await directus.get(\"/Events/\".concat(id), {\n            params: {\n                fields: \"*,Event_Categories.Category\"\n            }\n        });\n        return response.data;\n    },\n    createEvent: async (eventData)=>{\n        const response = await directus.post(\"/Events\", eventData);\n        return response.data;\n    },\n    // Banner Sliders\n    getBannerSliders: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        try {\n            const data = await directusFetch(\"Banner_Slider\", {\n                \"filter[status][_eq]\": status,\n                \"sort\": \"id\",\n                \"fields\": \"*\"\n            });\n            console.log(\"Banner sliders response:\", data);\n            return data;\n        } catch (error) {\n            console.error(\"Error fetching banner sliders:\", error);\n            throw error;\n        }\n    },\n    // Features\n    getFeatures: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        const response = await directus.get(\"/Features\", {\n            params: {\n                \"filter[status][_eq]\": status,\n                sort: \"id\",\n                fields: \"*\"\n            }\n        });\n        return response.data;\n    },\n    // Testimonials\n    getTestimonials: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        const response = await directus.get(\"/Testimonials\", {\n            params: {\n                \"filter[status][_eq]\": status,\n                sort: \"id\",\n                fields: \"*,user.first_name,user.last_name,user.avatar\"\n            }\n        });\n        return response.data;\n    },\n    // Social Media\n    getSocialMedia: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        const response = await directus.get(\"/Social_media\", {\n            params: {\n                \"filter[status][_eq]\": status,\n                sort: \"id\",\n                fields: \"*\"\n            }\n        });\n        return response.data;\n    },\n    // Comments\n    getComments: async function(postId) {\n        let status = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"published\";\n        const params = {\n            \"filter[status][_eq]\": status,\n            sort: \"date_created\",\n            fields: \"*,user.first_name,user.last_name,user.avatar\"\n        };\n        if (postId) {\n            params[\"filter[post][_eq]\"] = postId;\n        }\n        const response = await directus.get(\"/comments\", {\n            params\n        });\n        return response.data;\n    },\n    createComment: async (commentData)=>{\n        const response = await directus.post(\"/comments\", commentData);\n        return response.data;\n    },\n    // Users\n    getUserProfile: async (id)=>{\n        const response = await directus.get(\"/directus_users/\".concat(id));\n        return response.data;\n    },\n    updateUserProfile: async (id, userData)=>{\n        const response = await directus.patch(\"/directus_users/\".concat(id), userData);\n        return response.data;\n    },\n    // Quick test function to verify all collections are accessible\n    testAllCollections: async ()=>{\n        const results = {};\n        const collections = [\n            \"Posts\",\n            \"Categories\",\n            \"Events\",\n            \"Event_Categories\",\n            \"Banner_Slider\",\n            \"Features\",\n            \"Testimonials\",\n            \"Social_media\"\n        ];\n        for (const collection of collections){\n            try {\n                var _response_data_data, _response_data_data1;\n                const response = await directus.get(\"/\".concat(collection), {\n                    params: {\n                        limit: 1\n                    }\n                });\n                results[collection] = {\n                    success: true,\n                    count: ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.length) || 0,\n                    sample: ((_response_data_data1 = response.data.data) === null || _response_data_data1 === void 0 ? void 0 : _response_data_data1[0]) || null\n                };\n            } catch (error) {\n                results[collection] = {\n                    success: false,\n                    error: error instanceof Error ? error.message : \"Unknown error\"\n                };\n            }\n        }\n        return results;\n    }\n};\n// Authenticated API functions (require user login)\nconst authenticatedAPI = {\n    // Create a new post (requires authentication)\n    createPost: async (postData)=>{\n        try {\n            const authDirectus = getAuthenticatedDirectus();\n            const response = await authDirectus.post(\"/Posts\", {\n                ...postData,\n                status: \"draft\"\n            });\n            return response.data;\n        } catch (error) {\n            console.error(\"Create post error:\", error);\n            throw error;\n        }\n    },\n    // Update user's own post\n    updatePost: async (id, postData)=>{\n        try {\n            const authDirectus = getAuthenticatedDirectus();\n            const response = await authDirectus.patch(\"/Posts/\".concat(id), postData);\n            return response.data;\n        } catch (error) {\n            console.error(\"Update post error:\", error);\n            throw error;\n        }\n    },\n    // Get user's own posts\n    getUserPosts: async function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        try {\n            const authDirectus = getAuthenticatedDirectus();\n            const response = await authDirectus.get(\"/Posts\", {\n                params: {\n                    limit,\n                    offset,\n                    sort: \"-date_created\",\n                    \"filter[user_created][_eq]\": \"$CURRENT_USER\",\n                    fields: \"*,Categories.Category\"\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error(\"Get user posts error:\", error);\n            throw error;\n        }\n    },\n    // Update user profile\n    updateProfile: async (profileData)=>{\n        try {\n            const authDirectus = getAuthenticatedDirectus();\n            const response = await authDirectus.patch(\"/users/me\", profileData);\n            return response.data;\n        } catch (error) {\n            console.error(\"Update profile error:\", error);\n            throw error;\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/directus.ts\n"));

/***/ })

});