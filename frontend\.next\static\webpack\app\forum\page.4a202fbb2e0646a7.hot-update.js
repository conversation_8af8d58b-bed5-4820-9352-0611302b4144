"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forum/page",{

/***/ "(app-pages-browser)/./components/sidebar.tsx":
/*!********************************!*\
  !*** ./components/sidebar.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coffee.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_directus__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/directus */ \"(app-pages-browser)/./lib/directus.ts\");\n/* harmony import */ var _create_post_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./create-post-modal */ \"(app-pages-browser)/./components/create-post-modal.tsx\");\n/* harmony import */ var _auth_provider__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./auth-provider */ \"(app-pages-browser)/./components/auth-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Home\",\n        icon: _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        href: \"/\",\n        active: true\n    }\n];\n// Icon mapping for different category types\nconst categoryIconMap = {\n    \"general\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    \"prayer\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    \"discussion\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    \"spiritual\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n    \"community\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    \"tech\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    \"design\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n    \"career\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n    \"help\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n    \"random\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n    \"global\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"]\n};\n// Color mapping for categories\nconst categoryColors = [\n    \"bg-blue-500\",\n    \"bg-orange-500\",\n    \"bg-green-500\",\n    \"bg-purple-500\",\n    \"bg-red-500\",\n    \"bg-pink-500\",\n    \"bg-amber-500\",\n    \"bg-indigo-500\",\n    \"bg-teal-500\",\n    \"bg-cyan-500\"\n];\n// Function to get icon for category\nconst getCategoryIcon = (categoryName)=>{\n    const key = categoryName.toLowerCase();\n    return categoryIconMap[key] || _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]; // Default to BookOpen\n};\n// Function to get color for category\nconst getCategoryColor = (index)=>{\n    return categoryColors[index % categoryColors.length];\n};\nfunction Sidebar() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { isAuthenticated } = (0,_auth_provider__WEBPACK_IMPORTED_MODULE_9__.useAuth)();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCreatePost, setShowCreatePost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedCategoryId, setSelectedCategoryId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchCategories = async ()=>{\n            try {\n                setLoading(true);\n                setError(null);\n                // Try the new API first\n                try {\n                    const response = await _lib_directus__WEBPACK_IMPORTED_MODULE_7__.api.getCategoriesWithCounts(\"published\", isAuthenticated);\n                    if (response && response.data) {\n                        setCategories(response.data);\n                        return;\n                    }\n                } catch (countsError) {\n                    console.warn(\"Failed to fetch categories with counts, falling back to basic categories:\", countsError);\n                }\n                // Fallback to basic categories\n                const response = await _lib_directus__WEBPACK_IMPORTED_MODULE_7__.api.getCategories(\"published\");\n                if (response && response.data) {\n                    // Add zero counts to basic categories\n                    const categoriesWithZeroCounts = (response.data || []).map((cat)=>({\n                            ...cat,\n                            postCount: 0\n                        }));\n                    setCategories(categoriesWithZeroCounts);\n                }\n            } catch (err) {\n                console.error(\"Error fetching categories:\", err);\n                setError(\"Failed to load categories\");\n                setCategories([]); // Set empty array on error\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchCategories();\n    }, [\n        isAuthenticated\n    ]); // Re-fetch when authentication status changes\n    // Update selected category from URL params\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const category = searchParams.get(\"category\");\n        setSelectedCategoryId(category);\n    }, [\n        searchParams\n    ]);\n    const handleCategoryClick = (categoryId, categoryName)=>{\n        const url = \"/forum?category=\".concat(categoryId, \"&categoryName=\").concat(encodeURIComponent(categoryName));\n        router.push(url);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"fixed left-0 top-16 z-40 hidden w-64 h-[calc(100vh-4rem)] border-r bg-background lg:block\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                className: \"h-full px-3 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"mb-2 px-3 text-sm font-semibold text-muted-foreground uppercase tracking-wider\",\n                                    children: \"Navigation\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: item.active ? \"secondary\" : \"ghost\",\n                                            className: \"w-full justify-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this),\n                                                item.name\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"mb-2 px-3 text-sm font-semibold text-muted-foreground uppercase tracking-wider\",\n                                    children: \"Categories\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-orange-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 17\n                                    }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-red-500 px-3\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, this) : categories.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground px-3\",\n                                        children: \"No categories found\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 17\n                                    }, this) : categories.map((category, index)=>{\n                                        const IconComponent = getCategoryIcon(category.Category || \"\");\n                                        const color = getCategoryColor(index);\n                                        const postCount = category.postCount || 0; // Use the new postCount field\n                                        const isSelected = selectedCategoryId === category.id.toString();\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: isSelected ? \"secondary\" : \"ghost\",\n                                            className: \"w-full justify-between\",\n                                            onClick: ()=>handleCategoryClick(category.id.toString(), category.Category),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full mr-3 \".concat(color)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        category.Category\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"text-xs\",\n                                                    children: postCount\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, category.id, true, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"mb-2 px-3 text-sm font-semibold text-muted-foreground uppercase tracking-wider\",\n                                    children: \"Quick Actions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"w-full justify-start\",\n                                            onClick: ()=>setShowCreatePost(true),\n                                            disabled: !isAuthenticated,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Create Post\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isAuthenticated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground px-3 mt-1\",\n                                            children: \"Sign in to create posts\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_create_post_modal__WEBPACK_IMPORTED_MODULE_8__.CreatePostModal, {\n                open: showCreatePost,\n                onOpenChange: setShowCreatePost,\n                onPostCreated: ()=>{\n                    // Optionally refresh the page or emit an event\n                    window.location.reload();\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"TBLkWxyDimoLS9r5TxyVG/7N7sA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _auth_provider__WEBPACK_IMPORTED_MODULE_9__.useAuth\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/sidebar.tsx\n"));

/***/ })

});