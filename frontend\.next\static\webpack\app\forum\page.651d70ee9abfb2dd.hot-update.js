"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forum/page",{

/***/ "(app-pages-browser)/./components/posts-feed.tsx":
/*!***********************************!*\
  !*** ./components/posts-feed.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostsFeed: function() { return /* binding */ PostsFeed; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _post_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./post-card */ \"(app-pages-browser)/./components/post-card.tsx\");\n/* harmony import */ var _lib_directus__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/directus */ \"(app-pages-browser)/./lib/directus.ts\");\n/* harmony import */ var _components_auth_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/auth-provider */ \"(app-pages-browser)/./components/auth-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ PostsFeed auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Helper function to generate dummy avatar with first letter\nconst generateDummyAvatar = (firstName)=>{\n    var _firstName_charAt;\n    const letter = (firstName === null || firstName === void 0 ? void 0 : (_firstName_charAt = firstName.charAt(0)) === null || _firstName_charAt === void 0 ? void 0 : _firstName_charAt.toUpperCase()) || \"U\";\n    const colors = [\n        \"bg-gradient-to-br from-orange-400 to-red-500\",\n        \"bg-gradient-to-br from-amber-400 to-orange-500\",\n        \"bg-gradient-to-br from-red-400 to-pink-500\",\n        \"bg-gradient-to-br from-yellow-400 to-amber-500\",\n        \"bg-gradient-to-br from-pink-400 to-red-500\",\n        \"bg-gradient-to-br from-purple-400 to-pink-500\",\n        \"bg-gradient-to-br from-blue-400 to-purple-500\",\n        \"bg-gradient-to-br from-green-400 to-blue-500\"\n    ];\n    const colorIndex = letter.charCodeAt(0) % colors.length;\n    const gradientClass = colors[colorIndex];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-8 h-8 rounded-full \".concat(gradientClass, \" flex items-center justify-center text-white font-bold text-sm shadow-lg\"),\n        children: letter\n    }, void 0, false, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n// Helper function to get avatar URL from Directus\nconst getAvatarUrl = (avatarId)=>{\n    const directusUrl = \"http://localhost:8055\" || 0;\n    return \"\".concat(directusUrl, \"/assets/\").concat(avatarId);\n};\n// Helper function to transform Directus post to component format\nconst transformPost = (directusPost)=>{\n    var _directusPost_Categories_, _directusPost_Categories;\n    // Handle user data - might be user object, user_created ID, or null\n    const user = typeof directusPost.user === \"object\" ? directusPost.user : null;\n    const userCreatedId = directusPost.user_created || \"anonymous\";\n    const userName = user ? \"\".concat(user.first_name || \"\", \" \").concat(user.last_name || \"\").trim() : \"User \".concat(userCreatedId.slice(0, 8));\n    const firstName = (user === null || user === void 0 ? void 0 : user.first_name) || userName.split(\" \")[0] || \"User\";\n    var _directusPost_Is_Public;\n    return {\n        id: directusPost.id.toString(),\n        title: directusPost.Title,\n        content: directusPost.Description,\n        author: {\n            id: (user === null || user === void 0 ? void 0 : user.id) || userCreatedId,\n            name: userName,\n            avatar: (user === null || user === void 0 ? void 0 : user.avatar) ? getAvatarUrl(user.avatar) : null,\n            avatarFallback: generateDummyAvatar(firstName),\n            role: \"Community Member\"\n        },\n        category: ((_directusPost_Categories = directusPost.Categories) === null || _directusPost_Categories === void 0 ? void 0 : (_directusPost_Categories_ = _directusPost_Categories[0]) === null || _directusPost_Categories_ === void 0 ? void 0 : _directusPost_Categories_.Category) || \"General\",\n        tags: directusPost.Tags || [],\n        createdAt: directusPost.date_created || new Date().toISOString(),\n        likesCount: 0,\n        commentsCount: 0,\n        isLiked: false,\n        isPinned: false,\n        isPublic: (_directusPost_Is_Public = directusPost.Is_Public) !== null && _directusPost_Is_Public !== void 0 ? _directusPost_Is_Public : true\n    };\n};\nfunction PostsFeed(param) {\n    let { selectedCategory } = param;\n    _s();\n    const { isAuthenticated } = (0,_components_auth_provider__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [posts, setPosts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchPosts = async ()=>{\n            try {\n                setLoading(true);\n                const response = await _lib_directus__WEBPACK_IMPORTED_MODULE_3__.api.getPosts(20, 0, \"published\", selectedCategory || undefined);\n                if (response && response.data) {\n                    setPosts(response.data);\n                }\n            } catch (err) {\n                console.error(\"Error fetching posts:\", err);\n                setError(\"Failed to load posts\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchPosts();\n    }, [\n        selectedCategory\n    ]); // Re-fetch when selectedCategory changes\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                ...Array(3)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg border p-6 animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-gray-300 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-300 rounded w-24\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-300 rounded w-16\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-6 bg-gray-300 rounded w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded w-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded w-2/3\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, i, true, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600 dark:text-red-400\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>window.location.reload(),\n                    className: \"mt-2 text-red-600 dark:text-red-400 underline hover:no-underline\",\n                    children: \"Try again\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this);\n    }\n    if (posts.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 dark:text-gray-400 mb-2\",\n                    children: \"No posts found\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-500 dark:text-gray-500\",\n                    children: \"Be the first to create a post!\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: posts.map((directusPost)=>{\n            const transformedPost = transformPost(directusPost);\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_post_card__WEBPACK_IMPORTED_MODULE_2__.PostCard, {\n                post: transformedPost\n            }, transformedPost.id, false, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                lineNumber: 147,\n                columnNumber: 16\n            }, this);\n        })\n    }, void 0, false, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n_s(PostsFeed, \"A79ld06ONPcof5vFWT3wtdkYpGk=\", false, function() {\n    return [\n        _components_auth_provider__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c = PostsFeed;\nvar _c;\n$RefreshReg$(_c, \"PostsFeed\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/posts-feed.tsx\n"));

/***/ })

});