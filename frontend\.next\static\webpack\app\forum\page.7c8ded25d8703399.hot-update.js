"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forum/page",{

/***/ "(app-pages-browser)/./components/posts-feed.tsx":
/*!***********************************!*\
  !*** ./components/posts-feed.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostsFeed: function() { return /* binding */ PostsFeed; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _post_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./post-card */ \"(app-pages-browser)/./components/post-card.tsx\");\n/* harmony import */ var _lib_directus__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/directus */ \"(app-pages-browser)/./lib/directus.ts\");\n/* harmony import */ var _components_auth_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/auth-provider */ \"(app-pages-browser)/./components/auth-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ PostsFeed auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Helper function to generate dummy avatar with first letter\nconst generateDummyAvatar = (firstName)=>{\n    var _firstName_charAt;\n    const letter = (firstName === null || firstName === void 0 ? void 0 : (_firstName_charAt = firstName.charAt(0)) === null || _firstName_charAt === void 0 ? void 0 : _firstName_charAt.toUpperCase()) || \"U\";\n    const colors = [\n        \"bg-gradient-to-br from-orange-400 to-red-500\",\n        \"bg-gradient-to-br from-amber-400 to-orange-500\",\n        \"bg-gradient-to-br from-red-400 to-pink-500\",\n        \"bg-gradient-to-br from-yellow-400 to-amber-500\",\n        \"bg-gradient-to-br from-pink-400 to-red-500\",\n        \"bg-gradient-to-br from-purple-400 to-pink-500\",\n        \"bg-gradient-to-br from-blue-400 to-purple-500\",\n        \"bg-gradient-to-br from-green-400 to-blue-500\"\n    ];\n    const colorIndex = letter.charCodeAt(0) % colors.length;\n    const gradientClass = colors[colorIndex];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-8 h-8 rounded-full \".concat(gradientClass, \" flex items-center justify-center text-white font-bold text-sm shadow-lg\"),\n        children: letter\n    }, void 0, false, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n// Helper function to get avatar URL from Directus\nconst getAvatarUrl = (avatarId)=>{\n    const directusUrl = \"http://localhost:8055\" || 0;\n    return \"\".concat(directusUrl, \"/assets/\").concat(avatarId);\n};\n// Helper function to transform Directus post to component format\nconst transformPost = (directusPost)=>{\n    var _directusPost_Categories_, _directusPost_Categories;\n    // Handle user data - might be user object, user_created ID, or null\n    const user = typeof directusPost.user === \"object\" ? directusPost.user : null;\n    const userCreatedId = directusPost.user_created || \"anonymous\";\n    const userName = user ? \"\".concat(user.first_name || \"\", \" \").concat(user.last_name || \"\").trim() : \"User \".concat(userCreatedId.slice(0, 8));\n    const firstName = (user === null || user === void 0 ? void 0 : user.first_name) || userName.split(\" \")[0] || \"User\";\n    var _directusPost_Is_Public;\n    return {\n        id: directusPost.id.toString(),\n        title: directusPost.Title,\n        content: directusPost.Description,\n        author: {\n            id: (user === null || user === void 0 ? void 0 : user.id) || userCreatedId,\n            name: userName,\n            avatar: (user === null || user === void 0 ? void 0 : user.avatar) ? getAvatarUrl(user.avatar) : null,\n            avatarFallback: generateDummyAvatar(firstName),\n            role: \"Community Member\"\n        },\n        category: ((_directusPost_Categories = directusPost.Categories) === null || _directusPost_Categories === void 0 ? void 0 : (_directusPost_Categories_ = _directusPost_Categories[0]) === null || _directusPost_Categories_ === void 0 ? void 0 : _directusPost_Categories_.Category) || \"General\",\n        tags: directusPost.Tags || [],\n        createdAt: directusPost.date_created || new Date().toISOString(),\n        likesCount: 0,\n        commentsCount: 0,\n        isLiked: false,\n        isPinned: false,\n        isPublic: (_directusPost_Is_Public = directusPost.Is_Public) !== null && _directusPost_Is_Public !== void 0 ? _directusPost_Is_Public : true\n    };\n};\nfunction PostsFeed(param) {\n    let { selectedCategory } = param;\n    _s();\n    const { isAuthenticated } = (0,_components_auth_provider__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [posts, setPosts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchPosts = async ()=>{\n            try {\n                setLoading(true);\n                const response = await _lib_directus__WEBPACK_IMPORTED_MODULE_3__.api.getPosts(20, 0, \"published\", selectedCategory || undefined, isAuthenticated);\n                if (response && response.data) {\n                    setPosts(response.data);\n                }\n            } catch (err) {\n                console.error(\"Error fetching posts:\", err);\n                setError(\"Failed to load posts\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchPosts();\n    }, [\n        selectedCategory,\n        isAuthenticated\n    ]); // Re-fetch when selectedCategory or authentication status changes\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                ...Array(3)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg border p-6 animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-gray-300 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-300 rounded w-24\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-300 rounded w-16\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-6 bg-gray-300 rounded w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded w-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded w-2/3\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, i, true, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600 dark:text-red-400\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>window.location.reload(),\n                    className: \"mt-2 text-red-600 dark:text-red-400 underline hover:no-underline\",\n                    children: \"Try again\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this);\n    }\n    if (posts.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 dark:text-gray-400 mb-2\",\n                    children: \"No posts found\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-500 dark:text-gray-500\",\n                    children: \"Be the first to create a post!\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: posts.map((directusPost)=>{\n            const transformedPost = transformPost(directusPost);\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_post_card__WEBPACK_IMPORTED_MODULE_2__.PostCard, {\n                post: transformedPost\n            }, transformedPost.id, false, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                lineNumber: 147,\n                columnNumber: 16\n            }, this);\n        })\n    }, void 0, false, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n_s(PostsFeed, \"A79ld06ONPcof5vFWT3wtdkYpGk=\", false, function() {\n    return [\n        _components_auth_provider__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c = PostsFeed;\nvar _c;\n$RefreshReg$(_c, \"PostsFeed\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/posts-feed.tsx\n"));

/***/ })

});