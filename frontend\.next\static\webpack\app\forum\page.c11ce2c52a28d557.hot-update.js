"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forum/page",{

/***/ "(app-pages-browser)/./components/posts-feed.tsx":
/*!***********************************!*\
  !*** ./components/posts-feed.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostsFeed: function() { return /* binding */ PostsFeed; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _post_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./post-card */ \"(app-pages-browser)/./components/post-card.tsx\");\n/* harmony import */ var _lib_directus__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/directus */ \"(app-pages-browser)/./lib/directus.ts\");\n/* __next_internal_client_entry_do_not_use__ PostsFeed auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Helper function to generate dummy avatar with first letter\nconst generateDummyAvatar = (firstName)=>{\n    var _firstName_charAt;\n    const letter = (firstName === null || firstName === void 0 ? void 0 : (_firstName_charAt = firstName.charAt(0)) === null || _firstName_charAt === void 0 ? void 0 : _firstName_charAt.toUpperCase()) || \"U\";\n    const colors = [\n        \"bg-gradient-to-br from-orange-400 to-red-500\",\n        \"bg-gradient-to-br from-amber-400 to-orange-500\",\n        \"bg-gradient-to-br from-red-400 to-pink-500\",\n        \"bg-gradient-to-br from-yellow-400 to-amber-500\",\n        \"bg-gradient-to-br from-pink-400 to-red-500\",\n        \"bg-gradient-to-br from-purple-400 to-pink-500\",\n        \"bg-gradient-to-br from-blue-400 to-purple-500\",\n        \"bg-gradient-to-br from-green-400 to-blue-500\"\n    ];\n    const colorIndex = letter.charCodeAt(0) % colors.length;\n    const gradientClass = colors[colorIndex];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-8 h-8 rounded-full \".concat(gradientClass, \" flex items-center justify-center text-white font-bold text-sm shadow-lg\"),\n        children: letter\n    }, void 0, false, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n// Helper function to get avatar URL from Directus\nconst getAvatarUrl = (avatarId)=>{\n    const directusUrl = \"http://localhost:8055\" || 0;\n    return \"\".concat(directusUrl, \"/assets/\").concat(avatarId);\n};\n// Helper function to transform Directus post to component format\nconst transformPost = (directusPost)=>{\n    var _directusPost_Categories_, _directusPost_Categories;\n    // Handle user data - might be user object, user_created ID, or null\n    const user = typeof directusPost.user === \"object\" ? directusPost.user : null;\n    const userCreatedId = directusPost.user_created || \"anonymous\";\n    const userName = user ? \"\".concat(user.first_name || \"\", \" \").concat(user.last_name || \"\").trim() : \"User \".concat(userCreatedId.slice(0, 8));\n    const firstName = (user === null || user === void 0 ? void 0 : user.first_name) || userName.split(\" \")[0] || \"User\";\n    var _directusPost_Is_Public;\n    return {\n        id: directusPost.id.toString(),\n        title: directusPost.Title,\n        content: directusPost.Description,\n        author: {\n            id: (user === null || user === void 0 ? void 0 : user.id) || userCreatedId,\n            name: userName,\n            avatar: (user === null || user === void 0 ? void 0 : user.avatar) ? getAvatarUrl(user.avatar) : null,\n            avatarFallback: generateDummyAvatar(firstName),\n            role: \"Community Member\"\n        },\n        category: ((_directusPost_Categories = directusPost.Categories) === null || _directusPost_Categories === void 0 ? void 0 : (_directusPost_Categories_ = _directusPost_Categories[0]) === null || _directusPost_Categories_ === void 0 ? void 0 : _directusPost_Categories_.Category) || \"General\",\n        tags: directusPost.Tags || [],\n        createdAt: directusPost.date_created || new Date().toISOString(),\n        likesCount: 0,\n        commentsCount: 0,\n        isLiked: false,\n        isPinned: false,\n        isPublic: (_directusPost_Is_Public = directusPost.Is_Public) !== null && _directusPost_Is_Public !== void 0 ? _directusPost_Is_Public : true\n    };\n};\nfunction PostsFeed(param) {\n    let { selectedCategory } = param;\n    _s();\n    const [posts, setPosts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchPosts = async ()=>{\n            try {\n                setLoading(true);\n                const response = await _lib_directus__WEBPACK_IMPORTED_MODULE_3__.api.getPosts(20, 0, \"published\", selectedCategory || undefined);\n                if (response && response.data) {\n                    setPosts(response.data);\n                }\n            } catch (err) {\n                console.error(\"Error fetching posts:\", err);\n                setError(\"Failed to load posts\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchPosts();\n    }, [\n        selectedCategory\n    ]); // Re-fetch when selectedCategory changes\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                ...Array(3)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg border p-6 animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-gray-300 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-300 rounded w-24\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-300 rounded w-16\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-6 bg-gray-300 rounded w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded w-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded w-2/3\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, i, true, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600 dark:text-red-400\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>window.location.reload(),\n                    className: \"mt-2 text-red-600 dark:text-red-400 underline hover:no-underline\",\n                    children: \"Try again\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, this);\n    }\n    if (posts.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 dark:text-gray-400 mb-2\",\n                    children: \"No posts found\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-500 dark:text-gray-500\",\n                    children: \"Be the first to create a post!\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: posts.map((directusPost)=>{\n            const transformedPost = transformPost(directusPost);\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_post_card__WEBPACK_IMPORTED_MODULE_2__.PostCard, {\n                post: transformedPost\n            }, transformedPost.id, false, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n                lineNumber: 146,\n                columnNumber: 16\n            }, this);\n        })\n    }, void 0, false, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\posts-feed.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n_s(PostsFeed, \"FFSFm1bVdM1s2gLh8ffGbsIvlRY=\");\n_c = PostsFeed;\nvar _c;\n$RefreshReg$(_c, \"PostsFeed\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/posts-feed.tsx\n"));

/***/ })

});