"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forum/post/[id]/page",{

/***/ "(app-pages-browser)/./lib/directus.ts":
/*!*************************!*\
  !*** ./lib/directus.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: function() { return /* binding */ api; },\n/* harmony export */   auth: function() { return /* binding */ auth; },\n/* harmony export */   authAPI: function() { return /* binding */ authAPI; },\n/* harmony export */   authenticatedAPI: function() { return /* binding */ authenticatedAPI; },\n/* harmony export */   createAuthenticatedDirectus: function() { return /* binding */ createAuthenticatedDirectus; },\n/* harmony export */   directus: function() { return /* binding */ directus; },\n/* harmony export */   getAuthenticatedDirectus: function() { return /* binding */ getAuthenticatedDirectus; },\n/* harmony export */   testConnection: function() { return /* binding */ testConnection; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\n// Directus configuration\nconst DIRECTUS_URL = \"http://localhost:8055\" || 0;\nconst DIRECTUS_TOKEN = \"wHf_Vc3dv3UslEFsPhjflRI__tU_Xkx0\";\n// Helper function for Directus API calls with proper URL encoding\nconst directusFetch = async function(collection) {\n    let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const baseUrl = \"\".concat(DIRECTUS_URL, \"/items/\").concat(collection);\n    const queryParams = Object.entries(params).map((param)=>{\n        let [key, value] = param;\n        if (key.includes(\"[\") && key.includes(\"]\")) {\n            // Handle filter parameters with special encoding\n            const encodedKey = key.replace(/\\[/g, \"%5B\").replace(/\\]/g, \"%5D\");\n            return \"\".concat(encodedKey, \"=\").concat(encodeURIComponent(value));\n        }\n        return \"\".concat(key, \"=\").concat(encodeURIComponent(value));\n    }).join(\"&\");\n    const url = queryParams ? \"\".concat(baseUrl, \"?\").concat(queryParams) : baseUrl;\n    const response = await fetch(url, {\n        headers: {\n            \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n            \"Content-Type\": \"application/json\"\n        }\n    });\n    if (!response.ok) {\n        throw new Error(\"HTTP error! status: \".concat(response.status));\n    }\n    return response.json();\n};\n// Create axios instance for Directus API (public token)\nconst directus = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: \"\".concat(DIRECTUS_URL, \"/items\"),\n    headers: {\n        \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Create authenticated axios instance (user token)\nconst createAuthenticatedDirectus = (userToken)=>{\n    return axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n        baseURL: \"\".concat(DIRECTUS_URL, \"/items\"),\n        headers: {\n            \"Authorization\": \"Bearer \".concat(userToken),\n            \"Content-Type\": \"application/json\"\n        }\n    });\n};\n// Get authenticated instance from localStorage\nconst getAuthenticatedDirectus = ()=>{\n    const token = localStorage.getItem(\"directus_access_token\");\n    if (!token) {\n        throw new Error(\"No authentication token found\");\n    }\n    return createAuthenticatedDirectus(token);\n};\n// Authentication endpoints\nconst auth = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: \"\".concat(DIRECTUS_URL, \"/auth\"),\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Test connection function\nconst testConnection = async ()=>{\n    try {\n        // Test basic connection by fetching server info\n        const serverResponse = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(DIRECTUS_URL, \"/server/info\"));\n        console.log(\"Server info:\", serverResponse.data);\n        // Test actual collections with public token\n        const testResults = {\n            server: serverResponse.data,\n            collections: {}\n        };\n        // Test each collection\n        const collectionsToTest = [\n            \"Posts\",\n            \"Categories\",\n            \"Events\",\n            \"Event_Categories\",\n            \"Banner_Slider\",\n            \"Features\",\n            \"Testimonials\",\n            \"Social_media\"\n        ];\n        for (const collection of collectionsToTest){\n            try {\n                var _response_data_data;\n                const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(DIRECTUS_URL, \"/items/\").concat(collection), {\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n                        \"Content-Type\": \"application/json\"\n                    },\n                    params: {\n                        limit: 5,\n                        fields: \"id,status,Title,Category\"\n                    }\n                });\n                testResults.collections[collection] = {\n                    success: true,\n                    count: ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.length) || 0,\n                    data: response.data.data || []\n                };\n            } catch (collectionError) {\n                testResults.collections[collection] = {\n                    success: false,\n                    error: collectionError instanceof Error ? collectionError.message : \"Unknown error\"\n                };\n            }\n        }\n        return {\n            success: true,\n            ...testResults\n        };\n    } catch (error) {\n        console.error(\"Connection test failed:\", error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        };\n    }\n};\n// Authentication API functions\nconst authAPI = {\n    // Login with Directus\n    login: async (email, password)=>{\n        try {\n            const response = await fetch(\"\".concat(DIRECTUS_URL, \"/auth/login\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            if (!response.ok) {\n                var _error_errors_, _error_errors;\n                const error = await response.json();\n                throw new Error(((_error_errors = error.errors) === null || _error_errors === void 0 ? void 0 : (_error_errors_ = _error_errors[0]) === null || _error_errors_ === void 0 ? void 0 : _error_errors_.message) || \"Login failed\");\n            }\n            const data = await response.json();\n            return data.data;\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            throw error;\n        }\n    },\n    // Register new user with Writer role\n    register: async (userData)=>{\n        try {\n            // First, get the Writer role ID\n            const rolesResponse = await fetch(\"\".concat(DIRECTUS_URL, \"/roles?filter[name][_eq]=Writer\"), {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            let writerRoleId = null;\n            if (rolesResponse.ok) {\n                var _rolesData_data_, _rolesData_data;\n                const rolesData = await rolesResponse.json();\n                writerRoleId = (_rolesData_data = rolesData.data) === null || _rolesData_data === void 0 ? void 0 : (_rolesData_data_ = _rolesData_data[0]) === null || _rolesData_data_ === void 0 ? void 0 : _rolesData_data_.id;\n            }\n            // If Writer role not found, we'll let Directus handle the default role\n            const userPayload = {\n                ...userData,\n                status: \"active\",\n                ...writerRoleId && {\n                    role: writerRoleId\n                }\n            };\n            const response = await fetch(\"\".concat(DIRECTUS_URL, \"/users\"), {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(userPayload)\n            });\n            if (!response.ok) {\n                var _error_errors_, _error_errors;\n                const error = await response.json();\n                throw new Error(((_error_errors = error.errors) === null || _error_errors === void 0 ? void 0 : (_error_errors_ = _error_errors[0]) === null || _error_errors_ === void 0 ? void 0 : _error_errors_.message) || \"Registration failed\");\n            }\n            const data = await response.json();\n            return data.data;\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            throw error;\n        }\n    },\n    // Get current user info\n    getCurrentUser: async (token)=>{\n        try {\n            const response = await fetch(\"\".concat(DIRECTUS_URL, \"/users/me\"), {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to get user info\");\n            }\n            const data = await response.json();\n            return data.data;\n        } catch (error) {\n            console.error(\"Get user error:\", error);\n            throw error;\n        }\n    },\n    // Refresh token\n    refresh: async (refreshToken)=>{\n        try {\n            const response = await fetch(\"\".concat(DIRECTUS_URL, \"/auth/refresh\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refresh_token: refreshToken\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to refresh token\");\n            }\n            const data = await response.json();\n            return data.data;\n        } catch (error) {\n            console.error(\"Refresh token error:\", error);\n            throw error;\n        }\n    },\n    // Logout\n    logout: async (refreshToken)=>{\n        try {\n            await fetch(\"\".concat(DIRECTUS_URL, \"/auth/logout\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refresh_token: refreshToken\n                })\n            });\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        // Don't throw error for logout, just log it\n        }\n    }\n};\n// API functions\nconst api = {\n    // Posts\n    getPosts: async function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, status = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"published\", categoryId = arguments.length > 3 ? arguments[3] : void 0, isAuthenticated = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : false;\n        try {\n            // Start with basic fields that should have public access\n            const params = {\n                limit,\n                offset,\n                sort: \"-date_created\",\n                \"filter[status][_eq]\": status,\n                fields: \"id,Title,Description,date_created\"\n            };\n            // Add category filter if specified (filter by category ID through M2M relationship)\n            if (categoryId) {\n                params[\"filter[Categories][id][_eq]\"] = categoryId;\n            }\n            // Filter based on authentication status and Is_Public field\n            if (!isAuthenticated) {\n                // For unauthenticated users, only show public posts (Is_Public = true)\n                params[\"filter[Is_Public][_eq]\"] = true;\n            }\n            // For authenticated users, show all posts regardless of Is_Public value\n            console.log(\"Fetching posts with params:\", params);\n            const response = await directus.get(\"/Posts\", {\n                params\n            });\n            console.log(\"Posts response:\", response);\n            return response.data;\n        } catch (error) {\n            console.error(\"Error fetching posts:\", error);\n            // Fallback to even more minimal fields\n            try {\n                var _response_data;\n                const params = {\n                    limit,\n                    offset,\n                    sort: \"-date_created\",\n                    \"filter[status][_eq]\": status,\n                    fields: \"id,Title,Description\"\n                };\n                // Add category filter for fallback too\n                if (categoryId) {\n                    params[\"filter[Categories][id][_eq]\"] = categoryId;\n                }\n                // Try without Is_Public filter first in fallback\n                console.log(\"Fallback: Fetching posts with params:\", params);\n                const response = await directus.get(\"/Posts\", {\n                    params\n                });\n                console.log(\"Fallback posts response:\", response);\n                // Filter client-side if needed for unauthenticated users\n                let posts = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data) || response.data || [];\n                if (!isAuthenticated && Array.isArray(posts)) {\n                    // Client-side filtering as last resort\n                    posts = posts.filter((post)=>post.Is_Public !== false);\n                }\n                return {\n                    data: posts\n                };\n            } catch (fallbackError) {\n                console.error(\"Fallback error:\", fallbackError);\n                return {\n                    data: []\n                }; // Return empty array instead of throwing\n            }\n        }\n    },\n    getPost: async function(id) {\n        let isAuthenticated = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _response_data;\n            const response = await directus.get(\"/Posts/\".concat(id), {\n                params: {\n                    fields: \"id,Title,Description,date_created\"\n                }\n            });\n            console.log(\"Post response:\", response);\n            const post = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data) || response.data;\n            // For now, allow access to all posts since we can't reliably check Is_Public\n            // TODO: Implement proper Is_Public checking when permissions are configured\n            return {\n                data: post\n            };\n        } catch (error) {\n            console.error(\"Error fetching post:\", error);\n            // Fallback to even more minimal fields\n            try {\n                var _response_data1;\n                const response = await directus.get(\"/Posts/\".concat(id), {\n                    params: {\n                        fields: \"id,Title,Description\"\n                    }\n                });\n                console.log(\"Fallback post response:\", response);\n                const post = ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data) || response.data;\n                return {\n                    data: post\n                };\n            } catch (fallbackError) {\n                console.error(\"Fallback error:\", fallbackError);\n                throw fallbackError;\n            }\n        }\n    },\n    createPost: async (postData)=>{\n        const response = await directus.post(\"/Posts\", postData);\n        return response.data;\n    },\n    updatePost: async (id, postData)=>{\n        const response = await directus.patch(\"/Posts/\".concat(id), postData);\n        return response.data;\n    },\n    deletePost: async (id)=>{\n        const response = await directus.delete(\"/Posts/\".concat(id));\n        return response.data;\n    },\n    // Categories\n    getCategories: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        const response = await directus.get(\"/Categories\", {\n            params: {\n                \"filter[status][_eq]\": status,\n                sort: \"Category\",\n                fields: \"*\"\n            }\n        });\n        return response.data;\n    },\n    // Categories with proper post counts based on authentication\n    getCategoriesWithCounts: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\", isAuthenticated = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _categoriesResponse_data;\n            // Get categories first\n            const categoriesResponse = await directus.get(\"/Categories\", {\n                params: {\n                    \"filter[status][_eq]\": status,\n                    sort: \"Category\",\n                    fields: \"id,Category,status\"\n                }\n            });\n            console.log(\"Categories response:\", categoriesResponse);\n            // Handle different response structures\n            const categoriesData = ((_categoriesResponse_data = categoriesResponse.data) === null || _categoriesResponse_data === void 0 ? void 0 : _categoriesResponse_data.data) || categoriesResponse.data || [];\n            if (!Array.isArray(categoriesData)) {\n                console.error(\"Categories data is not an array:\", categoriesData);\n                return {\n                    data: []\n                };\n            }\n            // Simplified approach: Get all posts and count client-side\n            try {\n                var _allPostsResponse_data;\n                // Get all posts with minimal fields to avoid permission issues\n                const allPostsParams = {\n                    \"filter[status][_eq]\": \"published\",\n                    fields: \"id\",\n                    limit: 1000\n                };\n                // Apply Is_Public filter for unauthenticated users\n                if (!isAuthenticated) {\n                    allPostsParams[\"filter[Is_Public][_eq]\"] = true;\n                }\n                console.log(\"Fetching all posts with params:\", allPostsParams);\n                const allPostsResponse = await directus.get(\"/Posts\", {\n                    params: allPostsParams\n                });\n                console.log(\"All posts response:\", allPostsResponse);\n                const allPosts = ((_allPostsResponse_data = allPostsResponse.data) === null || _allPostsResponse_data === void 0 ? void 0 : _allPostsResponse_data.data) || allPostsResponse.data || [];\n                // For now, since we can't reliably get category relationships,\n                // let's use a simple approach: count all posts and distribute evenly\n                // or use the junction table approach\n                const totalPosts = Array.isArray(allPosts) ? allPosts.length : 0;\n                console.log(\"Total posts found: \".concat(totalPosts));\n                // Try to get category-post relationships from junction table\n                const categoriesWithCounts = await Promise.all(categoriesData.map(async (category)=>{\n                    try {\n                        // Try to query the junction table directly\n                        const junctionParams = {\n                            \"filter[Categories_id][_eq]\": category.id,\n                            fields: \"Posts_id\",\n                            limit: 1000\n                        };\n                        console.log(\"Querying junction table for category \".concat(category.id, \":\"), junctionParams);\n                        try {\n                            var _junctionResponse_data;\n                            const junctionResponse = await directus.get(\"/Categories_Posts\", {\n                                params: junctionParams\n                            });\n                            const junctionData = ((_junctionResponse_data = junctionResponse.data) === null || _junctionResponse_data === void 0 ? void 0 : _junctionResponse_data.data) || junctionResponse.data || [];\n                            let postCount = 0;\n                            if (Array.isArray(junctionData)) {\n                                // Get unique post IDs\n                                const postIds = junctionData.map((item)=>item.Posts_id).filter(Boolean);\n                                if (postIds.length > 0 && !isAuthenticated) {\n                                    // For unauthenticated users, we need to check if these posts are public\n                                    // This is complex, so for now let's use a simpler approach\n                                    postCount = postIds.length; // Assume all are public for now\n                                } else {\n                                    postCount = postIds.length;\n                                }\n                            }\n                            console.log(\"Category \".concat(category.Category, \" (ID: \").concat(category.id, \") has \").concat(postCount, \" posts via junction table\"));\n                            return {\n                                ...category,\n                                postCount: postCount\n                            };\n                        } catch (junctionError) {\n                            console.warn(\"Junction table query failed for category \".concat(category.id, \":\"), junctionError);\n                            // Fallback: distribute posts evenly among categories as a rough estimate\n                            const estimatedCount = Math.floor(totalPosts / categoriesData.length);\n                            console.log(\"Using estimated count \".concat(estimatedCount, \" for category \").concat(category.Category));\n                            return {\n                                ...category,\n                                postCount: estimatedCount\n                            };\n                        }\n                    } catch (error) {\n                        console.error(\"Error processing category \".concat(category.id, \":\"), error);\n                        return {\n                            ...category,\n                            postCount: 0\n                        };\n                    }\n                }));\n                return {\n                    data: categoriesWithCounts\n                };\n            } catch (postsError) {\n                console.error(\"Error fetching posts for counting:\", postsError);\n                // Fallback to basic categories without counts\n                const categoriesWithZeroCounts = categoriesData.map((cat)=>({\n                        ...cat,\n                        postCount: 0\n                    }));\n                return {\n                    data: categoriesWithZeroCounts\n                };\n            }\n        } catch (error) {\n            console.error(\"Error fetching categories with counts:\", error);\n            // Fallback to basic categories without counts\n            try {\n                var _fallbackResponse_data;\n                const fallbackResponse = await directus.get(\"/Categories\", {\n                    params: {\n                        \"filter[status][_eq]\": status,\n                        sort: \"Category\",\n                        fields: \"id,Category,status\"\n                    }\n                });\n                const fallbackData = ((_fallbackResponse_data = fallbackResponse.data) === null || _fallbackResponse_data === void 0 ? void 0 : _fallbackResponse_data.data) || fallbackResponse.data || [];\n                const categoriesWithZeroCounts = Array.isArray(fallbackData) ? fallbackData.map((cat)=>({\n                        ...cat,\n                        postCount: 0\n                    })) : [];\n                return {\n                    data: categoriesWithZeroCounts\n                };\n            } catch (fallbackError) {\n                console.error(\"Fallback categories fetch failed:\", fallbackError);\n                return {\n                    data: []\n                };\n            }\n        }\n    },\n    createCategory: async (categoryData)=>{\n        const response = await directus.post(\"/Categories\", categoryData);\n        return response.data;\n    },\n    // Events\n    getEvents: async function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, status = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"published\";\n        const response = await directus.get(\"/Events\", {\n            params: {\n                limit,\n                offset,\n                sort: \"Start_date\",\n                \"filter[status][_eq]\": status,\n                fields: \"*,Event_Categories.Category\"\n            }\n        });\n        return response.data;\n    },\n    getEvent: async (id)=>{\n        const response = await directus.get(\"/Events/\".concat(id), {\n            params: {\n                fields: \"*,Event_Categories.Category\"\n            }\n        });\n        return response.data;\n    },\n    createEvent: async (eventData)=>{\n        const response = await directus.post(\"/Events\", eventData);\n        return response.data;\n    },\n    // Banner Sliders\n    getBannerSliders: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        try {\n            const data = await directusFetch(\"Banner_Slider\", {\n                \"filter[status][_eq]\": status,\n                \"sort\": \"id\",\n                \"fields\": \"*\"\n            });\n            console.log(\"Banner sliders response:\", data);\n            return data;\n        } catch (error) {\n            console.error(\"Error fetching banner sliders:\", error);\n            throw error;\n        }\n    },\n    // Features\n    getFeatures: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        const response = await directus.get(\"/Features\", {\n            params: {\n                \"filter[status][_eq]\": status,\n                sort: \"id\",\n                fields: \"*\"\n            }\n        });\n        return response.data;\n    },\n    // Testimonials\n    getTestimonials: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        const response = await directus.get(\"/Testimonials\", {\n            params: {\n                \"filter[status][_eq]\": status,\n                sort: \"id\",\n                fields: \"*,user.first_name,user.last_name,user.avatar\"\n            }\n        });\n        return response.data;\n    },\n    // Social Media\n    getSocialMedia: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        const response = await directus.get(\"/Social_media\", {\n            params: {\n                \"filter[status][_eq]\": status,\n                sort: \"id\",\n                fields: \"*\"\n            }\n        });\n        return response.data;\n    },\n    // Comments\n    getComments: async function(postId) {\n        let status = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"published\";\n        const params = {\n            \"filter[status][_eq]\": status,\n            sort: \"date_created\",\n            fields: \"*,user.first_name,user.last_name,user.avatar\"\n        };\n        if (postId) {\n            params[\"filter[post][_eq]\"] = postId;\n        }\n        const response = await directus.get(\"/comments\", {\n            params\n        });\n        return response.data;\n    },\n    createComment: async (commentData)=>{\n        const response = await directus.post(\"/comments\", commentData);\n        return response.data;\n    },\n    // Users\n    getUserProfile: async (id)=>{\n        const response = await directus.get(\"/directus_users/\".concat(id));\n        return response.data;\n    },\n    updateUserProfile: async (id, userData)=>{\n        const response = await directus.patch(\"/directus_users/\".concat(id), userData);\n        return response.data;\n    },\n    // Quick test function to verify all collections are accessible\n    testAllCollections: async ()=>{\n        const results = {};\n        const collections = [\n            \"Posts\",\n            \"Categories\",\n            \"Events\",\n            \"Event_Categories\",\n            \"Banner_Slider\",\n            \"Features\",\n            \"Testimonials\",\n            \"Social_media\"\n        ];\n        for (const collection of collections){\n            try {\n                var _response_data_data, _response_data_data1;\n                const response = await directus.get(\"/\".concat(collection), {\n                    params: {\n                        limit: 1\n                    }\n                });\n                results[collection] = {\n                    success: true,\n                    count: ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.length) || 0,\n                    sample: ((_response_data_data1 = response.data.data) === null || _response_data_data1 === void 0 ? void 0 : _response_data_data1[0]) || null\n                };\n            } catch (error) {\n                results[collection] = {\n                    success: false,\n                    error: error instanceof Error ? error.message : \"Unknown error\"\n                };\n            }\n        }\n        return results;\n    }\n};\n// Authenticated API functions (require user login)\nconst authenticatedAPI = {\n    // Create a new post (requires authentication)\n    createPost: async (postData)=>{\n        try {\n            const authDirectus = getAuthenticatedDirectus();\n            const response = await authDirectus.post(\"/Posts\", {\n                ...postData,\n                status: \"draft\"\n            });\n            return response.data;\n        } catch (error) {\n            console.error(\"Create post error:\", error);\n            throw error;\n        }\n    },\n    // Update user's own post\n    updatePost: async (id, postData)=>{\n        try {\n            const authDirectus = getAuthenticatedDirectus();\n            const response = await authDirectus.patch(\"/Posts/\".concat(id), postData);\n            return response.data;\n        } catch (error) {\n            console.error(\"Update post error:\", error);\n            throw error;\n        }\n    },\n    // Get user's own posts\n    getUserPosts: async function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        try {\n            const authDirectus = getAuthenticatedDirectus();\n            const response = await authDirectus.get(\"/Posts\", {\n                params: {\n                    limit,\n                    offset,\n                    sort: \"-date_created\",\n                    \"filter[user_created][_eq]\": \"$CURRENT_USER\",\n                    fields: \"*,Categories.Category\"\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error(\"Get user posts error:\", error);\n            throw error;\n        }\n    },\n    // Update user profile\n    updateProfile: async (profileData)=>{\n        try {\n            const authDirectus = getAuthenticatedDirectus();\n            const response = await authDirectus.patch(\"/users/me\", profileData);\n            return response.data;\n        } catch (error) {\n            console.error(\"Update profile error:\", error);\n            throw error;\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/directus.ts\n"));

/***/ })

});