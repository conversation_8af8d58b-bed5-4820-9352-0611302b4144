"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forum/post/[id]/page",{

/***/ "(app-pages-browser)/./components/sidebar.tsx":
/*!********************************!*\
  !*** ./components/sidebar.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coffee.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_directus__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/directus */ \"(app-pages-browser)/./lib/directus.ts\");\n/* harmony import */ var _create_post_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./create-post-modal */ \"(app-pages-browser)/./components/create-post-modal.tsx\");\n/* harmony import */ var _auth_provider__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./auth-provider */ \"(app-pages-browser)/./components/auth-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Back to Home\",\n        icon: _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        href: \"./\",\n        active: true\n    }\n];\n// Icon mapping for different category types\nconst categoryIconMap = {\n    \"general\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    \"prayer\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    \"discussion\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    \"spiritual\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n    \"community\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    \"tech\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    \"design\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n    \"career\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n    \"help\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n    \"random\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n    \"global\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"]\n};\n// Color mapping for categories\nconst categoryColors = [\n    \"bg-blue-500\",\n    \"bg-orange-500\",\n    \"bg-green-500\",\n    \"bg-purple-500\",\n    \"bg-red-500\",\n    \"bg-pink-500\",\n    \"bg-amber-500\",\n    \"bg-indigo-500\",\n    \"bg-teal-500\",\n    \"bg-cyan-500\"\n];\n// Function to get icon for category\nconst getCategoryIcon = (categoryName)=>{\n    const key = categoryName.toLowerCase();\n    return categoryIconMap[key] || _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]; // Default to BookOpen\n};\n// Function to get color for category\nconst getCategoryColor = (index)=>{\n    return categoryColors[index % categoryColors.length];\n};\nfunction Sidebar() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { isAuthenticated } = (0,_auth_provider__WEBPACK_IMPORTED_MODULE_9__.useAuth)();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCreatePost, setShowCreatePost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedCategoryId, setSelectedCategoryId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchCategories = async ()=>{\n            try {\n                setLoading(true);\n                setError(null);\n                // Try the new API first\n                try {\n                    const response = await _lib_directus__WEBPACK_IMPORTED_MODULE_7__.api.getCategoriesWithCounts(\"published\", isAuthenticated);\n                    if (response && response.data) {\n                        setCategories(response.data);\n                        return;\n                    }\n                } catch (countsError) {\n                    console.warn(\"Failed to fetch categories with counts, falling back to basic categories:\", countsError);\n                }\n                // Fallback to basic categories\n                const response = await _lib_directus__WEBPACK_IMPORTED_MODULE_7__.api.getCategories(\"published\");\n                if (response && response.data) {\n                    // Add zero counts to basic categories\n                    const categoriesWithZeroCounts = (response.data || []).map((cat)=>({\n                            ...cat,\n                            postCount: 0\n                        }));\n                    setCategories(categoriesWithZeroCounts);\n                }\n            } catch (err) {\n                console.error(\"Error fetching categories:\", err);\n                setError(\"Failed to load categories\");\n                setCategories([]); // Set empty array on error\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchCategories();\n    }, [\n        isAuthenticated\n    ]); // Re-fetch when authentication status changes\n    // Update selected category from URL params\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const category = searchParams.get(\"category\");\n        setSelectedCategoryId(category);\n    }, [\n        searchParams\n    ]);\n    const handleCategoryClick = (categoryId, categoryName)=>{\n        const url = \"/forum?category=\".concat(categoryId, \"&categoryName=\").concat(encodeURIComponent(categoryName));\n        router.push(url);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"fixed left-0 top-16 z-40 hidden w-64 h-[calc(100vh-4rem)] border-r bg-background lg:block\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                className: \"h-full px-3 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"mb-2 px-3 text-sm font-semibold text-muted-foreground uppercase tracking-wider\",\n                                    children: \"Navigation\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: item.active ? \"secondary\" : \"ghost\",\n                                            className: \"w-full justify-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this),\n                                                item.name\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"mb-2 px-3 text-sm font-semibold text-muted-foreground uppercase tracking-wider\",\n                                    children: \"Categories\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-orange-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 17\n                                    }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-red-500 px-3\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, this) : categories.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground px-3\",\n                                        children: \"No categories found\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 17\n                                    }, this) : categories.map((category, index)=>{\n                                        const IconComponent = getCategoryIcon(category.Category || \"\");\n                                        const color = getCategoryColor(index);\n                                        const postCount = category.postCount || 0; // Use the new postCount field\n                                        const isSelected = selectedCategoryId === category.id.toString();\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: isSelected ? \"secondary\" : \"ghost\",\n                                            className: \"w-full justify-between\",\n                                            onClick: ()=>handleCategoryClick(category.id.toString(), category.Category),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full mr-3 \".concat(color)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        category.Category\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"text-xs\",\n                                                    children: postCount\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, category.id, true, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"mb-2 px-3 text-sm font-semibold text-muted-foreground uppercase tracking-wider\",\n                                    children: \"Quick Actions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"w-full justify-start\",\n                                            onClick: ()=>setShowCreatePost(true),\n                                            disabled: !isAuthenticated,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Create Post\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isAuthenticated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground px-3 mt-1\",\n                                            children: \"Sign in to create posts\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_create_post_modal__WEBPACK_IMPORTED_MODULE_8__.CreatePostModal, {\n                open: showCreatePost,\n                onOpenChange: setShowCreatePost,\n                onPostCreated: ()=>{\n                    // Optionally refresh the page or emit an event\n                    window.location.reload();\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"TBLkWxyDimoLS9r5TxyVG/7N7sA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _auth_provider__WEBPACK_IMPORTED_MODULE_9__.useAuth\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/sidebar.tsx\n"));

/***/ })

});