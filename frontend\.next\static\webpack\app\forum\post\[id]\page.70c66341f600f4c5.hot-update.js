"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forum/post/[id]/page",{

/***/ "(app-pages-browser)/./components/sidebar.tsx":
/*!********************************!*\
  !*** ./components/sidebar.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coffee.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Users,BookOpen,HelpCircle,Zap,Code,Palette,Briefcase,Coffee,Heart,MessageCircle,Star,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_directus__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/directus */ \"(app-pages-browser)/./lib/directus.ts\");\n/* harmony import */ var _create_post_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./create-post-modal */ \"(app-pages-browser)/./components/create-post-modal.tsx\");\n/* harmony import */ var _auth_provider__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./auth-provider */ \"(app-pages-browser)/./components/auth-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Home\",\n        icon: _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        href: \"/\",\n        active: true\n    }\n];\n// Icon mapping for different category types\nconst categoryIconMap = {\n    \"general\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    \"prayer\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    \"discussion\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    \"spiritual\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n    \"community\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    \"tech\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    \"design\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n    \"career\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n    \"help\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n    \"random\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n    \"global\": _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"]\n};\n// Color mapping for categories\nconst categoryColors = [\n    \"bg-blue-500\",\n    \"bg-orange-500\",\n    \"bg-green-500\",\n    \"bg-purple-500\",\n    \"bg-red-500\",\n    \"bg-pink-500\",\n    \"bg-amber-500\",\n    \"bg-indigo-500\",\n    \"bg-teal-500\",\n    \"bg-cyan-500\"\n];\n// Function to get icon for category\nconst getCategoryIcon = (categoryName)=>{\n    const key = categoryName.toLowerCase();\n    return categoryIconMap[key] || _barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]; // Default to BookOpen\n};\n// Function to get color for category\nconst getCategoryColor = (index)=>{\n    return categoryColors[index % categoryColors.length];\n};\nfunction Sidebar() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { isAuthenticated } = (0,_auth_provider__WEBPACK_IMPORTED_MODULE_9__.useAuth)();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCreatePost, setShowCreatePost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedCategoryId, setSelectedCategoryId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchCategories = async ()=>{\n            try {\n                setLoading(true);\n                const response = await _lib_directus__WEBPACK_IMPORTED_MODULE_7__.api.getCategoriesWithCounts(\"published\", isAuthenticated);\n                if (response && response.data) {\n                    setCategories(response.data);\n                }\n            } catch (err) {\n                console.error(\"Error fetching categories:\", err);\n                setError(\"Failed to load categories\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchCategories();\n    }, [\n        isAuthenticated\n    ]); // Re-fetch when authentication status changes\n    // Update selected category from URL params\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const category = searchParams.get(\"category\");\n        setSelectedCategoryId(category);\n    }, [\n        searchParams\n    ]);\n    const handleCategoryClick = (categoryId, categoryName)=>{\n        const url = \"/forum?category=\".concat(categoryId, \"&categoryName=\").concat(encodeURIComponent(categoryName));\n        router.push(url);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"fixed left-0 top-16 z-40 hidden w-64 h-[calc(100vh-4rem)] border-r bg-background lg:block\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                className: \"h-full px-3 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"mb-2 px-3 text-sm font-semibold text-muted-foreground uppercase tracking-wider\",\n                                    children: \"Navigation\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: item.active ? \"secondary\" : \"ghost\",\n                                            className: \"w-full justify-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 19\n                                                }, this),\n                                                item.name\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"mb-2 px-3 text-sm font-semibold text-muted-foreground uppercase tracking-wider\",\n                                    children: \"Categories\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-orange-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-red-500 px-3\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, this) : categories.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground px-3\",\n                                        children: \"No categories found\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, this) : categories.map((category, index)=>{\n                                        const IconComponent = getCategoryIcon(category.Category || \"\");\n                                        const color = getCategoryColor(index);\n                                        const postCount = Array.isArray(category.post) ? category.post.length : 0;\n                                        const isSelected = selectedCategoryId === category.id.toString();\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: isSelected ? \"secondary\" : \"ghost\",\n                                            className: \"w-full justify-between\",\n                                            onClick: ()=>handleCategoryClick(category.id.toString(), category.Category),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full mr-3 \".concat(color)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        category.Category\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"text-xs\",\n                                                    children: postCount\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, category.id, true, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"mb-2 px-3 text-sm font-semibold text-muted-foreground uppercase tracking-wider\",\n                                    children: \"Quick Actions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"w-full justify-start\",\n                                            onClick: ()=>setShowCreatePost(true),\n                                            disabled: !isAuthenticated,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Users_BookOpen_HelpCircle_Zap_Code_Palette_Briefcase_Coffee_Heart_MessageCircle_Star_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Create Post\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isAuthenticated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground px-3 mt-1\",\n                                            children: \"Sign in to create posts\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_create_post_modal__WEBPACK_IMPORTED_MODULE_8__.CreatePostModal, {\n                open: showCreatePost,\n                onOpenChange: setShowCreatePost,\n                onPostCreated: ()=>{\n                    // Optionally refresh the page or emit an event\n                    window.location.reload();\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\sidebar.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"TBLkWxyDimoLS9r5TxyVG/7N7sA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _auth_provider__WEBPACK_IMPORTED_MODULE_9__.useAuth\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/sidebar.tsx\n"));

/***/ })

});