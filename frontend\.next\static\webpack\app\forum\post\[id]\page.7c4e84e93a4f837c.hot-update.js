"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forum/post/[id]/page",{

/***/ "(app-pages-browser)/./lib/directus.ts":
/*!*************************!*\
  !*** ./lib/directus.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: function() { return /* binding */ api; },\n/* harmony export */   auth: function() { return /* binding */ auth; },\n/* harmony export */   authAPI: function() { return /* binding */ authAPI; },\n/* harmony export */   authenticatedAPI: function() { return /* binding */ authenticatedAPI; },\n/* harmony export */   createAuthenticatedDirectus: function() { return /* binding */ createAuthenticatedDirectus; },\n/* harmony export */   directus: function() { return /* binding */ directus; },\n/* harmony export */   getAuthenticatedDirectus: function() { return /* binding */ getAuthenticatedDirectus; },\n/* harmony export */   testConnection: function() { return /* binding */ testConnection; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\n// Directus configuration\nconst DIRECTUS_URL = \"http://localhost:8055\" || 0;\nconst DIRECTUS_TOKEN = \"wHf_Vc3dv3UslEFsPhjflRI__tU_Xkx0\";\n// Helper function for Directus API calls with proper URL encoding\nconst directusFetch = async function(collection) {\n    let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const baseUrl = \"\".concat(DIRECTUS_URL, \"/items/\").concat(collection);\n    const queryParams = Object.entries(params).map((param)=>{\n        let [key, value] = param;\n        if (key.includes(\"[\") && key.includes(\"]\")) {\n            // Handle filter parameters with special encoding\n            const encodedKey = key.replace(/\\[/g, \"%5B\").replace(/\\]/g, \"%5D\");\n            return \"\".concat(encodedKey, \"=\").concat(encodeURIComponent(value));\n        }\n        return \"\".concat(key, \"=\").concat(encodeURIComponent(value));\n    }).join(\"&\");\n    const url = queryParams ? \"\".concat(baseUrl, \"?\").concat(queryParams) : baseUrl;\n    const response = await fetch(url, {\n        headers: {\n            \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n            \"Content-Type\": \"application/json\"\n        }\n    });\n    if (!response.ok) {\n        throw new Error(\"HTTP error! status: \".concat(response.status));\n    }\n    return response.json();\n};\n// Create axios instance for Directus API (public token)\nconst directus = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: \"\".concat(DIRECTUS_URL, \"/items\"),\n    headers: {\n        \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Create authenticated axios instance (user token)\nconst createAuthenticatedDirectus = (userToken)=>{\n    return axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n        baseURL: \"\".concat(DIRECTUS_URL, \"/items\"),\n        headers: {\n            \"Authorization\": \"Bearer \".concat(userToken),\n            \"Content-Type\": \"application/json\"\n        }\n    });\n};\n// Get authenticated instance from localStorage\nconst getAuthenticatedDirectus = ()=>{\n    const token = localStorage.getItem(\"directus_access_token\");\n    if (!token) {\n        throw new Error(\"No authentication token found\");\n    }\n    return createAuthenticatedDirectus(token);\n};\n// Authentication endpoints\nconst auth = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: \"\".concat(DIRECTUS_URL, \"/auth\"),\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Test connection function\nconst testConnection = async ()=>{\n    try {\n        // Test basic connection by fetching server info\n        const serverResponse = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(DIRECTUS_URL, \"/server/info\"));\n        console.log(\"Server info:\", serverResponse.data);\n        // Test actual collections with public token\n        const testResults = {\n            server: serverResponse.data,\n            collections: {}\n        };\n        // Test each collection\n        const collectionsToTest = [\n            \"Posts\",\n            \"Categories\",\n            \"Events\",\n            \"Event_Categories\",\n            \"Banner_Slider\",\n            \"Features\",\n            \"Testimonials\",\n            \"Social_media\"\n        ];\n        for (const collection of collectionsToTest){\n            try {\n                var _response_data_data;\n                const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(DIRECTUS_URL, \"/items/\").concat(collection), {\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n                        \"Content-Type\": \"application/json\"\n                    },\n                    params: {\n                        limit: 5,\n                        fields: \"id,status,Title,Category\"\n                    }\n                });\n                testResults.collections[collection] = {\n                    success: true,\n                    count: ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.length) || 0,\n                    data: response.data.data || []\n                };\n            } catch (collectionError) {\n                testResults.collections[collection] = {\n                    success: false,\n                    error: collectionError instanceof Error ? collectionError.message : \"Unknown error\"\n                };\n            }\n        }\n        return {\n            success: true,\n            ...testResults\n        };\n    } catch (error) {\n        console.error(\"Connection test failed:\", error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        };\n    }\n};\n// Authentication API functions\nconst authAPI = {\n    // Login with Directus\n    login: async (email, password)=>{\n        try {\n            const response = await fetch(\"\".concat(DIRECTUS_URL, \"/auth/login\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            if (!response.ok) {\n                var _error_errors_, _error_errors;\n                const error = await response.json();\n                throw new Error(((_error_errors = error.errors) === null || _error_errors === void 0 ? void 0 : (_error_errors_ = _error_errors[0]) === null || _error_errors_ === void 0 ? void 0 : _error_errors_.message) || \"Login failed\");\n            }\n            const data = await response.json();\n            return data.data;\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            throw error;\n        }\n    },\n    // Register new user with Writer role\n    register: async (userData)=>{\n        try {\n            // First, get the Writer role ID\n            const rolesResponse = await fetch(\"\".concat(DIRECTUS_URL, \"/roles?filter[name][_eq]=Writer\"), {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            let writerRoleId = null;\n            if (rolesResponse.ok) {\n                var _rolesData_data_, _rolesData_data;\n                const rolesData = await rolesResponse.json();\n                writerRoleId = (_rolesData_data = rolesData.data) === null || _rolesData_data === void 0 ? void 0 : (_rolesData_data_ = _rolesData_data[0]) === null || _rolesData_data_ === void 0 ? void 0 : _rolesData_data_.id;\n            }\n            // If Writer role not found, we'll let Directus handle the default role\n            const userPayload = {\n                ...userData,\n                status: \"active\",\n                ...writerRoleId && {\n                    role: writerRoleId\n                }\n            };\n            const response = await fetch(\"\".concat(DIRECTUS_URL, \"/users\"), {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(userPayload)\n            });\n            if (!response.ok) {\n                var _error_errors_, _error_errors;\n                const error = await response.json();\n                throw new Error(((_error_errors = error.errors) === null || _error_errors === void 0 ? void 0 : (_error_errors_ = _error_errors[0]) === null || _error_errors_ === void 0 ? void 0 : _error_errors_.message) || \"Registration failed\");\n            }\n            const data = await response.json();\n            return data.data;\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            throw error;\n        }\n    },\n    // Get current user info\n    getCurrentUser: async (token)=>{\n        try {\n            const response = await fetch(\"\".concat(DIRECTUS_URL, \"/users/me\"), {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to get user info\");\n            }\n            const data = await response.json();\n            return data.data;\n        } catch (error) {\n            console.error(\"Get user error:\", error);\n            throw error;\n        }\n    },\n    // Refresh token\n    refresh: async (refreshToken)=>{\n        try {\n            const response = await fetch(\"\".concat(DIRECTUS_URL, \"/auth/refresh\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refresh_token: refreshToken\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to refresh token\");\n            }\n            const data = await response.json();\n            return data.data;\n        } catch (error) {\n            console.error(\"Refresh token error:\", error);\n            throw error;\n        }\n    },\n    // Logout\n    logout: async (refreshToken)=>{\n        try {\n            await fetch(\"\".concat(DIRECTUS_URL, \"/auth/logout\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refresh_token: refreshToken\n                })\n            });\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        // Don't throw error for logout, just log it\n        }\n    }\n};\n// API functions\nconst api = {\n    // Posts\n    getPosts: async function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, status = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"published\", categoryId = arguments.length > 3 ? arguments[3] : void 0, isAuthenticated = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : false;\n        try {\n            // Start with basic fields that should have public access\n            const params = {\n                limit,\n                offset,\n                sort: \"-date_created\",\n                \"filter[status][_eq]\": status,\n                fields: \"id,Title,Description,date_created\"\n            };\n            // Add category filter if specified (filter by category ID through M2M relationship)\n            if (categoryId) {\n                params[\"filter[Categories][id][_eq]\"] = categoryId;\n            }\n            // Filter based on authentication status and Is_Public field\n            if (!isAuthenticated) {\n                // For unauthenticated users, only show public posts (Is_Public = true)\n                params[\"filter[Is_Public][_eq]\"] = true;\n            }\n            // For authenticated users, show all posts regardless of Is_Public value\n            console.log(\"Fetching posts with params:\", params);\n            const response = await directus.get(\"/Posts\", {\n                params\n            });\n            console.log(\"Posts response:\", response);\n            return response.data;\n        } catch (error) {\n            console.error(\"Error fetching posts:\", error);\n            // Fallback to even more minimal fields\n            try {\n                var _response_data;\n                const params = {\n                    limit,\n                    offset,\n                    sort: \"-date_created\",\n                    \"filter[status][_eq]\": status,\n                    fields: \"id,Title,Description\"\n                };\n                // Add category filter for fallback too\n                if (categoryId) {\n                    params[\"filter[Categories][id][_eq]\"] = categoryId;\n                }\n                // Try without Is_Public filter first in fallback\n                console.log(\"Fallback: Fetching posts with params:\", params);\n                const response = await directus.get(\"/Posts\", {\n                    params\n                });\n                console.log(\"Fallback posts response:\", response);\n                // Filter client-side if needed for unauthenticated users\n                let posts = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data) || response.data || [];\n                if (!isAuthenticated && Array.isArray(posts)) {\n                    // Client-side filtering as last resort\n                    posts = posts.filter((post)=>post.Is_Public !== false);\n                }\n                return {\n                    data: posts\n                };\n            } catch (fallbackError) {\n                console.error(\"Fallback error:\", fallbackError);\n                return {\n                    data: []\n                }; // Return empty array instead of throwing\n            }\n        }\n    },\n    getPost: async function(id) {\n        let isAuthenticated = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _response_data;\n            const response = await directus.get(\"/Posts/\".concat(id), {\n                params: {\n                    fields: \"id,Title,Description,date_created\"\n                }\n            });\n            console.log(\"Post response:\", response);\n            const post = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data) || response.data;\n            // For now, allow access to all posts since we can't reliably check Is_Public\n            // TODO: Implement proper Is_Public checking when permissions are configured\n            return {\n                data: post\n            };\n        } catch (error) {\n            console.error(\"Error fetching post:\", error);\n            // Fallback to even more minimal fields\n            try {\n                var _response_data1;\n                const response = await directus.get(\"/Posts/\".concat(id), {\n                    params: {\n                        fields: \"id,Title,Description\"\n                    }\n                });\n                console.log(\"Fallback post response:\", response);\n                const post = ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data) || response.data;\n                return {\n                    data: post\n                };\n            } catch (fallbackError) {\n                console.error(\"Fallback error:\", fallbackError);\n                throw fallbackError;\n            }\n        }\n    },\n    createPost: async (postData)=>{\n        const response = await directus.post(\"/Posts\", postData);\n        return response.data;\n    },\n    updatePost: async (id, postData)=>{\n        const response = await directus.patch(\"/Posts/\".concat(id), postData);\n        return response.data;\n    },\n    deletePost: async (id)=>{\n        const response = await directus.delete(\"/Posts/\".concat(id));\n        return response.data;\n    },\n    // Categories\n    getCategories: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        const response = await directus.get(\"/Categories\", {\n            params: {\n                \"filter[status][_eq]\": status,\n                sort: \"Category\",\n                fields: \"*\"\n            }\n        });\n        return response.data;\n    },\n    // Categories with proper post counts based on authentication\n    getCategoriesWithCounts: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\", isAuthenticated = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _categoriesResponse_data;\n            // Get categories\n            const categoriesResponse = await directus.get(\"/Categories\", {\n                params: {\n                    \"filter[status][_eq]\": status,\n                    sort: \"Category\",\n                    fields: \"id,Category,status\"\n                }\n            });\n            console.log(\"Categories response:\", categoriesResponse);\n            // Handle different response structures\n            const categoriesData = ((_categoriesResponse_data = categoriesResponse.data) === null || _categoriesResponse_data === void 0 ? void 0 : _categoriesResponse_data.data) || categoriesResponse.data || [];\n            if (!Array.isArray(categoriesData)) {\n                console.error(\"Categories data is not an array:\", categoriesData);\n                return {\n                    data: []\n                };\n            }\n            // Get all posts first, then count by category\n            try {\n                var _allPostsResponse_data;\n                const allPostsParams = {\n                    \"filter[status][_eq]\": \"published\",\n                    fields: \"id,Categories.id\",\n                    limit: 1000\n                };\n                // Apply Is_Public filter for unauthenticated users\n                if (!isAuthenticated) {\n                    allPostsParams[\"filter[Is_Public][_eq]\"] = true;\n                }\n                console.log(\"Fetching all posts with params:\", allPostsParams);\n                const allPostsResponse = await directus.get(\"/Posts\", {\n                    params: allPostsParams\n                });\n                console.log(\"All posts response:\", allPostsResponse);\n                const allPosts = ((_allPostsResponse_data = allPostsResponse.data) === null || _allPostsResponse_data === void 0 ? void 0 : _allPostsResponse_data.data) || allPostsResponse.data || [];\n                // Count posts for each category\n                const categoriesWithCounts = categoriesData.map((category)=>{\n                    let postCount = 0;\n                    if (Array.isArray(allPosts)) {\n                        postCount = allPosts.filter((post)=>{\n                            // Check if this post belongs to this category\n                            if (post.Categories && Array.isArray(post.Categories)) {\n                                return post.Categories.some((cat)=>cat.id === category.id);\n                            } else if (post.Categories && typeof post.Categories === \"object\") {\n                                return post.Categories.id === category.id;\n                            }\n                            return false;\n                        }).length;\n                    }\n                    console.log(\"Category \".concat(category.Category, \" (ID: \").concat(category.id, \") has \").concat(postCount, \" posts\"));\n                    return {\n                        ...category,\n                        postCount: postCount\n                    };\n                });\n                return {\n                    data: categoriesWithCounts\n                };\n            } catch (postsError) {\n                console.error(\"Error fetching all posts for counting:\", postsError);\n                // Fallback: try to count posts individually for each category\n                const categoriesWithCounts = await Promise.all(categoriesData.map(async (category)=>{\n                    try {\n                        // Try different approaches for category filtering\n                        const approaches = [\n                            // Approach 1: Direct category filter\n                            {\n                                \"filter[status][_eq]\": \"published\",\n                                \"filter[Categories][id][_eq]\": category.id,\n                                fields: \"id\",\n                                limit: 1000\n                            },\n                            // Approach 2: Using junction table\n                            {\n                                \"filter[status][_eq]\": \"published\",\n                                \"filter[Categories][Categories_id][_eq]\": category.id,\n                                fields: \"id\",\n                                limit: 1000\n                            },\n                            // Approach 3: Simple filter\n                            {\n                                \"filter[status][_eq]\": \"published\",\n                                fields: \"id,Categories\",\n                                limit: 1000\n                            }\n                        ];\n                        for (const params of approaches){\n                            try {\n                                var _postsResponse_data;\n                                if (!isAuthenticated) {\n                                    params[\"filter[Is_Public][_eq]\"] = true;\n                                }\n                                console.log(\"Trying approach for category \".concat(category.id, \":\"), params);\n                                const postsResponse = await directus.get(\"/Posts\", {\n                                    params\n                                });\n                                const posts = ((_postsResponse_data = postsResponse.data) === null || _postsResponse_data === void 0 ? void 0 : _postsResponse_data.data) || postsResponse.data || [];\n                                let postCount = 0;\n                                if (Array.isArray(posts)) {\n                                    if (params.fields === \"id,Categories\") {\n                                        // Filter client-side for approach 3\n                                        postCount = posts.filter((post)=>{\n                                            if (post.Categories && Array.isArray(post.Categories)) {\n                                                return post.Categories.some((cat)=>cat.id === category.id);\n                                            } else if (post.Categories && typeof post.Categories === \"object\") {\n                                                return post.Categories.id === category.id;\n                                            }\n                                            return false;\n                                        }).length;\n                                    } else {\n                                        postCount = posts.length;\n                                    }\n                                }\n                                console.log(\"Category \".concat(category.Category, \" (approach \").concat(approaches.indexOf(params) + 1, \"): \").concat(postCount, \" posts\"));\n                                return {\n                                    ...category,\n                                    postCount: postCount\n                                };\n                            } catch (approachError) {\n                                console.warn(\"Approach \".concat(approaches.indexOf(params) + 1, \" failed for category \").concat(category.id, \":\"), approachError);\n                                continue;\n                            }\n                        }\n                        // If all approaches fail, return 0\n                        return {\n                            ...category,\n                            postCount: 0\n                        };\n                    } catch (error) {\n                        console.error(\"Error processing category \".concat(category.id, \":\"), error);\n                        return {\n                            ...category,\n                            postCount: 0\n                        };\n                    }\n                }));\n                return {\n                    data: categoriesWithCounts\n                };\n            }\n        } catch (error) {\n            console.error(\"Error fetching categories with counts:\", error);\n            // Fallback to basic categories without counts\n            try {\n                var _fallbackResponse_data;\n                const fallbackResponse = await directus.get(\"/Categories\", {\n                    params: {\n                        \"filter[status][_eq]\": status,\n                        sort: \"Category\",\n                        fields: \"id,Category,status\"\n                    }\n                });\n                const fallbackData = ((_fallbackResponse_data = fallbackResponse.data) === null || _fallbackResponse_data === void 0 ? void 0 : _fallbackResponse_data.data) || fallbackResponse.data || [];\n                const categoriesWithZeroCounts = Array.isArray(fallbackData) ? fallbackData.map((cat)=>({\n                        ...cat,\n                        postCount: 0\n                    })) : [];\n                return {\n                    data: categoriesWithZeroCounts\n                };\n            } catch (fallbackError) {\n                console.error(\"Fallback categories fetch failed:\", fallbackError);\n                return {\n                    data: []\n                };\n            }\n        }\n    },\n    createCategory: async (categoryData)=>{\n        const response = await directus.post(\"/Categories\", categoryData);\n        return response.data;\n    },\n    // Events\n    getEvents: async function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, status = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"published\";\n        const response = await directus.get(\"/Events\", {\n            params: {\n                limit,\n                offset,\n                sort: \"Start_date\",\n                \"filter[status][_eq]\": status,\n                fields: \"*,Event_Categories.Category\"\n            }\n        });\n        return response.data;\n    },\n    getEvent: async (id)=>{\n        const response = await directus.get(\"/Events/\".concat(id), {\n            params: {\n                fields: \"*,Event_Categories.Category\"\n            }\n        });\n        return response.data;\n    },\n    createEvent: async (eventData)=>{\n        const response = await directus.post(\"/Events\", eventData);\n        return response.data;\n    },\n    // Banner Sliders\n    getBannerSliders: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        try {\n            const data = await directusFetch(\"Banner_Slider\", {\n                \"filter[status][_eq]\": status,\n                \"sort\": \"id\",\n                \"fields\": \"*\"\n            });\n            console.log(\"Banner sliders response:\", data);\n            return data;\n        } catch (error) {\n            console.error(\"Error fetching banner sliders:\", error);\n            throw error;\n        }\n    },\n    // Features\n    getFeatures: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        const response = await directus.get(\"/Features\", {\n            params: {\n                \"filter[status][_eq]\": status,\n                sort: \"id\",\n                fields: \"*\"\n            }\n        });\n        return response.data;\n    },\n    // Testimonials\n    getTestimonials: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        const response = await directus.get(\"/Testimonials\", {\n            params: {\n                \"filter[status][_eq]\": status,\n                sort: \"id\",\n                fields: \"*,user.first_name,user.last_name,user.avatar\"\n            }\n        });\n        return response.data;\n    },\n    // Social Media\n    getSocialMedia: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        const response = await directus.get(\"/Social_media\", {\n            params: {\n                \"filter[status][_eq]\": status,\n                sort: \"id\",\n                fields: \"*\"\n            }\n        });\n        return response.data;\n    },\n    // Comments\n    getComments: async function(postId) {\n        let status = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"published\";\n        const params = {\n            \"filter[status][_eq]\": status,\n            sort: \"date_created\",\n            fields: \"*,user.first_name,user.last_name,user.avatar\"\n        };\n        if (postId) {\n            params[\"filter[post][_eq]\"] = postId;\n        }\n        const response = await directus.get(\"/comments\", {\n            params\n        });\n        return response.data;\n    },\n    createComment: async (commentData)=>{\n        const response = await directus.post(\"/comments\", commentData);\n        return response.data;\n    },\n    // Users\n    getUserProfile: async (id)=>{\n        const response = await directus.get(\"/directus_users/\".concat(id));\n        return response.data;\n    },\n    updateUserProfile: async (id, userData)=>{\n        const response = await directus.patch(\"/directus_users/\".concat(id), userData);\n        return response.data;\n    },\n    // Quick test function to verify all collections are accessible\n    testAllCollections: async ()=>{\n        const results = {};\n        const collections = [\n            \"Posts\",\n            \"Categories\",\n            \"Events\",\n            \"Event_Categories\",\n            \"Banner_Slider\",\n            \"Features\",\n            \"Testimonials\",\n            \"Social_media\"\n        ];\n        for (const collection of collections){\n            try {\n                var _response_data_data, _response_data_data1;\n                const response = await directus.get(\"/\".concat(collection), {\n                    params: {\n                        limit: 1\n                    }\n                });\n                results[collection] = {\n                    success: true,\n                    count: ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.length) || 0,\n                    sample: ((_response_data_data1 = response.data.data) === null || _response_data_data1 === void 0 ? void 0 : _response_data_data1[0]) || null\n                };\n            } catch (error) {\n                results[collection] = {\n                    success: false,\n                    error: error instanceof Error ? error.message : \"Unknown error\"\n                };\n            }\n        }\n        return results;\n    }\n};\n// Authenticated API functions (require user login)\nconst authenticatedAPI = {\n    // Create a new post (requires authentication)\n    createPost: async (postData)=>{\n        try {\n            const authDirectus = getAuthenticatedDirectus();\n            const response = await authDirectus.post(\"/Posts\", {\n                ...postData,\n                status: \"draft\"\n            });\n            return response.data;\n        } catch (error) {\n            console.error(\"Create post error:\", error);\n            throw error;\n        }\n    },\n    // Update user's own post\n    updatePost: async (id, postData)=>{\n        try {\n            const authDirectus = getAuthenticatedDirectus();\n            const response = await authDirectus.patch(\"/Posts/\".concat(id), postData);\n            return response.data;\n        } catch (error) {\n            console.error(\"Update post error:\", error);\n            throw error;\n        }\n    },\n    // Get user's own posts\n    getUserPosts: async function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        try {\n            const authDirectus = getAuthenticatedDirectus();\n            const response = await authDirectus.get(\"/Posts\", {\n                params: {\n                    limit,\n                    offset,\n                    sort: \"-date_created\",\n                    \"filter[user_created][_eq]\": \"$CURRENT_USER\",\n                    fields: \"*,Categories.Category\"\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error(\"Get user posts error:\", error);\n            throw error;\n        }\n    },\n    // Update user profile\n    updateProfile: async (profileData)=>{\n        try {\n            const authDirectus = getAuthenticatedDirectus();\n            const response = await authDirectus.patch(\"/users/me\", profileData);\n            return response.data;\n        } catch (error) {\n            console.error(\"Update profile error:\", error);\n            throw error;\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/directus.ts\n"));

/***/ })

});