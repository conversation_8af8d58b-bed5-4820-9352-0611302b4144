"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forum/post/[id]/page",{

/***/ "(app-pages-browser)/./app/forum/post/[id]/page.tsx":
/*!**************************************!*\
  !*** ./app/forum/post/[id]/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PostDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/header */ \"(app-pages-browser)/./components/header.tsx\");\n/* harmony import */ var _components_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/sidebar */ \"(app-pages-browser)/./components/sidebar.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _barrel_optimize_names_Heart_MessageCircle_Share2_ArrowLeft_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MessageCircle,Share2,ArrowLeft,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_MessageCircle_Share2_ArrowLeft_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MessageCircle,Share2,ArrowLeft,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_MessageCircle_Share2_ArrowLeft_Send_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MessageCircle,Share2,ArrowLeft,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_MessageCircle_Share2_ArrowLeft_Send_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MessageCircle,Share2,ArrowLeft,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_MessageCircle_Share2_ArrowLeft_Send_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MessageCircle,Share2,ArrowLeft,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var date_fns_formatDistanceToNow__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! date-fns/formatDistanceToNow */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.mjs\");\n/* harmony import */ var _lib_directus__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/directus */ \"(app-pages-browser)/./lib/directus.ts\");\n/* harmony import */ var _components_auth_provider__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/auth-provider */ \"(app-pages-browser)/./components/auth-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to generate dummy avatar with first letter\nconst generateDummyAvatar = (firstName)=>{\n    var _firstName_charAt;\n    const letter = (firstName === null || firstName === void 0 ? void 0 : (_firstName_charAt = firstName.charAt(0)) === null || _firstName_charAt === void 0 ? void 0 : _firstName_charAt.toUpperCase()) || \"U\";\n    const colors = [\n        \"bg-gradient-to-br from-orange-400 to-red-500\",\n        \"bg-gradient-to-br from-amber-400 to-orange-500\",\n        \"bg-gradient-to-br from-red-400 to-pink-500\",\n        \"bg-gradient-to-br from-yellow-400 to-amber-500\",\n        \"bg-gradient-to-br from-pink-400 to-red-500\",\n        \"bg-gradient-to-br from-purple-400 to-pink-500\",\n        \"bg-gradient-to-br from-blue-400 to-purple-500\",\n        \"bg-gradient-to-br from-green-400 to-blue-500\"\n    ];\n    const colorIndex = letter.charCodeAt(0) % colors.length;\n    const gradientClass = colors[colorIndex];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-10 h-10 rounded-full \".concat(gradientClass, \" flex items-center justify-center text-white font-bold text-sm shadow-lg\"),\n        children: letter\n    }, void 0, false, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n// Helper function to get avatar URL from Directus\nconst getAvatarUrl = (avatarId)=>{\n    const directusUrl = \"http://localhost:8055\" || 0;\n    return \"\".concat(directusUrl, \"/assets/\").concat(avatarId);\n};\nfunction PostDetailPage() {\n    var _post_Categories;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const postId = params.id;\n    const { user: authUser, isAuthenticated } = (0,_components_auth_provider__WEBPACK_IMPORTED_MODULE_12__.useAuth)();\n    const [post, setPost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newComment, setNewComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [submittingComment, setSubmittingComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchPostAndComments = async ()=>{\n            try {\n                setLoading(true);\n                // Fetch post details\n                const postResponse = await _lib_directus__WEBPACK_IMPORTED_MODULE_11__.api.getPost(parseInt(postId), isAuthenticated);\n                if (postResponse && postResponse.data) {\n                    setPost(postResponse.data);\n                }\n                // Fetch comments for this post\n                const commentsResponse = await _lib_directus__WEBPACK_IMPORTED_MODULE_11__.api.getComments(parseInt(postId));\n                if (commentsResponse && commentsResponse.data) {\n                    setComments(commentsResponse.data);\n                }\n            } catch (err) {\n                console.error(\"Error fetching post details:\", err);\n                setError(\"Failed to load post details\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (postId) {\n            fetchPostAndComments();\n        }\n    }, [\n        postId\n    ]);\n    const handleSubmitComment = async (e)=>{\n        e.preventDefault();\n        if (!newComment.trim() || !isAuthenticated) return;\n        try {\n            setSubmittingComment(true);\n            // Use authenticated API to create comment\n            const authDirectus = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/directus */ \"(app-pages-browser)/./lib/directus.ts\")).then((m)=>m.getAuthenticatedDirectus());\n            await authDirectus.post(\"/comments\", {\n                comment: newComment,\n                post: parseInt(postId),\n                user: authUser === null || authUser === void 0 ? void 0 : authUser.id,\n                status: \"published\"\n            });\n            // Refresh comments\n            const commentsResponse = await _lib_directus__WEBPACK_IMPORTED_MODULE_11__.api.getComments(parseInt(postId));\n            if (commentsResponse && commentsResponse.data) {\n                setComments(commentsResponse.data);\n            }\n            setNewComment(\"\");\n        } catch (err) {\n            console.error(\"Error submitting comment:\", err);\n            alert(\"Failed to submit comment. Please try again.\");\n        } finally{\n            setSubmittingComment(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header__WEBPACK_IMPORTED_MODULE_4__.Header, {}, void 0, false, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar__WEBPACK_IMPORTED_MODULE_5__.Sidebar, {}, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 lg:ml-64\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"container mx-auto px-4 py-6 max-w-4xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-pulse space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 bg-gray-300 rounded w-1/4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white dark:bg-gray-800 rounded-lg border p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-gray-300 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-4 bg-gray-300 rounded w-32\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 141,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-3 bg-gray-300 rounded w-24\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 142,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 bg-gray-300 rounded w-3/4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 bg-gray-300 rounded w-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 147,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 bg-gray-300 rounded w-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 148,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 bg-gray-300 rounded w-2/3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n            lineNumber: 128,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !post) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header__WEBPACK_IMPORTED_MODULE_4__.Header, {}, void 0, false, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar__WEBPACK_IMPORTED_MODULE_5__.Sidebar, {}, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 lg:ml-64\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"container mx-auto px-4 py-6 max-w-4xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-600 dark:text-red-400\",\n                                            children: error || \"Post not found\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/forum\",\n                                            className: \"mt-2 text-red-600 dark:text-red-400 underline hover:no-underline\",\n                                            children: \"Back to Forum\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n            lineNumber: 163,\n            columnNumber: 7\n        }, this);\n    }\n    const postUser = typeof post.user === \"object\" ? post.user : null;\n    const userName = postUser ? \"\".concat(postUser.first_name || \"\", \" \").concat(postUser.last_name || \"\").trim() : \"Anonymous User\";\n    const firstName = (postUser === null || postUser === void 0 ? void 0 : postUser.first_name) || \"Anonymous\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header__WEBPACK_IMPORTED_MODULE_4__.Header, {}, void 0, false, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar__WEBPACK_IMPORTED_MODULE_5__.Sidebar, {}, void 0, false, {\n                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 lg:ml-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-6 max-w-4xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/forum\",\n                                    className: \"inline-flex items-center text-muted-foreground hover:text-foreground mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MessageCircle_Share2_ArrowLeft_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Back to Forum\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                    className: \"mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        (postUser === null || postUser === void 0 ? void 0 : postUser.avatar) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_9__.Avatar, {\n                                                            className: \"h-12 w-12\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_9__.AvatarImage, {\n                                                                    src: getAvatarUrl(postUser.avatar),\n                                                                    alt: userName\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 206,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_9__.AvatarFallback, {\n                                                                    children: firstName.charAt(0).toUpperCase()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 207,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 23\n                                                        }, this) : generateDummyAvatar(firstName),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-foreground\",\n                                                                            children: userName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 214,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                            variant: \"outline\",\n                                                                            className: \"text-xs\",\n                                                                            children: \"Community Member\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 215,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 213,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mt-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: (0,date_fns_formatDistanceToNow__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(new Date(post.date_created || \"\"), {\n                                                                                addSuffix: true\n                                                                            })\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 220,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        ((_post_Categories = post.Categories) === null || _post_Categories === void 0 ? void 0 : _post_Categories[0]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"•\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 225,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                                    variant: \"secondary\",\n                                                                                    className: \"text-xs\",\n                                                                                    children: post.Categories[0].Category\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 226,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold text-foreground mb-4\",\n                                                    children: post.Title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"prose prose-gray dark:prose-invert max-w-none\",\n                                                    dangerouslySetInnerHTML: {\n                                                        __html: post.Description\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 17\n                                                }, this),\n                                                post.Tags && post.Tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2 mt-6\",\n                                                    children: post.Tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"text-xs\",\n                                                            children: [\n                                                                \"#\",\n                                                                tag\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 mt-6 pt-4 border-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MessageCircle_Share2_ArrowLeft_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 255,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 256,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MessageCircle_Share2_ArrowLeft_Send_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: comments.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 261,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MessageCircle_Share2_ArrowLeft_Send_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold\",\n                                            children: [\n                                                \"Comments (\",\n                                                comments.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                className: \"pt-6\",\n                                                children: isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleSubmitComment,\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                                                            placeholder: \"Write a comment...\",\n                                                            value: newComment,\n                                                            onChange: (e)=>setNewComment(e.target.value),\n                                                            className: \"min-h-[100px]\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-end\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                type: \"submit\",\n                                                                disabled: submittingComment || !newComment.trim(),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MessageCircle_Share2_ArrowLeft_Send_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 288,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    submittingComment ? \"Posting...\" : \"Post Comment\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground mb-4\",\n                                                            children: \"You need to be logged in to post a comment.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                            onClick: ()=>window.location.href = \"/auth-test\",\n                                                            children: \"Sign In\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this),\n                                        comments.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                className: \"pt-6 text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"No comments yet. Be the first to comment!\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: comments.map((comment)=>{\n                                                const commentUser = typeof comment.user === \"object\" ? comment.user : null;\n                                                const commentUserName = commentUser ? \"\".concat(commentUser.first_name || \"\", \" \").concat(commentUser.last_name || \"\").trim() : \"Anonymous User\";\n                                                const commentFirstName = (commentUser === null || commentUser === void 0 ? void 0 : commentUser.first_name) || \"Anonymous\";\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                        className: \"pt-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-3\",\n                                                            children: [\n                                                                (commentUser === null || commentUser === void 0 ? void 0 : commentUser.avatar) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_9__.Avatar, {\n                                                                    className: \"h-8 w-8\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_9__.AvatarImage, {\n                                                                            src: getAvatarUrl(commentUser.avatar),\n                                                                            alt: commentUserName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 324,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_9__.AvatarFallback, {\n                                                                            children: commentFirstName.charAt(0).toUpperCase()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 325,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 31\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8\",\n                                                                    children: generateDummyAvatar(commentFirstName)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium text-sm\",\n                                                                                    children: commentUserName\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 334,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: (0,date_fns_formatDistanceToNow__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(new Date(comment.date_created || \"\"), {\n                                                                                        addSuffix: true\n                                                                                    })\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 335,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 333,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: comment.comment\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 339,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 332,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, comment.id, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n        lineNumber: 187,\n        columnNumber: 5\n    }, this);\n}\n_s(PostDetailPage, \"7BlwNFsW6pKbbmy4/9L0tpLG0Ng=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _components_auth_provider__WEBPACK_IMPORTED_MODULE_12__.useAuth\n    ];\n});\n_c = PostDetailPage;\nvar _c;\n$RefreshReg$(_c, \"PostDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/forum/post/[id]/page.tsx\n"));

/***/ })

});