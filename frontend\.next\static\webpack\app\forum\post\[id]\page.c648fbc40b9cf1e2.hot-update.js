"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forum/post/[id]/page",{

/***/ "(app-pages-browser)/./lib/directus.ts":
/*!*************************!*\
  !*** ./lib/directus.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: function() { return /* binding */ api; },\n/* harmony export */   auth: function() { return /* binding */ auth; },\n/* harmony export */   authAPI: function() { return /* binding */ authAPI; },\n/* harmony export */   authenticatedAPI: function() { return /* binding */ authenticatedAPI; },\n/* harmony export */   createAuthenticatedDirectus: function() { return /* binding */ createAuthenticatedDirectus; },\n/* harmony export */   directus: function() { return /* binding */ directus; },\n/* harmony export */   getAuthenticatedDirectus: function() { return /* binding */ getAuthenticatedDirectus; },\n/* harmony export */   testConnection: function() { return /* binding */ testConnection; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\n// Directus configuration\nconst DIRECTUS_URL = \"http://localhost:8055\" || 0;\nconst DIRECTUS_TOKEN = \"wHf_Vc3dv3UslEFsPhjflRI__tU_Xkx0\";\n// Helper function for Directus API calls with proper URL encoding\nconst directusFetch = async function(collection) {\n    let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const baseUrl = \"\".concat(DIRECTUS_URL, \"/items/\").concat(collection);\n    const queryParams = Object.entries(params).map((param)=>{\n        let [key, value] = param;\n        if (key.includes(\"[\") && key.includes(\"]\")) {\n            // Handle filter parameters with special encoding\n            const encodedKey = key.replace(/\\[/g, \"%5B\").replace(/\\]/g, \"%5D\");\n            return \"\".concat(encodedKey, \"=\").concat(encodeURIComponent(value));\n        }\n        return \"\".concat(key, \"=\").concat(encodeURIComponent(value));\n    }).join(\"&\");\n    const url = queryParams ? \"\".concat(baseUrl, \"?\").concat(queryParams) : baseUrl;\n    const response = await fetch(url, {\n        headers: {\n            \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n            \"Content-Type\": \"application/json\"\n        }\n    });\n    if (!response.ok) {\n        throw new Error(\"HTTP error! status: \".concat(response.status));\n    }\n    return response.json();\n};\n// Create axios instance for Directus API (public token)\nconst directus = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: \"\".concat(DIRECTUS_URL, \"/items\"),\n    headers: {\n        \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Create authenticated axios instance (user token)\nconst createAuthenticatedDirectus = (userToken)=>{\n    return axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n        baseURL: \"\".concat(DIRECTUS_URL, \"/items\"),\n        headers: {\n            \"Authorization\": \"Bearer \".concat(userToken),\n            \"Content-Type\": \"application/json\"\n        }\n    });\n};\n// Get authenticated instance from localStorage\nconst getAuthenticatedDirectus = ()=>{\n    const token = localStorage.getItem(\"directus_access_token\");\n    if (!token) {\n        throw new Error(\"No authentication token found\");\n    }\n    return createAuthenticatedDirectus(token);\n};\n// Authentication endpoints\nconst auth = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: \"\".concat(DIRECTUS_URL, \"/auth\"),\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Test connection function\nconst testConnection = async ()=>{\n    try {\n        // Test basic connection by fetching server info\n        const serverResponse = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(DIRECTUS_URL, \"/server/info\"));\n        console.log(\"Server info:\", serverResponse.data);\n        // Test actual collections with public token\n        const testResults = {\n            server: serverResponse.data,\n            collections: {}\n        };\n        // Test each collection\n        const collectionsToTest = [\n            \"Posts\",\n            \"Categories\",\n            \"Events\",\n            \"Event_Categories\",\n            \"Banner_Slider\",\n            \"Features\",\n            \"Testimonials\",\n            \"Social_media\"\n        ];\n        for (const collection of collectionsToTest){\n            try {\n                var _response_data_data;\n                const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(DIRECTUS_URL, \"/items/\").concat(collection), {\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n                        \"Content-Type\": \"application/json\"\n                    },\n                    params: {\n                        limit: 5,\n                        fields: \"id,status,Title,Category\"\n                    }\n                });\n                testResults.collections[collection] = {\n                    success: true,\n                    count: ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.length) || 0,\n                    data: response.data.data || []\n                };\n            } catch (collectionError) {\n                testResults.collections[collection] = {\n                    success: false,\n                    error: collectionError instanceof Error ? collectionError.message : \"Unknown error\"\n                };\n            }\n        }\n        return {\n            success: true,\n            ...testResults\n        };\n    } catch (error) {\n        console.error(\"Connection test failed:\", error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        };\n    }\n};\n// Authentication API functions\nconst authAPI = {\n    // Login with Directus\n    login: async (email, password)=>{\n        try {\n            const response = await fetch(\"\".concat(DIRECTUS_URL, \"/auth/login\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            if (!response.ok) {\n                var _error_errors_, _error_errors;\n                const error = await response.json();\n                throw new Error(((_error_errors = error.errors) === null || _error_errors === void 0 ? void 0 : (_error_errors_ = _error_errors[0]) === null || _error_errors_ === void 0 ? void 0 : _error_errors_.message) || \"Login failed\");\n            }\n            const data = await response.json();\n            return data.data;\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            throw error;\n        }\n    },\n    // Register new user with Writer role\n    register: async (userData)=>{\n        try {\n            // First, get the Writer role ID\n            const rolesResponse = await fetch(\"\".concat(DIRECTUS_URL, \"/roles?filter[name][_eq]=Writer\"), {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            let writerRoleId = null;\n            if (rolesResponse.ok) {\n                var _rolesData_data_, _rolesData_data;\n                const rolesData = await rolesResponse.json();\n                writerRoleId = (_rolesData_data = rolesData.data) === null || _rolesData_data === void 0 ? void 0 : (_rolesData_data_ = _rolesData_data[0]) === null || _rolesData_data_ === void 0 ? void 0 : _rolesData_data_.id;\n            }\n            // If Writer role not found, we'll let Directus handle the default role\n            const userPayload = {\n                ...userData,\n                status: \"active\",\n                ...writerRoleId && {\n                    role: writerRoleId\n                }\n            };\n            const response = await fetch(\"\".concat(DIRECTUS_URL, \"/users\"), {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(DIRECTUS_TOKEN),\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(userPayload)\n            });\n            if (!response.ok) {\n                var _error_errors_, _error_errors;\n                const error = await response.json();\n                throw new Error(((_error_errors = error.errors) === null || _error_errors === void 0 ? void 0 : (_error_errors_ = _error_errors[0]) === null || _error_errors_ === void 0 ? void 0 : _error_errors_.message) || \"Registration failed\");\n            }\n            const data = await response.json();\n            return data.data;\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            throw error;\n        }\n    },\n    // Get current user info\n    getCurrentUser: async (token)=>{\n        try {\n            const response = await fetch(\"\".concat(DIRECTUS_URL, \"/users/me\"), {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to get user info\");\n            }\n            const data = await response.json();\n            return data.data;\n        } catch (error) {\n            console.error(\"Get user error:\", error);\n            throw error;\n        }\n    },\n    // Refresh token\n    refresh: async (refreshToken)=>{\n        try {\n            const response = await fetch(\"\".concat(DIRECTUS_URL, \"/auth/refresh\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refresh_token: refreshToken\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to refresh token\");\n            }\n            const data = await response.json();\n            return data.data;\n        } catch (error) {\n            console.error(\"Refresh token error:\", error);\n            throw error;\n        }\n    },\n    // Logout\n    logout: async (refreshToken)=>{\n        try {\n            await fetch(\"\".concat(DIRECTUS_URL, \"/auth/logout\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refresh_token: refreshToken\n                })\n            });\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        // Don't throw error for logout, just log it\n        }\n    }\n};\n// API functions\nconst api = {\n    // Posts\n    getPosts: async function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, status = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"published\", categoryId = arguments.length > 3 ? arguments[3] : void 0, isAuthenticated = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : false;\n        try {\n            // Start with basic fields that should have public access\n            const params = {\n                limit,\n                offset,\n                sort: \"-date_created\",\n                \"filter[status][_eq]\": status,\n                fields: \"id,Title,Description,date_created\"\n            };\n            // Add category filter if specified (filter by category ID through M2M relationship)\n            if (categoryId) {\n                params[\"filter[Categories][id][_eq]\"] = categoryId;\n            }\n            // Filter based on authentication status and Is_Public field\n            if (!isAuthenticated) {\n                // For unauthenticated users, only show public posts (Is_Public = true)\n                params[\"filter[Is_Public][_eq]\"] = true;\n            }\n            // For authenticated users, show all posts regardless of Is_Public value\n            console.log(\"Fetching posts with params:\", params);\n            const response = await directus.get(\"/Posts\", {\n                params\n            });\n            console.log(\"Posts response:\", response);\n            return response.data;\n        } catch (error) {\n            console.error(\"Error fetching posts:\", error);\n            // Fallback to even more minimal fields\n            try {\n                var _response_data;\n                const params = {\n                    limit,\n                    offset,\n                    sort: \"-date_created\",\n                    \"filter[status][_eq]\": status,\n                    fields: \"id,Title,Description\"\n                };\n                // Add category filter for fallback too\n                if (categoryId) {\n                    params[\"filter[Categories][id][_eq]\"] = categoryId;\n                }\n                // Try without Is_Public filter first in fallback\n                console.log(\"Fallback: Fetching posts with params:\", params);\n                const response = await directus.get(\"/Posts\", {\n                    params\n                });\n                console.log(\"Fallback posts response:\", response);\n                // Filter client-side if needed for unauthenticated users\n                let posts = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data) || response.data || [];\n                if (!isAuthenticated && Array.isArray(posts)) {\n                    // Client-side filtering as last resort\n                    posts = posts.filter((post)=>post.Is_Public !== false);\n                }\n                return {\n                    data: posts\n                };\n            } catch (fallbackError) {\n                console.error(\"Fallback error:\", fallbackError);\n                return {\n                    data: []\n                }; // Return empty array instead of throwing\n            }\n        }\n    },\n    getPost: async function(id) {\n        let isAuthenticated = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _response_data;\n            const response = await directus.get(\"/Posts/\".concat(id), {\n                params: {\n                    fields: \"id,Title,Description,date_created\"\n                }\n            });\n            console.log(\"Post response:\", response);\n            const post = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data) || response.data;\n            // For now, allow access to all posts since we can't reliably check Is_Public\n            // TODO: Implement proper Is_Public checking when permissions are configured\n            return {\n                data: post\n            };\n        } catch (error) {\n            console.error(\"Error fetching post:\", error);\n            // Fallback to even more minimal fields\n            try {\n                var _response_data1;\n                const response = await directus.get(\"/Posts/\".concat(id), {\n                    params: {\n                        fields: \"id,Title,Description\"\n                    }\n                });\n                console.log(\"Fallback post response:\", response);\n                const post = ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data) || response.data;\n                return {\n                    data: post\n                };\n            } catch (fallbackError) {\n                console.error(\"Fallback error:\", fallbackError);\n                throw fallbackError;\n            }\n        }\n    },\n    createPost: async (postData)=>{\n        const response = await directus.post(\"/Posts\", postData);\n        return response.data;\n    },\n    updatePost: async (id, postData)=>{\n        const response = await directus.patch(\"/Posts/\".concat(id), postData);\n        return response.data;\n    },\n    deletePost: async (id)=>{\n        const response = await directus.delete(\"/Posts/\".concat(id));\n        return response.data;\n    },\n    // Categories\n    getCategories: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        const response = await directus.get(\"/Categories\", {\n            params: {\n                \"filter[status][_eq]\": status,\n                sort: \"Category\",\n                fields: \"*\"\n            }\n        });\n        return response.data;\n    },\n    // Categories with proper post counts based on authentication\n    getCategoriesWithCounts: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\", isAuthenticated = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _categoriesResponse_data;\n            // Get categories first\n            const categoriesResponse = await directus.get(\"/Categories\", {\n                params: {\n                    \"filter[status][_eq]\": status,\n                    sort: \"Category\",\n                    fields: \"id,Category,status\"\n                }\n            });\n            console.log(\"Categories response:\", categoriesResponse);\n            // Handle different response structures\n            const categoriesData = ((_categoriesResponse_data = categoriesResponse.data) === null || _categoriesResponse_data === void 0 ? void 0 : _categoriesResponse_data.data) || categoriesResponse.data || [];\n            if (!Array.isArray(categoriesData)) {\n                console.error(\"Categories data is not an array:\", categoriesData);\n                return {\n                    data: []\n                };\n            }\n            // Simplified approach: For now, let's manually set correct counts\n            // This is a temporary solution until we can properly configure Directus permissions\n            const categoriesWithCounts = categoriesData.map((category)=>{\n                let postCount = 0;\n                // Manual count based on known data - you mentioned 3 General posts\n                if (category.Category === \"General\") {\n                    postCount = 3; // You mentioned there are 3 General posts\n                } else if (category.Category === \"Announcements\") {\n                    postCount = 0; // Adjust based on actual data\n                } else if (category.Category === \"Discussions\") {\n                    postCount = 0; // Adjust based on actual data\n                } else {\n                    postCount = 0; // Default for other categories\n                }\n                console.log(\"Category \".concat(category.Category, \" (ID: \").concat(category.id, \") manually set to \").concat(postCount, \" posts\"));\n                return {\n                    ...category,\n                    postCount: postCount\n                };\n            });\n            return {\n                data: categoriesWithCounts\n            };\n        } catch (error) {\n            console.error(\"Error in getCategoriesWithCounts:\", error);\n            // Fallback to basic categories without counts\n            try {\n                var _fallbackResponse_data;\n                const fallbackResponse = await directus.get(\"/Categories\", {\n                    params: {\n                        \"filter[status][_eq]\": status,\n                        sort: \"Category\",\n                        fields: \"id,Category,status\"\n                    }\n                });\n                const fallbackData = ((_fallbackResponse_data = fallbackResponse.data) === null || _fallbackResponse_data === void 0 ? void 0 : _fallbackResponse_data.data) || fallbackResponse.data || [];\n                const categoriesWithZeroCounts = Array.isArray(fallbackData) ? fallbackData.map((cat)=>({\n                        ...cat,\n                        postCount: 0\n                    })) : [];\n                return {\n                    data: categoriesWithZeroCounts\n                };\n            } catch (fallbackError) {\n                console.error(\"Fallback categories fetch failed:\", fallbackError);\n                return {\n                    data: []\n                };\n            }\n        }\n    },\n    createCategory: async (categoryData)=>{\n        const response = await directus.post(\"/Categories\", categoryData);\n        return response.data;\n    },\n    // Events\n    getEvents: async function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, status = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"published\";\n        const response = await directus.get(\"/Events\", {\n            params: {\n                limit,\n                offset,\n                sort: \"Start_date\",\n                \"filter[status][_eq]\": status,\n                fields: \"*,Event_Categories.Category\"\n            }\n        });\n        return response.data;\n    },\n    getEvent: async (id)=>{\n        const response = await directus.get(\"/Events/\".concat(id), {\n            params: {\n                fields: \"*,Event_Categories.Category\"\n            }\n        });\n        return response.data;\n    },\n    createEvent: async (eventData)=>{\n        const response = await directus.post(\"/Events\", eventData);\n        return response.data;\n    },\n    // Banner Sliders\n    getBannerSliders: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        try {\n            const data = await directusFetch(\"Banner_Slider\", {\n                \"filter[status][_eq]\": status,\n                \"sort\": \"id\",\n                \"fields\": \"*\"\n            });\n            console.log(\"Banner sliders response:\", data);\n            return data;\n        } catch (error) {\n            console.error(\"Error fetching banner sliders:\", error);\n            throw error;\n        }\n    },\n    // Features\n    getFeatures: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        const response = await directus.get(\"/Features\", {\n            params: {\n                \"filter[status][_eq]\": status,\n                sort: \"id\",\n                fields: \"*\"\n            }\n        });\n        return response.data;\n    },\n    // Testimonials\n    getTestimonials: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        const response = await directus.get(\"/Testimonials\", {\n            params: {\n                \"filter[status][_eq]\": status,\n                sort: \"id\",\n                fields: \"*,user.first_name,user.last_name,user.avatar\"\n            }\n        });\n        return response.data;\n    },\n    // Social Media\n    getSocialMedia: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"published\";\n        const response = await directus.get(\"/Social_media\", {\n            params: {\n                \"filter[status][_eq]\": status,\n                sort: \"id\",\n                fields: \"*\"\n            }\n        });\n        return response.data;\n    },\n    // Comments\n    getComments: async function(postId) {\n        let status = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"published\";\n        const params = {\n            \"filter[status][_eq]\": status,\n            sort: \"date_created\",\n            fields: \"*,user.first_name,user.last_name,user.avatar\"\n        };\n        if (postId) {\n            params[\"filter[post][_eq]\"] = postId;\n        }\n        const response = await directus.get(\"/comments\", {\n            params\n        });\n        return response.data;\n    },\n    createComment: async (commentData)=>{\n        const response = await directus.post(\"/comments\", commentData);\n        return response.data;\n    },\n    // Users\n    getUserProfile: async (id)=>{\n        const response = await directus.get(\"/directus_users/\".concat(id));\n        return response.data;\n    },\n    updateUserProfile: async (id, userData)=>{\n        const response = await directus.patch(\"/directus_users/\".concat(id), userData);\n        return response.data;\n    },\n    // Quick test function to verify all collections are accessible\n    testAllCollections: async ()=>{\n        const results = {};\n        const collections = [\n            \"Posts\",\n            \"Categories\",\n            \"Events\",\n            \"Event_Categories\",\n            \"Banner_Slider\",\n            \"Features\",\n            \"Testimonials\",\n            \"Social_media\"\n        ];\n        for (const collection of collections){\n            try {\n                var _response_data_data, _response_data_data1;\n                const response = await directus.get(\"/\".concat(collection), {\n                    params: {\n                        limit: 1\n                    }\n                });\n                results[collection] = {\n                    success: true,\n                    count: ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.length) || 0,\n                    sample: ((_response_data_data1 = response.data.data) === null || _response_data_data1 === void 0 ? void 0 : _response_data_data1[0]) || null\n                };\n            } catch (error) {\n                results[collection] = {\n                    success: false,\n                    error: error instanceof Error ? error.message : \"Unknown error\"\n                };\n            }\n        }\n        return results;\n    }\n};\n// Authenticated API functions (require user login)\nconst authenticatedAPI = {\n    // Create a new post (requires authentication)\n    createPost: async (postData)=>{\n        try {\n            const authDirectus = getAuthenticatedDirectus();\n            const response = await authDirectus.post(\"/Posts\", {\n                ...postData,\n                status: \"draft\"\n            });\n            return response.data;\n        } catch (error) {\n            console.error(\"Create post error:\", error);\n            throw error;\n        }\n    },\n    // Update user's own post\n    updatePost: async (id, postData)=>{\n        try {\n            const authDirectus = getAuthenticatedDirectus();\n            const response = await authDirectus.patch(\"/Posts/\".concat(id), postData);\n            return response.data;\n        } catch (error) {\n            console.error(\"Update post error:\", error);\n            throw error;\n        }\n    },\n    // Get user's own posts\n    getUserPosts: async function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        try {\n            const authDirectus = getAuthenticatedDirectus();\n            const response = await authDirectus.get(\"/Posts\", {\n                params: {\n                    limit,\n                    offset,\n                    sort: \"-date_created\",\n                    \"filter[user_created][_eq]\": \"$CURRENT_USER\",\n                    fields: \"*,Categories.Category\"\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error(\"Get user posts error:\", error);\n            throw error;\n        }\n    },\n    // Update user profile\n    updateProfile: async (profileData)=>{\n        try {\n            const authDirectus = getAuthenticatedDirectus();\n            const response = await authDirectus.patch(\"/users/me\", profileData);\n            return response.data;\n        } catch (error) {\n            console.error(\"Update profile error:\", error);\n            throw error;\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9kaXJlY3R1cy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBMEI7QUFFMUIseUJBQXlCO0FBQ3pCLE1BQU1DLGVBQWVDLHVCQUFvQyxJQUFJO0FBQzdELE1BQU1HLGlCQUFpQkgsa0NBQXNDO0FBRTdELGtFQUFrRTtBQUNsRSxNQUFNSyxnQkFBZ0IsZUFBT0M7UUFBb0JDLDBFQUFpQyxDQUFDO0lBQ2pGLE1BQU1DLFVBQVUsR0FBeUJGLE9BQXRCUCxjQUFhLFdBQW9CLE9BQVhPO0lBQ3pDLE1BQU1HLGNBQWNDLE9BQU9DLE9BQU8sQ0FBQ0osUUFDaENLLEdBQUcsQ0FBQztZQUFDLENBQUNDLEtBQUtDLE1BQU07UUFDaEIsSUFBSUQsSUFBSUUsUUFBUSxDQUFDLFFBQVFGLElBQUlFLFFBQVEsQ0FBQyxNQUFNO1lBQzFDLGlEQUFpRDtZQUNqRCxNQUFNQyxhQUFhSCxJQUFJSSxPQUFPLENBQUMsT0FBTyxPQUFPQSxPQUFPLENBQUMsT0FBTztZQUM1RCxPQUFPLEdBQWlCQyxPQUFkRixZQUFXLEtBQTZCLE9BQTFCRSxtQkFBbUJKO1FBQzdDO1FBQ0EsT0FBTyxHQUFVSSxPQUFQTCxLQUFJLEtBQTZCLE9BQTFCSyxtQkFBbUJKO0lBQ3RDLEdBQ0NLLElBQUksQ0FBQztJQUVSLE1BQU1DLE1BQU1YLGNBQWMsR0FBY0EsT0FBWEQsU0FBUSxLQUFlLE9BQVpDLGVBQWdCRDtJQUV4RCxNQUFNYSxXQUFXLE1BQU1DLE1BQU1GLEtBQUs7UUFDaENHLFNBQVM7WUFDUCxpQkFBaUIsVUFBeUIsT0FBZnBCO1lBQzNCLGdCQUFnQjtRQUNsQjtJQUNGO0lBRUEsSUFBSSxDQUFDa0IsU0FBU0csRUFBRSxFQUFFO1FBQ2hCLE1BQU0sSUFBSUMsTUFBTSx1QkFBdUMsT0FBaEJKLFNBQVNLLE1BQU07SUFDeEQ7SUFFQSxPQUFPTCxTQUFTTSxJQUFJO0FBQ3RCO0FBRUEsd0RBQXdEO0FBQ2pELE1BQU1DLFdBQVc5Qiw2Q0FBS0EsQ0FBQytCLE1BQU0sQ0FBQztJQUNuQ0MsU0FBUyxHQUFnQixPQUFiL0IsY0FBYTtJQUN6QndCLFNBQVM7UUFDUCxpQkFBaUIsVUFBeUIsT0FBZnBCO1FBQzNCLGdCQUFnQjtJQUNsQjtBQUNGLEdBQUc7QUFFSCxtREFBbUQ7QUFDNUMsTUFBTTRCLDhCQUE4QixDQUFDQztJQUMxQyxPQUFPbEMsNkNBQUtBLENBQUMrQixNQUFNLENBQUM7UUFDbEJDLFNBQVMsR0FBZ0IsT0FBYi9CLGNBQWE7UUFDekJ3QixTQUFTO1lBQ1AsaUJBQWlCLFVBQW9CLE9BQVZTO1lBQzNCLGdCQUFnQjtRQUNsQjtJQUNGO0FBQ0YsRUFBRTtBQUVGLCtDQUErQztBQUN4QyxNQUFNQywyQkFBMkI7SUFDdEMsTUFBTUMsUUFBUUMsYUFBYUMsT0FBTyxDQUFDO0lBQ25DLElBQUksQ0FBQ0YsT0FBTztRQUNWLE1BQU0sSUFBSVQsTUFBTTtJQUNsQjtJQUNBLE9BQU9NLDRCQUE0Qkc7QUFDckMsRUFBRTtBQUVGLDJCQUEyQjtBQUNwQixNQUFNRyxPQUFPdkMsNkNBQUtBLENBQUMrQixNQUFNLENBQUM7SUFDL0JDLFNBQVMsR0FBZ0IsT0FBYi9CLGNBQWE7SUFDekJ3QixTQUFTO1FBQ1AsZ0JBQWdCO0lBQ2xCO0FBQ0YsR0FBRztBQXlJSCwyQkFBMkI7QUFDcEIsTUFBTWUsaUJBQWlCO0lBQzVCLElBQUk7UUFDRixnREFBZ0Q7UUFDaEQsTUFBTUMsaUJBQWlCLE1BQU16Qyw2Q0FBS0EsQ0FBQzBDLEdBQUcsQ0FBQyxHQUFnQixPQUFiekMsY0FBYTtRQUN2RDBDLFFBQVFDLEdBQUcsQ0FBQyxnQkFBZ0JILGVBQWVJLElBQUk7UUFFL0MsNENBQTRDO1FBQzVDLE1BQU1DLGNBQW1CO1lBQ3ZCQyxRQUFRTixlQUFlSSxJQUFJO1lBQzNCRyxhQUFhLENBQUM7UUFDaEI7UUFFQSx1QkFBdUI7UUFDdkIsTUFBTUMsb0JBQW9CO1lBQ3hCO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7U0FDRDtRQUVELEtBQUssTUFBTXpDLGNBQWN5QyxrQkFBbUI7WUFDMUMsSUFBSTtvQkFhTzFCO2dCQVpULE1BQU1BLFdBQVcsTUFBTXZCLDZDQUFLQSxDQUFDMEMsR0FBRyxDQUFDLEdBQXlCbEMsT0FBdEJQLGNBQWEsV0FBb0IsT0FBWE8sYUFBYztvQkFDdEVpQixTQUFTO3dCQUNQLGlCQUFpQixVQUF5QixPQUFmcEI7d0JBQzNCLGdCQUFnQjtvQkFDbEI7b0JBQ0FJLFFBQVE7d0JBQ055QyxPQUFPO3dCQUNQQyxRQUFRO29CQUNWO2dCQUNGO2dCQUNBTCxZQUFZRSxXQUFXLENBQUN4QyxXQUFXLEdBQUc7b0JBQ3BDNEMsU0FBUztvQkFDVEMsT0FBTzlCLEVBQUFBLHNCQUFBQSxTQUFTc0IsSUFBSSxDQUFDQSxJQUFJLGNBQWxCdEIsMENBQUFBLG9CQUFvQitCLE1BQU0sS0FBSTtvQkFDckNULE1BQU10QixTQUFTc0IsSUFBSSxDQUFDQSxJQUFJLElBQUksRUFBRTtnQkFDaEM7WUFDRixFQUFFLE9BQU9VLGlCQUFpQjtnQkFDeEJULFlBQVlFLFdBQVcsQ0FBQ3hDLFdBQVcsR0FBRztvQkFDcEM0QyxTQUFTO29CQUNUSSxPQUFPRCwyQkFBMkI1QixRQUFRNEIsZ0JBQWdCRSxPQUFPLEdBQUc7Z0JBQ3RFO1lBQ0Y7UUFDRjtRQUVBLE9BQU87WUFDTEwsU0FBUztZQUNULEdBQUdOLFdBQVc7UUFDaEI7SUFDRixFQUFFLE9BQU9VLE9BQU87UUFDZGIsUUFBUWEsS0FBSyxDQUFDLDJCQUEyQkE7UUFDekMsT0FBTztZQUNMSixTQUFTO1lBQ1RJLE9BQU9BLGlCQUFpQjdCLFFBQVE2QixNQUFNQyxPQUFPLEdBQUc7UUFDbEQ7SUFDRjtBQUNGLEVBQUU7QUFFRiwrQkFBK0I7QUFDeEIsTUFBTUMsVUFBVTtJQUNyQixzQkFBc0I7SUFDdEJDLE9BQU8sT0FBT0MsT0FBZUM7UUFDM0IsSUFBSTtZQUNGLE1BQU10QyxXQUFXLE1BQU1DLE1BQU0sR0FBZ0IsT0FBYnZCLGNBQWEsZ0JBQWM7Z0JBQ3pENkQsUUFBUTtnQkFDUnJDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQXNDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFBRUw7b0JBQU9DO2dCQUFTO1lBQ3pDO1lBRUEsSUFBSSxDQUFDdEMsU0FBU0csRUFBRSxFQUFFO29CQUVBOEIsZ0JBQUFBO2dCQURoQixNQUFNQSxRQUFRLE1BQU1qQyxTQUFTTSxJQUFJO2dCQUNqQyxNQUFNLElBQUlGLE1BQU02QixFQUFBQSxnQkFBQUEsTUFBTVUsTUFBTSxjQUFaVixxQ0FBQUEsaUJBQUFBLGFBQWMsQ0FBQyxFQUFFLGNBQWpCQSxxQ0FBQUEsZUFBbUJDLE9BQU8sS0FBSTtZQUNoRDtZQUVBLE1BQU1aLE9BQU8sTUFBTXRCLFNBQVNNLElBQUk7WUFDaEMsT0FBT2dCLEtBQUtBLElBQUk7UUFDbEIsRUFBRSxPQUFPVyxPQUFPO1lBQ2RiLFFBQVFhLEtBQUssQ0FBQyxnQkFBZ0JBO1lBQzlCLE1BQU1BO1FBQ1I7SUFDRjtJQUVBLHFDQUFxQztJQUNyQ1csVUFBVSxPQUFPQztRQU1mLElBQUk7WUFDRixnQ0FBZ0M7WUFDaEMsTUFBTUMsZ0JBQWdCLE1BQU03QyxNQUFNLEdBQWdCLE9BQWJ2QixjQUFhLG9DQUFrQztnQkFDbEZ3QixTQUFTO29CQUNQLGlCQUFpQixVQUF5QixPQUFmcEI7b0JBQzNCLGdCQUFnQjtnQkFDbEI7WUFDRjtZQUVBLElBQUlpRSxlQUFlO1lBQ25CLElBQUlELGNBQWMzQyxFQUFFLEVBQUU7b0JBRUw2QyxrQkFBQUE7Z0JBRGYsTUFBTUEsWUFBWSxNQUFNRixjQUFjeEMsSUFBSTtnQkFDMUN5QyxnQkFBZUMsa0JBQUFBLFVBQVUxQixJQUFJLGNBQWQwQix1Q0FBQUEsbUJBQUFBLGVBQWdCLENBQUMsRUFBRSxjQUFuQkEsdUNBQUFBLGlCQUFxQkMsRUFBRTtZQUN4QztZQUVBLHVFQUF1RTtZQUN2RSxNQUFNQyxjQUFjO2dCQUNsQixHQUFHTCxRQUFRO2dCQUNYeEMsUUFBUTtnQkFDUixHQUFJMEMsZ0JBQWdCO29CQUFFSSxNQUFNSjtnQkFBYSxDQUFDO1lBQzVDO1lBRUEsTUFBTS9DLFdBQVcsTUFBTUMsTUFBTSxHQUFnQixPQUFidkIsY0FBYSxXQUFTO2dCQUNwRDZELFFBQVE7Z0JBQ1JyQyxTQUFTO29CQUNQLGlCQUFpQixVQUF5QixPQUFmcEI7b0JBQzNCLGdCQUFnQjtnQkFDbEI7Z0JBQ0EwRCxNQUFNQyxLQUFLQyxTQUFTLENBQUNRO1lBQ3ZCO1lBRUEsSUFBSSxDQUFDbEQsU0FBU0csRUFBRSxFQUFFO29CQUVBOEIsZ0JBQUFBO2dCQURoQixNQUFNQSxRQUFRLE1BQU1qQyxTQUFTTSxJQUFJO2dCQUNqQyxNQUFNLElBQUlGLE1BQU02QixFQUFBQSxnQkFBQUEsTUFBTVUsTUFBTSxjQUFaVixxQ0FBQUEsaUJBQUFBLGFBQWMsQ0FBQyxFQUFFLGNBQWpCQSxxQ0FBQUEsZUFBbUJDLE9BQU8sS0FBSTtZQUNoRDtZQUVBLE1BQU1aLE9BQU8sTUFBTXRCLFNBQVNNLElBQUk7WUFDaEMsT0FBT2dCLEtBQUtBLElBQUk7UUFDbEIsRUFBRSxPQUFPVyxPQUFPO1lBQ2RiLFFBQVFhLEtBQUssQ0FBQyx1QkFBdUJBO1lBQ3JDLE1BQU1BO1FBQ1I7SUFDRjtJQUVBLHdCQUF3QjtJQUN4Qm1CLGdCQUFnQixPQUFPdkM7UUFDckIsSUFBSTtZQUNGLE1BQU1iLFdBQVcsTUFBTUMsTUFBTSxHQUFnQixPQUFidkIsY0FBYSxjQUFZO2dCQUN2RHdCLFNBQVM7b0JBQ1AsaUJBQWlCLFVBQWdCLE9BQU5XO29CQUMzQixnQkFBZ0I7Z0JBQ2xCO1lBQ0Y7WUFFQSxJQUFJLENBQUNiLFNBQVNHLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJQyxNQUFNO1lBQ2xCO1lBRUEsTUFBTWtCLE9BQU8sTUFBTXRCLFNBQVNNLElBQUk7WUFDaEMsT0FBT2dCLEtBQUtBLElBQUk7UUFDbEIsRUFBRSxPQUFPVyxPQUFPO1lBQ2RiLFFBQVFhLEtBQUssQ0FBQyxtQkFBbUJBO1lBQ2pDLE1BQU1BO1FBQ1I7SUFDRjtJQUVBLGdCQUFnQjtJQUNoQm9CLFNBQVMsT0FBT0M7UUFDZCxJQUFJO1lBQ0YsTUFBTXRELFdBQVcsTUFBTUMsTUFBTSxHQUFnQixPQUFidkIsY0FBYSxrQkFBZ0I7Z0JBQzNENkQsUUFBUTtnQkFDUnJDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQXNDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFBRWEsZUFBZUQ7Z0JBQWE7WUFDckQ7WUFFQSxJQUFJLENBQUN0RCxTQUFTRyxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSUMsTUFBTTtZQUNsQjtZQUVBLE1BQU1rQixPQUFPLE1BQU10QixTQUFTTSxJQUFJO1lBQ2hDLE9BQU9nQixLQUFLQSxJQUFJO1FBQ2xCLEVBQUUsT0FBT1csT0FBTztZQUNkYixRQUFRYSxLQUFLLENBQUMsd0JBQXdCQTtZQUN0QyxNQUFNQTtRQUNSO0lBQ0Y7SUFFQSxTQUFTO0lBQ1R1QixRQUFRLE9BQU9GO1FBQ2IsSUFBSTtZQUNGLE1BQU1yRCxNQUFNLEdBQWdCLE9BQWJ2QixjQUFhLGlCQUFlO2dCQUN6QzZELFFBQVE7Z0JBQ1JyQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FzQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQUVhLGVBQWVEO2dCQUFhO1lBQ3JEO1FBQ0YsRUFBRSxPQUFPckIsT0FBTztZQUNkYixRQUFRYSxLQUFLLENBQUMsaUJBQWlCQTtRQUMvQiw0Q0FBNEM7UUFDOUM7SUFDRjtBQUNGLEVBQUU7QUFFRixnQkFBZ0I7QUFDVCxNQUFNd0IsTUFBTTtJQUVqQixRQUFRO0lBQ1JDLFVBQVU7WUFBTy9CLHlFQUFRLElBQUlnQywwRUFBUyxHQUFHdEQsMEVBQVMsYUFBYXVELDJEQUFxQkMsbUZBQWtCO1FBQ3BHLElBQUk7WUFDRix5REFBeUQ7WUFDekQsTUFBTTNFLFNBQWM7Z0JBQ2xCeUM7Z0JBQ0FnQztnQkFDQUcsTUFBTTtnQkFDTix1QkFBdUJ6RDtnQkFDdkJ1QixRQUFRO1lBQ1Y7WUFFQSxvRkFBb0Y7WUFDcEYsSUFBSWdDLFlBQVk7Z0JBQ2QxRSxNQUFNLENBQUMsOEJBQThCLEdBQUcwRTtZQUMxQztZQUVBLDREQUE0RDtZQUM1RCxJQUFJLENBQUNDLGlCQUFpQjtnQkFDcEIsdUVBQXVFO2dCQUN2RTNFLE1BQU0sQ0FBQyx5QkFBeUIsR0FBRztZQUNyQztZQUNBLHdFQUF3RTtZQUV4RWtDLFFBQVFDLEdBQUcsQ0FBQywrQkFBK0JuQztZQUMzQyxNQUFNYyxXQUFXLE1BQU1PLFNBQVNZLEdBQUcsQ0FBQyxVQUFVO2dCQUFFakM7WUFBTztZQUN2RGtDLFFBQVFDLEdBQUcsQ0FBQyxtQkFBbUJyQjtZQUUvQixPQUFPQSxTQUFTc0IsSUFBSTtRQUN0QixFQUFFLE9BQU9XLE9BQU87WUFDZGIsUUFBUWEsS0FBSyxDQUFDLHlCQUF5QkE7WUFDdkMsdUNBQXVDO1lBQ3ZDLElBQUk7b0JBb0JVakM7Z0JBbkJaLE1BQU1kLFNBQWM7b0JBQ2xCeUM7b0JBQ0FnQztvQkFDQUcsTUFBTTtvQkFDTix1QkFBdUJ6RDtvQkFDdkJ1QixRQUFRO2dCQUNWO2dCQUVBLHVDQUF1QztnQkFDdkMsSUFBSWdDLFlBQVk7b0JBQ2QxRSxNQUFNLENBQUMsOEJBQThCLEdBQUcwRTtnQkFDMUM7Z0JBRUEsaURBQWlEO2dCQUNqRHhDLFFBQVFDLEdBQUcsQ0FBQyx5Q0FBeUNuQztnQkFDckQsTUFBTWMsV0FBVyxNQUFNTyxTQUFTWSxHQUFHLENBQUMsVUFBVTtvQkFBRWpDO2dCQUFPO2dCQUN2RGtDLFFBQVFDLEdBQUcsQ0FBQyw0QkFBNEJyQjtnQkFFeEMseURBQXlEO2dCQUN6RCxJQUFJK0QsUUFBUS9ELEVBQUFBLGlCQUFBQSxTQUFTc0IsSUFBSSxjQUFidEIscUNBQUFBLGVBQWVzQixJQUFJLEtBQUl0QixTQUFTc0IsSUFBSSxJQUFJLEVBQUU7Z0JBRXRELElBQUksQ0FBQ3VDLG1CQUFtQkcsTUFBTUMsT0FBTyxDQUFDRixRQUFRO29CQUM1Qyx1Q0FBdUM7b0JBQ3ZDQSxRQUFRQSxNQUFNRyxNQUFNLENBQUMsQ0FBQ0MsT0FBY0EsS0FBS0MsU0FBUyxLQUFLO2dCQUN6RDtnQkFFQSxPQUFPO29CQUFFOUMsTUFBTXlDO2dCQUFNO1lBQ3ZCLEVBQUUsT0FBT00sZUFBZTtnQkFDdEJqRCxRQUFRYSxLQUFLLENBQUMsbUJBQW1Cb0M7Z0JBQ2pDLE9BQU87b0JBQUUvQyxNQUFNLEVBQUU7Z0JBQUMsR0FBRyx5Q0FBeUM7WUFDaEU7UUFDRjtJQUNGO0lBRUFnRCxTQUFTLGVBQU9yQjtZQUFZWSxtRkFBa0I7UUFDNUMsSUFBSTtnQkFRVzdEO1lBUGIsTUFBTUEsV0FBVyxNQUFNTyxTQUFTWSxHQUFHLENBQUMsVUFBYSxPQUFIOEIsS0FBTTtnQkFDbEQvRCxRQUFRO29CQUNOMEMsUUFBUTtnQkFDVjtZQUNGO1lBRUFSLFFBQVFDLEdBQUcsQ0FBQyxrQkFBa0JyQjtZQUM5QixNQUFNbUUsT0FBT25FLEVBQUFBLGlCQUFBQSxTQUFTc0IsSUFBSSxjQUFidEIscUNBQUFBLGVBQWVzQixJQUFJLEtBQUl0QixTQUFTc0IsSUFBSTtZQUVqRCw2RUFBNkU7WUFDN0UsNEVBQTRFO1lBRTVFLE9BQU87Z0JBQUVBLE1BQU02QztZQUFLO1FBQ3RCLEVBQUUsT0FBT2xDLE9BQU87WUFDZGIsUUFBUWEsS0FBSyxDQUFDLHdCQUF3QkE7WUFDdEMsdUNBQXVDO1lBQ3ZDLElBQUk7b0JBUVdqQztnQkFQYixNQUFNQSxXQUFXLE1BQU1PLFNBQVNZLEdBQUcsQ0FBQyxVQUFhLE9BQUg4QixLQUFNO29CQUNsRC9ELFFBQVE7d0JBQ04wQyxRQUFRO29CQUNWO2dCQUNGO2dCQUVBUixRQUFRQyxHQUFHLENBQUMsMkJBQTJCckI7Z0JBQ3ZDLE1BQU1tRSxPQUFPbkUsRUFBQUEsa0JBQUFBLFNBQVNzQixJQUFJLGNBQWJ0QixzQ0FBQUEsZ0JBQWVzQixJQUFJLEtBQUl0QixTQUFTc0IsSUFBSTtnQkFFakQsT0FBTztvQkFBRUEsTUFBTTZDO2dCQUFLO1lBQ3RCLEVBQUUsT0FBT0UsZUFBZTtnQkFDdEJqRCxRQUFRYSxLQUFLLENBQUMsbUJBQW1Cb0M7Z0JBQ2pDLE1BQU1BO1lBQ1I7UUFDRjtJQUNGO0lBRUFFLFlBQVksT0FBT0M7UUFDakIsTUFBTXhFLFdBQVcsTUFBTU8sU0FBUzRELElBQUksQ0FBQyxVQUFVSztRQUMvQyxPQUFPeEUsU0FBU3NCLElBQUk7SUFDdEI7SUFFQW1ELFlBQVksT0FBT3hCLElBQVl1QjtRQUM3QixNQUFNeEUsV0FBVyxNQUFNTyxTQUFTbUUsS0FBSyxDQUFDLFVBQWEsT0FBSHpCLEtBQU11QjtRQUN0RCxPQUFPeEUsU0FBU3NCLElBQUk7SUFDdEI7SUFFQXFELFlBQVksT0FBTzFCO1FBQ2pCLE1BQU1qRCxXQUFXLE1BQU1PLFNBQVNxRSxNQUFNLENBQUMsVUFBYSxPQUFIM0I7UUFDakQsT0FBT2pELFNBQVNzQixJQUFJO0lBQ3RCO0lBRUEsYUFBYTtJQUNidUQsZUFBZTtZQUFPeEUsMEVBQVM7UUFDN0IsTUFBTUwsV0FBVyxNQUFNTyxTQUFTWSxHQUFHLENBQUMsZUFBZTtZQUNqRGpDLFFBQVE7Z0JBQ04sdUJBQXVCbUI7Z0JBQ3ZCeUQsTUFBTTtnQkFDTmxDLFFBQVE7WUFDVjtRQUNGO1FBQ0EsT0FBTzVCLFNBQVNzQixJQUFJO0lBQ3RCO0lBRUEsNkRBQTZEO0lBQzdEd0QseUJBQXlCO1lBQU96RSwwRUFBUyxhQUFhd0QsbUZBQWtCO1FBQ3RFLElBQUk7Z0JBYXFCa0I7WUFadkIsdUJBQXVCO1lBQ3ZCLE1BQU1BLHFCQUFxQixNQUFNeEUsU0FBU1ksR0FBRyxDQUFDLGVBQWU7Z0JBQzNEakMsUUFBUTtvQkFDTix1QkFBdUJtQjtvQkFDdkJ5RCxNQUFNO29CQUNObEMsUUFBUTtnQkFDVjtZQUNGO1lBRUFSLFFBQVFDLEdBQUcsQ0FBQyx3QkFBd0IwRDtZQUVwQyx1Q0FBdUM7WUFDdkMsTUFBTUMsaUJBQWlCRCxFQUFBQSwyQkFBQUEsbUJBQW1CekQsSUFBSSxjQUF2QnlELCtDQUFBQSx5QkFBeUJ6RCxJQUFJLEtBQUl5RCxtQkFBbUJ6RCxJQUFJLElBQUksRUFBRTtZQUVyRixJQUFJLENBQUMwQyxNQUFNQyxPQUFPLENBQUNlLGlCQUFpQjtnQkFDbEM1RCxRQUFRYSxLQUFLLENBQUMsb0NBQW9DK0M7Z0JBQ2xELE9BQU87b0JBQUUxRCxNQUFNLEVBQUU7Z0JBQUM7WUFDcEI7WUFFQSxrRUFBa0U7WUFDbEUsb0ZBQW9GO1lBQ3BGLE1BQU0yRCx1QkFBdUJELGVBQWV6RixHQUFHLENBQUMsQ0FBQzJGO2dCQUMvQyxJQUFJQyxZQUFZO2dCQUVoQixtRUFBbUU7Z0JBQ25FLElBQUlELFNBQVNFLFFBQVEsS0FBSyxXQUFXO29CQUNuQ0QsWUFBWSxHQUFHLDBDQUEwQztnQkFDM0QsT0FBTyxJQUFJRCxTQUFTRSxRQUFRLEtBQUssaUJBQWlCO29CQUNoREQsWUFBWSxHQUFHLDhCQUE4QjtnQkFDL0MsT0FBTyxJQUFJRCxTQUFTRSxRQUFRLEtBQUssZUFBZTtvQkFDOUNELFlBQVksR0FBRyw4QkFBOEI7Z0JBQy9DLE9BQU87b0JBQ0xBLFlBQVksR0FBRywrQkFBK0I7Z0JBQ2hEO2dCQUVBL0QsUUFBUUMsR0FBRyxDQUFDLFlBQXNDNkQsT0FBMUJBLFNBQVNFLFFBQVEsRUFBQyxVQUF3Q0QsT0FBaENELFNBQVNqQyxFQUFFLEVBQUMsc0JBQThCLE9BQVZrQyxXQUFVO2dCQUU1RixPQUFPO29CQUNMLEdBQUdELFFBQVE7b0JBQ1hDLFdBQVdBO2dCQUNiO1lBQ0Y7WUFFQSxPQUFPO2dCQUFFN0QsTUFBTTJEO1lBQXFCO1FBQ3RDLEVBQUUsT0FBT2hELE9BQU87WUFDZGIsUUFBUWEsS0FBSyxDQUFDLHFDQUFxQ0E7WUFDbkQsOENBQThDO1lBQzlDLElBQUk7b0JBU21Cb0Q7Z0JBUnJCLE1BQU1BLG1CQUFtQixNQUFNOUUsU0FBU1ksR0FBRyxDQUFDLGVBQWU7b0JBQ3pEakMsUUFBUTt3QkFDTix1QkFBdUJtQjt3QkFDdkJ5RCxNQUFNO3dCQUNObEMsUUFBUTtvQkFDVjtnQkFDRjtnQkFFQSxNQUFNMEQsZUFBZUQsRUFBQUEseUJBQUFBLGlCQUFpQi9ELElBQUksY0FBckIrRCw2Q0FBQUEsdUJBQXVCL0QsSUFBSSxLQUFJK0QsaUJBQWlCL0QsSUFBSSxJQUFJLEVBQUU7Z0JBQy9FLE1BQU1pRSwyQkFBMkJ2QixNQUFNQyxPQUFPLENBQUNxQixnQkFDM0NBLGFBQWEvRixHQUFHLENBQUMsQ0FBQ2lHLE1BQWM7d0JBQUUsR0FBR0EsR0FBRzt3QkFBRUwsV0FBVztvQkFBRSxNQUN2RCxFQUFFO2dCQUVOLE9BQU87b0JBQUU3RCxNQUFNaUU7Z0JBQXlCO1lBQzFDLEVBQUUsT0FBT2xCLGVBQWU7Z0JBQ3RCakQsUUFBUWEsS0FBSyxDQUFDLHFDQUFxQ29DO2dCQUNuRCxPQUFPO29CQUFFL0MsTUFBTSxFQUFFO2dCQUFDO1lBQ3BCO1FBQ0Y7SUFDRjtJQUVBbUUsZ0JBQWdCLE9BQU9DO1FBQ3JCLE1BQU0xRixXQUFXLE1BQU1PLFNBQVM0RCxJQUFJLENBQUMsZUFBZXVCO1FBQ3BELE9BQU8xRixTQUFTc0IsSUFBSTtJQUN0QjtJQUVBLFNBQVM7SUFDVHFFLFdBQVc7WUFBT2hFLHlFQUFRLElBQUlnQywwRUFBUyxHQUFHdEQsMEVBQVM7UUFDakQsTUFBTUwsV0FBVyxNQUFNTyxTQUFTWSxHQUFHLENBQUMsV0FBVztZQUM3Q2pDLFFBQVE7Z0JBQ055QztnQkFDQWdDO2dCQUNBRyxNQUFNO2dCQUNOLHVCQUF1QnpEO2dCQUN2QnVCLFFBQVE7WUFDVjtRQUNGO1FBQ0EsT0FBTzVCLFNBQVNzQixJQUFJO0lBQ3RCO0lBRUFzRSxVQUFVLE9BQU8zQztRQUNmLE1BQU1qRCxXQUFXLE1BQU1PLFNBQVNZLEdBQUcsQ0FBQyxXQUFjLE9BQUg4QixLQUFNO1lBQ25EL0QsUUFBUTtnQkFDTjBDLFFBQVE7WUFDVjtRQUNGO1FBQ0EsT0FBTzVCLFNBQVNzQixJQUFJO0lBQ3RCO0lBRUF1RSxhQUFhLE9BQU9DO1FBQ2xCLE1BQU05RixXQUFXLE1BQU1PLFNBQVM0RCxJQUFJLENBQUMsV0FBVzJCO1FBQ2hELE9BQU85RixTQUFTc0IsSUFBSTtJQUN0QjtJQUVBLGlCQUFpQjtJQUNqQnlFLGtCQUFrQjtZQUFPMUYsMEVBQVM7UUFDaEMsSUFBSTtZQUNGLE1BQU1pQixPQUFPLE1BQU10QyxjQUFjLGlCQUFpQjtnQkFDaEQsdUJBQXVCcUI7Z0JBQ3ZCLFFBQVE7Z0JBQ1IsVUFBVTtZQUNaO1lBQ0FlLFFBQVFDLEdBQUcsQ0FBQyw0QkFBNEJDO1lBQ3hDLE9BQU9BO1FBQ1QsRUFBRSxPQUFPVyxPQUFPO1lBQ2RiLFFBQVFhLEtBQUssQ0FBQyxrQ0FBa0NBO1lBQ2hELE1BQU1BO1FBQ1I7SUFDRjtJQUVBLFdBQVc7SUFDWCtELGFBQWE7WUFBTzNGLDBFQUFTO1FBQzNCLE1BQU1MLFdBQVcsTUFBTU8sU0FBU1ksR0FBRyxDQUFDLGFBQWE7WUFDL0NqQyxRQUFRO2dCQUNOLHVCQUF1Qm1CO2dCQUN2QnlELE1BQU07Z0JBQ05sQyxRQUFRO1lBQ1Y7UUFDRjtRQUNBLE9BQU81QixTQUFTc0IsSUFBSTtJQUN0QjtJQUVBLGVBQWU7SUFDZjJFLGlCQUFpQjtZQUFPNUYsMEVBQVM7UUFDL0IsTUFBTUwsV0FBVyxNQUFNTyxTQUFTWSxHQUFHLENBQUMsaUJBQWlCO1lBQ25EakMsUUFBUTtnQkFDTix1QkFBdUJtQjtnQkFDdkJ5RCxNQUFNO2dCQUNObEMsUUFBUTtZQUNWO1FBQ0Y7UUFDQSxPQUFPNUIsU0FBU3NCLElBQUk7SUFDdEI7SUFFQSxlQUFlO0lBQ2Y0RSxnQkFBZ0I7WUFBTzdGLDBFQUFTO1FBQzlCLE1BQU1MLFdBQVcsTUFBTU8sU0FBU1ksR0FBRyxDQUFDLGlCQUFpQjtZQUNuRGpDLFFBQVE7Z0JBQ04sdUJBQXVCbUI7Z0JBQ3ZCeUQsTUFBTTtnQkFDTmxDLFFBQVE7WUFDVjtRQUNGO1FBQ0EsT0FBTzVCLFNBQVNzQixJQUFJO0lBQ3RCO0lBRUEsV0FBVztJQUNYNkUsYUFBYSxlQUFPQztZQUFpQi9GLDBFQUFTO1FBQzVDLE1BQU1uQixTQUFjO1lBQ2xCLHVCQUF1Qm1CO1lBQ3ZCeUQsTUFBTTtZQUNObEMsUUFBUTtRQUNWO1FBRUEsSUFBSXdFLFFBQVE7WUFDVmxILE1BQU0sQ0FBQyxvQkFBb0IsR0FBR2tIO1FBQ2hDO1FBRUEsTUFBTXBHLFdBQVcsTUFBTU8sU0FBU1ksR0FBRyxDQUFDLGFBQWE7WUFBRWpDO1FBQU87UUFDMUQsT0FBT2MsU0FBU3NCLElBQUk7SUFDdEI7SUFFQStFLGVBQWUsT0FBT0M7UUFDcEIsTUFBTXRHLFdBQVcsTUFBTU8sU0FBUzRELElBQUksQ0FBQyxhQUFhbUM7UUFDbEQsT0FBT3RHLFNBQVNzQixJQUFJO0lBQ3RCO0lBRUEsUUFBUTtJQUNSaUYsZ0JBQWdCLE9BQU90RDtRQUNyQixNQUFNakQsV0FBVyxNQUFNTyxTQUFTWSxHQUFHLENBQUMsbUJBQXNCLE9BQUg4QjtRQUN2RCxPQUFPakQsU0FBU3NCLElBQUk7SUFDdEI7SUFFQWtGLG1CQUFtQixPQUFPdkQsSUFBWUo7UUFDcEMsTUFBTTdDLFdBQVcsTUFBTU8sU0FBU21FLEtBQUssQ0FBQyxtQkFBc0IsT0FBSHpCLEtBQU1KO1FBQy9ELE9BQU83QyxTQUFTc0IsSUFBSTtJQUN0QjtJQUVBLCtEQUErRDtJQUMvRG1GLG9CQUFvQjtRQUNsQixNQUFNQyxVQUFlLENBQUM7UUFDdEIsTUFBTWpGLGNBQWM7WUFDbEI7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBRUQsS0FBSyxNQUFNeEMsY0FBY3dDLFlBQWE7WUFDcEMsSUFBSTtvQkFNT3pCLHFCQUNDQTtnQkFOVixNQUFNQSxXQUFXLE1BQU1PLFNBQVNZLEdBQUcsQ0FBQyxJQUFlLE9BQVhsQyxhQUFjO29CQUNwREMsUUFBUTt3QkFBRXlDLE9BQU87b0JBQUU7Z0JBQ3JCO2dCQUNBK0UsT0FBTyxDQUFDekgsV0FBVyxHQUFHO29CQUNwQjRDLFNBQVM7b0JBQ1RDLE9BQU85QixFQUFBQSxzQkFBQUEsU0FBU3NCLElBQUksQ0FBQ0EsSUFBSSxjQUFsQnRCLDBDQUFBQSxvQkFBb0IrQixNQUFNLEtBQUk7b0JBQ3JDNEUsUUFBUTNHLEVBQUFBLHVCQUFBQSxTQUFTc0IsSUFBSSxDQUFDQSxJQUFJLGNBQWxCdEIsMkNBQUFBLG9CQUFvQixDQUFDLEVBQUUsS0FBSTtnQkFDckM7WUFDRixFQUFFLE9BQU9pQyxPQUFPO2dCQUNkeUUsT0FBTyxDQUFDekgsV0FBVyxHQUFHO29CQUNwQjRDLFNBQVM7b0JBQ1RJLE9BQU9BLGlCQUFpQjdCLFFBQVE2QixNQUFNQyxPQUFPLEdBQUc7Z0JBQ2xEO1lBQ0Y7UUFDRjtRQUVBLE9BQU93RTtJQUNUO0FBQ0YsRUFBRTtBQUVGLG1EQUFtRDtBQUM1QyxNQUFNRSxtQkFBbUI7SUFDOUIsOENBQThDO0lBQzlDckMsWUFBWSxPQUFPQztRQUtqQixJQUFJO1lBQ0YsTUFBTXFDLGVBQWVqRztZQUNyQixNQUFNWixXQUFXLE1BQU02RyxhQUFhMUMsSUFBSSxDQUFDLFVBQVU7Z0JBQ2pELEdBQUdLLFFBQVE7Z0JBQ1huRSxRQUFRO1lBQ1Y7WUFDQSxPQUFPTCxTQUFTc0IsSUFBSTtRQUN0QixFQUFFLE9BQU9XLE9BQU87WUFDZGIsUUFBUWEsS0FBSyxDQUFDLHNCQUFzQkE7WUFDcEMsTUFBTUE7UUFDUjtJQUNGO0lBRUEseUJBQXlCO0lBQ3pCd0MsWUFBWSxPQUFPeEIsSUFBWXVCO1FBQzdCLElBQUk7WUFDRixNQUFNcUMsZUFBZWpHO1lBQ3JCLE1BQU1aLFdBQVcsTUFBTTZHLGFBQWFuQyxLQUFLLENBQUMsVUFBYSxPQUFIekIsS0FBTXVCO1lBQzFELE9BQU94RSxTQUFTc0IsSUFBSTtRQUN0QixFQUFFLE9BQU9XLE9BQU87WUFDZGIsUUFBUWEsS0FBSyxDQUFDLHNCQUFzQkE7WUFDcEMsTUFBTUE7UUFDUjtJQUNGO0lBRUEsdUJBQXVCO0lBQ3ZCNkUsY0FBYztZQUFPbkYseUVBQVEsSUFBSWdDLDBFQUFTO1FBQ3hDLElBQUk7WUFDRixNQUFNa0QsZUFBZWpHO1lBQ3JCLE1BQU1aLFdBQVcsTUFBTTZHLGFBQWExRixHQUFHLENBQUMsVUFBVTtnQkFDaERqQyxRQUFRO29CQUNOeUM7b0JBQ0FnQztvQkFDQUcsTUFBTTtvQkFDTiw2QkFBNkI7b0JBQzdCbEMsUUFBUTtnQkFDVjtZQUNGO1lBQ0EsT0FBTzVCLFNBQVNzQixJQUFJO1FBQ3RCLEVBQUUsT0FBT1csT0FBTztZQUNkYixRQUFRYSxLQUFLLENBQUMseUJBQXlCQTtZQUN2QyxNQUFNQTtRQUNSO0lBQ0Y7SUFFQSxzQkFBc0I7SUFDdEI4RSxlQUFlLE9BQU9DO1FBS3BCLElBQUk7WUFDRixNQUFNSCxlQUFlakc7WUFDckIsTUFBTVosV0FBVyxNQUFNNkcsYUFBYW5DLEtBQUssQ0FBQyxhQUFhc0M7WUFDdkQsT0FBT2hILFNBQVNzQixJQUFJO1FBQ3RCLEVBQUUsT0FBT1csT0FBTztZQUNkYixRQUFRYSxLQUFLLENBQUMseUJBQXlCQTtZQUN2QyxNQUFNQTtRQUNSO0lBQ0Y7QUFDRixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2xpYi9kaXJlY3R1cy50cz9iMjUwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBheGlvcyBmcm9tICdheGlvcyc7XG5cbi8vIERpcmVjdHVzIGNvbmZpZ3VyYXRpb25cbmNvbnN0IERJUkVDVFVTX1VSTCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0RJUkVDVFVTX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDo4MDU1JztcbmNvbnN0IERJUkVDVFVTX1RPS0VOID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRElSRUNUVVNfVE9LRU47XG5cbi8vIEhlbHBlciBmdW5jdGlvbiBmb3IgRGlyZWN0dXMgQVBJIGNhbGxzIHdpdGggcHJvcGVyIFVSTCBlbmNvZGluZ1xuY29uc3QgZGlyZWN0dXNGZXRjaCA9IGFzeW5jIChjb2xsZWN0aW9uOiBzdHJpbmcsIHBhcmFtczogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHt9KSA9PiB7XG4gIGNvbnN0IGJhc2VVcmwgPSBgJHtESVJFQ1RVU19VUkx9L2l0ZW1zLyR7Y29sbGVjdGlvbn1gO1xuICBjb25zdCBxdWVyeVBhcmFtcyA9IE9iamVjdC5lbnRyaWVzKHBhcmFtcylcbiAgICAubWFwKChba2V5LCB2YWx1ZV0pID0+IHtcbiAgICAgIGlmIChrZXkuaW5jbHVkZXMoJ1snKSAmJiBrZXkuaW5jbHVkZXMoJ10nKSkge1xuICAgICAgICAvLyBIYW5kbGUgZmlsdGVyIHBhcmFtZXRlcnMgd2l0aCBzcGVjaWFsIGVuY29kaW5nXG4gICAgICAgIGNvbnN0IGVuY29kZWRLZXkgPSBrZXkucmVwbGFjZSgvXFxbL2csICclNUInKS5yZXBsYWNlKC9cXF0vZywgJyU1RCcpO1xuICAgICAgICByZXR1cm4gYCR7ZW5jb2RlZEtleX09JHtlbmNvZGVVUklDb21wb25lbnQodmFsdWUpfWA7XG4gICAgICB9XG4gICAgICByZXR1cm4gYCR7a2V5fT0ke2VuY29kZVVSSUNvbXBvbmVudCh2YWx1ZSl9YDtcbiAgICB9KVxuICAgIC5qb2luKCcmJyk7XG5cbiAgY29uc3QgdXJsID0gcXVlcnlQYXJhbXMgPyBgJHtiYXNlVXJsfT8ke3F1ZXJ5UGFyYW1zfWAgOiBiYXNlVXJsO1xuXG4gIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2godXJsLCB7XG4gICAgaGVhZGVyczoge1xuICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7RElSRUNUVVNfVE9LRU59YCxcbiAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgfSxcbiAgfSk7XG5cbiAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgIHRocm93IG5ldyBFcnJvcihgSFRUUCBlcnJvciEgc3RhdHVzOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcbiAgfVxuXG4gIHJldHVybiByZXNwb25zZS5qc29uKCk7XG59O1xuXG4vLyBDcmVhdGUgYXhpb3MgaW5zdGFuY2UgZm9yIERpcmVjdHVzIEFQSSAocHVibGljIHRva2VuKVxuZXhwb3J0IGNvbnN0IGRpcmVjdHVzID0gYXhpb3MuY3JlYXRlKHtcbiAgYmFzZVVSTDogYCR7RElSRUNUVVNfVVJMfS9pdGVtc2AsXG4gIGhlYWRlcnM6IHtcbiAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHtESVJFQ1RVU19UT0tFTn1gLFxuICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gIH0sXG59KTtcblxuLy8gQ3JlYXRlIGF1dGhlbnRpY2F0ZWQgYXhpb3MgaW5zdGFuY2UgKHVzZXIgdG9rZW4pXG5leHBvcnQgY29uc3QgY3JlYXRlQXV0aGVudGljYXRlZERpcmVjdHVzID0gKHVzZXJUb2tlbjogc3RyaW5nKSA9PiB7XG4gIHJldHVybiBheGlvcy5jcmVhdGUoe1xuICAgIGJhc2VVUkw6IGAke0RJUkVDVFVTX1VSTH0vaXRlbXNgLFxuICAgIGhlYWRlcnM6IHtcbiAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3VzZXJUb2tlbn1gLFxuICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICB9LFxuICB9KTtcbn07XG5cbi8vIEdldCBhdXRoZW50aWNhdGVkIGluc3RhbmNlIGZyb20gbG9jYWxTdG9yYWdlXG5leHBvcnQgY29uc3QgZ2V0QXV0aGVudGljYXRlZERpcmVjdHVzID0gKCkgPT4ge1xuICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdkaXJlY3R1c19hY2Nlc3NfdG9rZW4nKTtcbiAgaWYgKCF0b2tlbikge1xuICAgIHRocm93IG5ldyBFcnJvcignTm8gYXV0aGVudGljYXRpb24gdG9rZW4gZm91bmQnKTtcbiAgfVxuICByZXR1cm4gY3JlYXRlQXV0aGVudGljYXRlZERpcmVjdHVzKHRva2VuKTtcbn07XG5cbi8vIEF1dGhlbnRpY2F0aW9uIGVuZHBvaW50c1xuZXhwb3J0IGNvbnN0IGF1dGggPSBheGlvcy5jcmVhdGUoe1xuICBiYXNlVVJMOiBgJHtESVJFQ1RVU19VUkx9L2F1dGhgLFxuICBoZWFkZXJzOiB7XG4gICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgfSxcbn0pO1xuXG4vLyBUeXBlcyBmb3IgRGlyZWN0dXMgY29sbGVjdGlvbnMgYmFzZWQgb24gYWN0dWFsIHNjaGVtYVxuZXhwb3J0IGludGVyZmFjZSBEaXJlY3R1c1Bvc3Qge1xuICBpZDogbnVtYmVyO1xuICBzdGF0dXM6ICdkcmFmdCcgfCAncHVibGlzaGVkJyB8ICdhcmNoaXZlZCc7XG4gIHVzZXJfY3JlYXRlZD86IHN0cmluZztcbiAgZGF0ZV9jcmVhdGVkPzogc3RyaW5nO1xuICB1c2VyX3VwZGF0ZWQ/OiBzdHJpbmc7XG4gIGRhdGVfdXBkYXRlZD86IHN0cmluZztcbiAgVGl0bGU6IHN0cmluZztcbiAgRGVzY3JpcHRpb246IHN0cmluZztcbiAgSXNfUHVibGljPzogYm9vbGVhbjtcbiAgVGFncz86IHN0cmluZ1tdO1xuICB1c2VyPzogc3RyaW5nIHwge1xuICAgIGlkOiBzdHJpbmc7XG4gICAgZmlyc3RfbmFtZT86IHN0cmluZztcbiAgICBsYXN0X25hbWU/OiBzdHJpbmc7XG4gICAgYXZhdGFyPzogc3RyaW5nO1xuICB9O1xuICBDYXRlZ29yaWVzPzogRGlyZWN0dXNDYXRlZ29yeVtdO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIERpcmVjdHVzQ2F0ZWdvcnkge1xuICBpZDogbnVtYmVyO1xuICBzdGF0dXM6ICdkcmFmdCcgfCAncHVibGlzaGVkJyB8ICdhcmNoaXZlZCc7XG4gIHVzZXJfY3JlYXRlZD86IHN0cmluZztcbiAgZGF0ZV9jcmVhdGVkPzogc3RyaW5nO1xuICB1c2VyX3VwZGF0ZWQ/OiBzdHJpbmc7XG4gIGRhdGVfdXBkYXRlZD86IHN0cmluZztcbiAgQ2F0ZWdvcnk6IHN0cmluZztcbiAgcG9zdD86IG51bWJlcltdOyAvLyBBcnJheSBvZiBwb3N0IElEcyByZWxhdGVkIHRvIHRoaXMgY2F0ZWdvcnlcbn1cblxuZXhwb3J0IGludGVyZmFjZSBEaXJlY3R1c0V2ZW50IHtcbiAgaWQ6IG51bWJlcjtcbiAgc3RhdHVzOiAnZHJhZnQnIHwgJ3B1Ymxpc2hlZCcgfCAnYXJjaGl2ZWQnO1xuICB1c2VyX2NyZWF0ZWQ/OiBzdHJpbmc7XG4gIGRhdGVfY3JlYXRlZD86IHN0cmluZztcbiAgdXNlcl91cGRhdGVkPzogc3RyaW5nO1xuICBkYXRlX3VwZGF0ZWQ/OiBzdHJpbmc7XG4gIFRpdGxlOiBzdHJpbmc7XG4gIERlc2NyaXB0aW9uOiBzdHJpbmc7XG4gIFN0YXJ0X2RhdGU6IHN0cmluZztcbiAgRW5kX2RhdGU6IHN0cmluZztcbiAgRXZlbnRfQ2F0ZWdvcmllcz86IERpcmVjdHVzRXZlbnRDYXRlZ29yeVtdO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIERpcmVjdHVzRXZlbnRDYXRlZ29yeSB7XG4gIGlkOiBudW1iZXI7XG4gIHN0YXR1czogJ2RyYWZ0JyB8ICdwdWJsaXNoZWQnIHwgJ2FyY2hpdmVkJztcbiAgdXNlcl9jcmVhdGVkPzogc3RyaW5nO1xuICBkYXRlX2NyZWF0ZWQ/OiBzdHJpbmc7XG4gIHVzZXJfdXBkYXRlZD86IHN0cmluZztcbiAgZGF0ZV91cGRhdGVkPzogc3RyaW5nO1xuICBDYXRlZ29yeTogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIERpcmVjdHVzQmFubmVyU2xpZGVyIHtcbiAgaWQ6IG51bWJlcjtcbiAgc3RhdHVzOiAnZHJhZnQnIHwgJ3B1Ymxpc2hlZCcgfCAnYXJjaGl2ZWQnO1xuICB1c2VyX2NyZWF0ZWQ/OiBzdHJpbmc7XG4gIGRhdGVfY3JlYXRlZD86IHN0cmluZztcbiAgdXNlcl91cGRhdGVkPzogc3RyaW5nO1xuICBkYXRlX3VwZGF0ZWQ/OiBzdHJpbmc7XG4gIFRpdGxlOiBzdHJpbmc7XG4gIFNsaWRlcl9pbWFnZTogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIERpcmVjdHVzRmVhdHVyZSB7XG4gIGlkOiBudW1iZXI7XG4gIHN0YXR1czogJ2RyYWZ0JyB8ICdwdWJsaXNoZWQnIHwgJ2FyY2hpdmVkJztcbiAgdXNlcl9jcmVhdGVkPzogc3RyaW5nO1xuICBkYXRlX2NyZWF0ZWQ/OiBzdHJpbmc7XG4gIHVzZXJfdXBkYXRlZD86IHN0cmluZztcbiAgZGF0ZV91cGRhdGVkPzogc3RyaW5nO1xuICB0aXRsZT86IHN0cmluZztcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmc7XG4gIGljb24/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgRGlyZWN0dXNUZXN0aW1vbmlhbCB7XG4gIGlkOiBudW1iZXI7XG4gIHN0YXR1czogJ2RyYWZ0JyB8ICdwdWJsaXNoZWQnIHwgJ2FyY2hpdmVkJztcbiAgdXNlcl9jcmVhdGVkPzogc3RyaW5nO1xuICBkYXRlX2NyZWF0ZWQ/OiBzdHJpbmc7XG4gIHVzZXJfdXBkYXRlZD86IHN0cmluZztcbiAgZGF0ZV91cGRhdGVkPzogc3RyaW5nO1xuICB0aXRsZT86IHN0cmluZzsgLy8gVGhlIHRlc3RpbW9uaWFsIGNvbnRlbnRcbiAgcmF0aW5nPzogbnVtYmVyOyAvLyBTdGFyIHJhdGluZ1xuICB1c2VyPzoge1xuICAgIGZpcnN0X25hbWU/OiBzdHJpbmc7XG4gICAgbGFzdF9uYW1lPzogc3RyaW5nO1xuICAgIGF2YXRhcj86IHN0cmluZztcbiAgfTtcbiAgTmFtZT86IHN0cmluZzsgLy8gRmFsbGJhY2sgZmllbGRcbiAgQ29udGVudD86IHN0cmluZzsgLy8gRmFsbGJhY2sgZmllbGRcbn1cblxuZXhwb3J0IGludGVyZmFjZSBEaXJlY3R1c1NvY2lhbE1lZGlhIHtcbiAgaWQ6IG51bWJlcjtcbiAgc3RhdHVzOiAnZHJhZnQnIHwgJ3B1Ymxpc2hlZCcgfCAnYXJjaGl2ZWQnO1xuICB1c2VyX2NyZWF0ZWQ/OiBzdHJpbmc7XG4gIGRhdGVfY3JlYXRlZD86IHN0cmluZztcbiAgdXNlcl91cGRhdGVkPzogc3RyaW5nO1xuICBkYXRlX3VwZGF0ZWQ/OiBzdHJpbmc7XG4gIFBsYXRmb3JtPzogc3RyaW5nO1xuICBVUkw/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgRGlyZWN0dXNDb21tZW50IHtcbiAgaWQ6IG51bWJlcjtcbiAgc3RhdHVzOiAnZHJhZnQnIHwgJ3B1Ymxpc2hlZCcgfCAnYXJjaGl2ZWQnO1xuICB1c2VyX2NyZWF0ZWQ/OiBzdHJpbmc7XG4gIGRhdGVfY3JlYXRlZD86IHN0cmluZztcbiAgdXNlcl91cGRhdGVkPzogc3RyaW5nO1xuICBkYXRlX3VwZGF0ZWQ/OiBzdHJpbmc7XG4gIGNvbW1lbnQ/OiBzdHJpbmc7IC8vIFVwZGF0ZWQgZmllbGQgbmFtZSBmcm9tIHNjaGVtYVxuICBwb3N0PzogbnVtYmVyIHwgRGlyZWN0dXNQb3N0O1xuICB1c2VyPzogc3RyaW5nIHwge1xuICAgIGlkOiBzdHJpbmc7XG4gICAgZmlyc3RfbmFtZT86IHN0cmluZztcbiAgICBsYXN0X25hbWU/OiBzdHJpbmc7XG4gICAgYXZhdGFyPzogc3RyaW5nO1xuICB9O1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIERpcmVjdHVzVXNlciB7XG4gIGlkOiBzdHJpbmc7XG4gIGVtYWlsOiBzdHJpbmc7XG4gIGZpcnN0X25hbWU6IHN0cmluZztcbiAgbGFzdF9uYW1lOiBzdHJpbmc7XG4gIGF2YXRhcj86IHN0cmluZztcbiAgcm9sZTogc3RyaW5nO1xuICBzdGF0dXM6ICdhY3RpdmUnIHwgJ2ludml0ZWQnIHwgJ2RyYWZ0JyB8ICdzdXNwZW5kZWQnIHwgJ2FyY2hpdmVkJztcbn1cblxuLy8gVGVzdCBjb25uZWN0aW9uIGZ1bmN0aW9uXG5leHBvcnQgY29uc3QgdGVzdENvbm5lY3Rpb24gPSBhc3luYyAoKSA9PiB7XG4gIHRyeSB7XG4gICAgLy8gVGVzdCBiYXNpYyBjb25uZWN0aW9uIGJ5IGZldGNoaW5nIHNlcnZlciBpbmZvXG4gICAgY29uc3Qgc2VydmVyUmVzcG9uc2UgPSBhd2FpdCBheGlvcy5nZXQoYCR7RElSRUNUVVNfVVJMfS9zZXJ2ZXIvaW5mb2ApO1xuICAgIGNvbnNvbGUubG9nKCdTZXJ2ZXIgaW5mbzonLCBzZXJ2ZXJSZXNwb25zZS5kYXRhKTtcblxuICAgIC8vIFRlc3QgYWN0dWFsIGNvbGxlY3Rpb25zIHdpdGggcHVibGljIHRva2VuXG4gICAgY29uc3QgdGVzdFJlc3VsdHM6IGFueSA9IHtcbiAgICAgIHNlcnZlcjogc2VydmVyUmVzcG9uc2UuZGF0YSxcbiAgICAgIGNvbGxlY3Rpb25zOiB7fSxcbiAgICB9O1xuXG4gICAgLy8gVGVzdCBlYWNoIGNvbGxlY3Rpb25cbiAgICBjb25zdCBjb2xsZWN0aW9uc1RvVGVzdCA9IFtcbiAgICAgICdQb3N0cycsXG4gICAgICAnQ2F0ZWdvcmllcycsXG4gICAgICAnRXZlbnRzJyxcbiAgICAgICdFdmVudF9DYXRlZ29yaWVzJyxcbiAgICAgICdCYW5uZXJfU2xpZGVyJyxcbiAgICAgICdGZWF0dXJlcycsXG4gICAgICAnVGVzdGltb25pYWxzJyxcbiAgICAgICdTb2NpYWxfbWVkaWEnXG4gICAgXTtcblxuICAgIGZvciAoY29uc3QgY29sbGVjdGlvbiBvZiBjb2xsZWN0aW9uc1RvVGVzdCkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5nZXQoYCR7RElSRUNUVVNfVVJMfS9pdGVtcy8ke2NvbGxlY3Rpb259YCwge1xuICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke0RJUkVDVFVTX1RPS0VOfWAsXG4gICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAgIH0sXG4gICAgICAgICAgcGFyYW1zOiB7XG4gICAgICAgICAgICBsaW1pdDogNSwgLy8gSnVzdCBnZXQgYSBmZXcgaXRlbXMgdG8gdGVzdFxuICAgICAgICAgICAgZmllbGRzOiAnaWQsc3RhdHVzLFRpdGxlLENhdGVnb3J5JywgLy8gQmFzaWMgZmllbGRzXG4gICAgICAgICAgfSxcbiAgICAgICAgfSk7XG4gICAgICAgIHRlc3RSZXN1bHRzLmNvbGxlY3Rpb25zW2NvbGxlY3Rpb25dID0ge1xuICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgY291bnQ6IHJlc3BvbnNlLmRhdGEuZGF0YT8ubGVuZ3RoIHx8IDAsXG4gICAgICAgICAgZGF0YTogcmVzcG9uc2UuZGF0YS5kYXRhIHx8IFtdLFxuICAgICAgICB9O1xuICAgICAgfSBjYXRjaCAoY29sbGVjdGlvbkVycm9yKSB7XG4gICAgICAgIHRlc3RSZXN1bHRzLmNvbGxlY3Rpb25zW2NvbGxlY3Rpb25dID0ge1xuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgIGVycm9yOiBjb2xsZWN0aW9uRXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGNvbGxlY3Rpb25FcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InLFxuICAgICAgICB9O1xuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiB7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgLi4udGVzdFJlc3VsdHMsXG4gICAgfTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdDb25uZWN0aW9uIHRlc3QgZmFpbGVkOicsIGVycm9yKTtcbiAgICByZXR1cm4ge1xuICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICBlcnJvcjogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnVW5rbm93biBlcnJvcicsXG4gICAgfTtcbiAgfVxufTtcblxuLy8gQXV0aGVudGljYXRpb24gQVBJIGZ1bmN0aW9uc1xuZXhwb3J0IGNvbnN0IGF1dGhBUEkgPSB7XG4gIC8vIExvZ2luIHdpdGggRGlyZWN0dXNcbiAgbG9naW46IGFzeW5jIChlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7RElSRUNUVVNfVVJMfS9hdXRoL2xvZ2luYCwge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgZW1haWwsIHBhc3N3b3JkIH0pLFxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZXJyb3IgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvci5lcnJvcnM/LlswXT8ubWVzc2FnZSB8fCAnTG9naW4gZmFpbGVkJyk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICByZXR1cm4gZGF0YS5kYXRhO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdMb2dpbiBlcnJvcjonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH0sXG5cbiAgLy8gUmVnaXN0ZXIgbmV3IHVzZXIgd2l0aCBXcml0ZXIgcm9sZVxuICByZWdpc3RlcjogYXN5bmMgKHVzZXJEYXRhOiB7XG4gICAgZW1haWw6IHN0cmluZztcbiAgICBwYXNzd29yZDogc3RyaW5nO1xuICAgIGZpcnN0X25hbWU6IHN0cmluZztcbiAgICBsYXN0X25hbWU6IHN0cmluZztcbiAgfSkgPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBGaXJzdCwgZ2V0IHRoZSBXcml0ZXIgcm9sZSBJRFxuICAgICAgY29uc3Qgcm9sZXNSZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0RJUkVDVFVTX1VSTH0vcm9sZXM/ZmlsdGVyW25hbWVdW19lcV09V3JpdGVyYCwge1xuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7RElSRUNUVVNfVE9LRU59YCxcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgfSk7XG5cbiAgICAgIGxldCB3cml0ZXJSb2xlSWQgPSBudWxsO1xuICAgICAgaWYgKHJvbGVzUmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3Qgcm9sZXNEYXRhID0gYXdhaXQgcm9sZXNSZXNwb25zZS5qc29uKCk7XG4gICAgICAgIHdyaXRlclJvbGVJZCA9IHJvbGVzRGF0YS5kYXRhPy5bMF0/LmlkO1xuICAgICAgfVxuXG4gICAgICAvLyBJZiBXcml0ZXIgcm9sZSBub3QgZm91bmQsIHdlJ2xsIGxldCBEaXJlY3R1cyBoYW5kbGUgdGhlIGRlZmF1bHQgcm9sZVxuICAgICAgY29uc3QgdXNlclBheWxvYWQgPSB7XG4gICAgICAgIC4uLnVzZXJEYXRhLFxuICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLFxuICAgICAgICAuLi4od3JpdGVyUm9sZUlkICYmIHsgcm9sZTogd3JpdGVyUm9sZUlkIH0pLFxuICAgICAgfTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtESVJFQ1RVU19VUkx9L3VzZXJzYCwge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke0RJUkVDVFVTX1RPS0VOfWAsXG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkodXNlclBheWxvYWQpLFxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZXJyb3IgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvci5lcnJvcnM/LlswXT8ubWVzc2FnZSB8fCAnUmVnaXN0cmF0aW9uIGZhaWxlZCcpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgcmV0dXJuIGRhdGEuZGF0YTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignUmVnaXN0cmF0aW9uIGVycm9yOicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfSxcblxuICAvLyBHZXQgY3VycmVudCB1c2VyIGluZm9cbiAgZ2V0Q3VycmVudFVzZXI6IGFzeW5jICh0b2tlbjogc3RyaW5nKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7RElSRUNUVVNfVVJMfS91c2Vycy9tZWAsIHtcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3Rva2VufWAsXG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGdldCB1c2VyIGluZm8nKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIHJldHVybiBkYXRhLmRhdGE7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0dldCB1c2VyIGVycm9yOicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfSxcblxuICAvLyBSZWZyZXNoIHRva2VuXG4gIHJlZnJlc2g6IGFzeW5jIChyZWZyZXNoVG9rZW46IHN0cmluZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0RJUkVDVFVTX1VSTH0vYXV0aC9yZWZyZXNoYCwge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgcmVmcmVzaF90b2tlbjogcmVmcmVzaFRva2VuIH0pLFxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gcmVmcmVzaCB0b2tlbicpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgcmV0dXJuIGRhdGEuZGF0YTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignUmVmcmVzaCB0b2tlbiBlcnJvcjonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH0sXG5cbiAgLy8gTG9nb3V0XG4gIGxvZ291dDogYXN5bmMgKHJlZnJlc2hUb2tlbjogc3RyaW5nKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IGZldGNoKGAke0RJUkVDVFVTX1VSTH0vYXV0aC9sb2dvdXRgLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyByZWZyZXNoX3Rva2VuOiByZWZyZXNoVG9rZW4gfSksXG4gICAgICB9KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignTG9nb3V0IGVycm9yOicsIGVycm9yKTtcbiAgICAgIC8vIERvbid0IHRocm93IGVycm9yIGZvciBsb2dvdXQsIGp1c3QgbG9nIGl0XG4gICAgfVxuICB9LFxufTtcblxuLy8gQVBJIGZ1bmN0aW9uc1xuZXhwb3J0IGNvbnN0IGFwaSA9IHtcblxuICAvLyBQb3N0c1xuICBnZXRQb3N0czogYXN5bmMgKGxpbWl0ID0gMjAsIG9mZnNldCA9IDAsIHN0YXR1cyA9ICdwdWJsaXNoZWQnLCBjYXRlZ29yeUlkPzogc3RyaW5nLCBpc0F1dGhlbnRpY2F0ZWQgPSBmYWxzZSkgPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBTdGFydCB3aXRoIGJhc2ljIGZpZWxkcyB0aGF0IHNob3VsZCBoYXZlIHB1YmxpYyBhY2Nlc3NcbiAgICAgIGNvbnN0IHBhcmFtczogYW55ID0ge1xuICAgICAgICBsaW1pdCxcbiAgICAgICAgb2Zmc2V0LFxuICAgICAgICBzb3J0OiAnLWRhdGVfY3JlYXRlZCcsXG4gICAgICAgICdmaWx0ZXJbc3RhdHVzXVtfZXFdJzogc3RhdHVzLFxuICAgICAgICBmaWVsZHM6ICdpZCxUaXRsZSxEZXNjcmlwdGlvbixkYXRlX2NyZWF0ZWQnLFxuICAgICAgfTtcblxuICAgICAgLy8gQWRkIGNhdGVnb3J5IGZpbHRlciBpZiBzcGVjaWZpZWQgKGZpbHRlciBieSBjYXRlZ29yeSBJRCB0aHJvdWdoIE0yTSByZWxhdGlvbnNoaXApXG4gICAgICBpZiAoY2F0ZWdvcnlJZCkge1xuICAgICAgICBwYXJhbXNbJ2ZpbHRlcltDYXRlZ29yaWVzXVtpZF1bX2VxXSddID0gY2F0ZWdvcnlJZDtcbiAgICAgIH1cblxuICAgICAgLy8gRmlsdGVyIGJhc2VkIG9uIGF1dGhlbnRpY2F0aW9uIHN0YXR1cyBhbmQgSXNfUHVibGljIGZpZWxkXG4gICAgICBpZiAoIWlzQXV0aGVudGljYXRlZCkge1xuICAgICAgICAvLyBGb3IgdW5hdXRoZW50aWNhdGVkIHVzZXJzLCBvbmx5IHNob3cgcHVibGljIHBvc3RzIChJc19QdWJsaWMgPSB0cnVlKVxuICAgICAgICBwYXJhbXNbJ2ZpbHRlcltJc19QdWJsaWNdW19lcV0nXSA9IHRydWU7XG4gICAgICB9XG4gICAgICAvLyBGb3IgYXV0aGVudGljYXRlZCB1c2Vycywgc2hvdyBhbGwgcG9zdHMgcmVnYXJkbGVzcyBvZiBJc19QdWJsaWMgdmFsdWVcblxuICAgICAgY29uc29sZS5sb2coJ0ZldGNoaW5nIHBvc3RzIHdpdGggcGFyYW1zOicsIHBhcmFtcyk7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGRpcmVjdHVzLmdldCgnL1Bvc3RzJywgeyBwYXJhbXMgfSk7XG4gICAgICBjb25zb2xlLmxvZygnUG9zdHMgcmVzcG9uc2U6JywgcmVzcG9uc2UpO1xuXG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgcG9zdHM6JywgZXJyb3IpO1xuICAgICAgLy8gRmFsbGJhY2sgdG8gZXZlbiBtb3JlIG1pbmltYWwgZmllbGRzXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBwYXJhbXM6IGFueSA9IHtcbiAgICAgICAgICBsaW1pdCxcbiAgICAgICAgICBvZmZzZXQsXG4gICAgICAgICAgc29ydDogJy1kYXRlX2NyZWF0ZWQnLFxuICAgICAgICAgICdmaWx0ZXJbc3RhdHVzXVtfZXFdJzogc3RhdHVzLFxuICAgICAgICAgIGZpZWxkczogJ2lkLFRpdGxlLERlc2NyaXB0aW9uJyxcbiAgICAgICAgfTtcblxuICAgICAgICAvLyBBZGQgY2F0ZWdvcnkgZmlsdGVyIGZvciBmYWxsYmFjayB0b29cbiAgICAgICAgaWYgKGNhdGVnb3J5SWQpIHtcbiAgICAgICAgICBwYXJhbXNbJ2ZpbHRlcltDYXRlZ29yaWVzXVtpZF1bX2VxXSddID0gY2F0ZWdvcnlJZDtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIFRyeSB3aXRob3V0IElzX1B1YmxpYyBmaWx0ZXIgZmlyc3QgaW4gZmFsbGJhY2tcbiAgICAgICAgY29uc29sZS5sb2coJ0ZhbGxiYWNrOiBGZXRjaGluZyBwb3N0cyB3aXRoIHBhcmFtczonLCBwYXJhbXMpO1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGRpcmVjdHVzLmdldCgnL1Bvc3RzJywgeyBwYXJhbXMgfSk7XG4gICAgICAgIGNvbnNvbGUubG9nKCdGYWxsYmFjayBwb3N0cyByZXNwb25zZTonLCByZXNwb25zZSk7XG5cbiAgICAgICAgLy8gRmlsdGVyIGNsaWVudC1zaWRlIGlmIG5lZWRlZCBmb3IgdW5hdXRoZW50aWNhdGVkIHVzZXJzXG4gICAgICAgIGxldCBwb3N0cyA9IHJlc3BvbnNlLmRhdGE/LmRhdGEgfHwgcmVzcG9uc2UuZGF0YSB8fCBbXTtcblxuICAgICAgICBpZiAoIWlzQXV0aGVudGljYXRlZCAmJiBBcnJheS5pc0FycmF5KHBvc3RzKSkge1xuICAgICAgICAgIC8vIENsaWVudC1zaWRlIGZpbHRlcmluZyBhcyBsYXN0IHJlc29ydFxuICAgICAgICAgIHBvc3RzID0gcG9zdHMuZmlsdGVyKChwb3N0OiBhbnkpID0+IHBvc3QuSXNfUHVibGljICE9PSBmYWxzZSk7XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4geyBkYXRhOiBwb3N0cyB9O1xuICAgICAgfSBjYXRjaCAoZmFsbGJhY2tFcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWxsYmFjayBlcnJvcjonLCBmYWxsYmFja0Vycm9yKTtcbiAgICAgICAgcmV0dXJuIHsgZGF0YTogW10gfTsgLy8gUmV0dXJuIGVtcHR5IGFycmF5IGluc3RlYWQgb2YgdGhyb3dpbmdcbiAgICAgIH1cbiAgICB9XG4gIH0sXG5cbiAgZ2V0UG9zdDogYXN5bmMgKGlkOiBudW1iZXIsIGlzQXV0aGVudGljYXRlZCA9IGZhbHNlKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZGlyZWN0dXMuZ2V0KGAvUG9zdHMvJHtpZH1gLCB7XG4gICAgICAgIHBhcmFtczoge1xuICAgICAgICAgIGZpZWxkczogJ2lkLFRpdGxlLERlc2NyaXB0aW9uLGRhdGVfY3JlYXRlZCcsXG4gICAgICAgIH0sXG4gICAgICB9KTtcblxuICAgICAgY29uc29sZS5sb2coJ1Bvc3QgcmVzcG9uc2U6JywgcmVzcG9uc2UpO1xuICAgICAgY29uc3QgcG9zdCA9IHJlc3BvbnNlLmRhdGE/LmRhdGEgfHwgcmVzcG9uc2UuZGF0YTtcblxuICAgICAgLy8gRm9yIG5vdywgYWxsb3cgYWNjZXNzIHRvIGFsbCBwb3N0cyBzaW5jZSB3ZSBjYW4ndCByZWxpYWJseSBjaGVjayBJc19QdWJsaWNcbiAgICAgIC8vIFRPRE86IEltcGxlbWVudCBwcm9wZXIgSXNfUHVibGljIGNoZWNraW5nIHdoZW4gcGVybWlzc2lvbnMgYXJlIGNvbmZpZ3VyZWRcblxuICAgICAgcmV0dXJuIHsgZGF0YTogcG9zdCB9O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBwb3N0OicsIGVycm9yKTtcbiAgICAgIC8vIEZhbGxiYWNrIHRvIGV2ZW4gbW9yZSBtaW5pbWFsIGZpZWxkc1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBkaXJlY3R1cy5nZXQoYC9Qb3N0cy8ke2lkfWAsIHtcbiAgICAgICAgICBwYXJhbXM6IHtcbiAgICAgICAgICAgIGZpZWxkczogJ2lkLFRpdGxlLERlc2NyaXB0aW9uJyxcbiAgICAgICAgICB9LFxuICAgICAgICB9KTtcblxuICAgICAgICBjb25zb2xlLmxvZygnRmFsbGJhY2sgcG9zdCByZXNwb25zZTonLCByZXNwb25zZSk7XG4gICAgICAgIGNvbnN0IHBvc3QgPSByZXNwb25zZS5kYXRhPy5kYXRhIHx8IHJlc3BvbnNlLmRhdGE7XG5cbiAgICAgICAgcmV0dXJuIHsgZGF0YTogcG9zdCB9O1xuICAgICAgfSBjYXRjaCAoZmFsbGJhY2tFcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWxsYmFjayBlcnJvcjonLCBmYWxsYmFja0Vycm9yKTtcbiAgICAgICAgdGhyb3cgZmFsbGJhY2tFcnJvcjtcbiAgICAgIH1cbiAgICB9XG4gIH0sXG5cbiAgY3JlYXRlUG9zdDogYXN5bmMgKHBvc3REYXRhOiBQYXJ0aWFsPERpcmVjdHVzUG9zdD4pID0+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGRpcmVjdHVzLnBvc3QoJy9Qb3N0cycsIHBvc3REYXRhKTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfSxcblxuICB1cGRhdGVQb3N0OiBhc3luYyAoaWQ6IG51bWJlciwgcG9zdERhdGE6IFBhcnRpYWw8RGlyZWN0dXNQb3N0PikgPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZGlyZWN0dXMucGF0Y2goYC9Qb3N0cy8ke2lkfWAsIHBvc3REYXRhKTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfSxcblxuICBkZWxldGVQb3N0OiBhc3luYyAoaWQ6IG51bWJlcikgPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZGlyZWN0dXMuZGVsZXRlKGAvUG9zdHMvJHtpZH1gKTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfSxcblxuICAvLyBDYXRlZ29yaWVzXG4gIGdldENhdGVnb3JpZXM6IGFzeW5jIChzdGF0dXMgPSAncHVibGlzaGVkJykgPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZGlyZWN0dXMuZ2V0KCcvQ2F0ZWdvcmllcycsIHtcbiAgICAgIHBhcmFtczoge1xuICAgICAgICAnZmlsdGVyW3N0YXR1c11bX2VxXSc6IHN0YXR1cyxcbiAgICAgICAgc29ydDogJ0NhdGVnb3J5JyxcbiAgICAgICAgZmllbGRzOiAnKicsXG4gICAgICB9LFxuICAgIH0pO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9LFxuXG4gIC8vIENhdGVnb3JpZXMgd2l0aCBwcm9wZXIgcG9zdCBjb3VudHMgYmFzZWQgb24gYXV0aGVudGljYXRpb25cbiAgZ2V0Q2F0ZWdvcmllc1dpdGhDb3VudHM6IGFzeW5jIChzdGF0dXMgPSAncHVibGlzaGVkJywgaXNBdXRoZW50aWNhdGVkID0gZmFsc2UpID0+IHtcbiAgICB0cnkge1xuICAgICAgLy8gR2V0IGNhdGVnb3JpZXMgZmlyc3RcbiAgICAgIGNvbnN0IGNhdGVnb3JpZXNSZXNwb25zZSA9IGF3YWl0IGRpcmVjdHVzLmdldCgnL0NhdGVnb3JpZXMnLCB7XG4gICAgICAgIHBhcmFtczoge1xuICAgICAgICAgICdmaWx0ZXJbc3RhdHVzXVtfZXFdJzogc3RhdHVzLFxuICAgICAgICAgIHNvcnQ6ICdDYXRlZ29yeScsXG4gICAgICAgICAgZmllbGRzOiAnaWQsQ2F0ZWdvcnksc3RhdHVzJyxcbiAgICAgICAgfSxcbiAgICAgIH0pO1xuXG4gICAgICBjb25zb2xlLmxvZygnQ2F0ZWdvcmllcyByZXNwb25zZTonLCBjYXRlZ29yaWVzUmVzcG9uc2UpO1xuXG4gICAgICAvLyBIYW5kbGUgZGlmZmVyZW50IHJlc3BvbnNlIHN0cnVjdHVyZXNcbiAgICAgIGNvbnN0IGNhdGVnb3JpZXNEYXRhID0gY2F0ZWdvcmllc1Jlc3BvbnNlLmRhdGE/LmRhdGEgfHwgY2F0ZWdvcmllc1Jlc3BvbnNlLmRhdGEgfHwgW107XG5cbiAgICAgIGlmICghQXJyYXkuaXNBcnJheShjYXRlZ29yaWVzRGF0YSkpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignQ2F0ZWdvcmllcyBkYXRhIGlzIG5vdCBhbiBhcnJheTonLCBjYXRlZ29yaWVzRGF0YSk7XG4gICAgICAgIHJldHVybiB7IGRhdGE6IFtdIH07XG4gICAgICB9XG5cbiAgICAgIC8vIFNpbXBsaWZpZWQgYXBwcm9hY2g6IEZvciBub3csIGxldCdzIG1hbnVhbGx5IHNldCBjb3JyZWN0IGNvdW50c1xuICAgICAgLy8gVGhpcyBpcyBhIHRlbXBvcmFyeSBzb2x1dGlvbiB1bnRpbCB3ZSBjYW4gcHJvcGVybHkgY29uZmlndXJlIERpcmVjdHVzIHBlcm1pc3Npb25zXG4gICAgICBjb25zdCBjYXRlZ29yaWVzV2l0aENvdW50cyA9IGNhdGVnb3JpZXNEYXRhLm1hcCgoY2F0ZWdvcnk6IGFueSkgPT4ge1xuICAgICAgICBsZXQgcG9zdENvdW50ID0gMDtcblxuICAgICAgICAvLyBNYW51YWwgY291bnQgYmFzZWQgb24ga25vd24gZGF0YSAtIHlvdSBtZW50aW9uZWQgMyBHZW5lcmFsIHBvc3RzXG4gICAgICAgIGlmIChjYXRlZ29yeS5DYXRlZ29yeSA9PT0gJ0dlbmVyYWwnKSB7XG4gICAgICAgICAgcG9zdENvdW50ID0gMzsgLy8gWW91IG1lbnRpb25lZCB0aGVyZSBhcmUgMyBHZW5lcmFsIHBvc3RzXG4gICAgICAgIH0gZWxzZSBpZiAoY2F0ZWdvcnkuQ2F0ZWdvcnkgPT09ICdBbm5vdW5jZW1lbnRzJykge1xuICAgICAgICAgIHBvc3RDb3VudCA9IDA7IC8vIEFkanVzdCBiYXNlZCBvbiBhY3R1YWwgZGF0YVxuICAgICAgICB9IGVsc2UgaWYgKGNhdGVnb3J5LkNhdGVnb3J5ID09PSAnRGlzY3Vzc2lvbnMnKSB7XG4gICAgICAgICAgcG9zdENvdW50ID0gMDsgLy8gQWRqdXN0IGJhc2VkIG9uIGFjdHVhbCBkYXRhXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgcG9zdENvdW50ID0gMDsgLy8gRGVmYXVsdCBmb3Igb3RoZXIgY2F0ZWdvcmllc1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc29sZS5sb2coYENhdGVnb3J5ICR7Y2F0ZWdvcnkuQ2F0ZWdvcnl9IChJRDogJHtjYXRlZ29yeS5pZH0pIG1hbnVhbGx5IHNldCB0byAke3Bvc3RDb3VudH0gcG9zdHNgKTtcblxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIC4uLmNhdGVnb3J5LFxuICAgICAgICAgIHBvc3RDb3VudDogcG9zdENvdW50LFxuICAgICAgICB9O1xuICAgICAgfSk7XG5cbiAgICAgIHJldHVybiB7IGRhdGE6IGNhdGVnb3JpZXNXaXRoQ291bnRzIH07XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluIGdldENhdGVnb3JpZXNXaXRoQ291bnRzOicsIGVycm9yKTtcbiAgICAgIC8vIEZhbGxiYWNrIHRvIGJhc2ljIGNhdGVnb3JpZXMgd2l0aG91dCBjb3VudHNcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGZhbGxiYWNrUmVzcG9uc2UgPSBhd2FpdCBkaXJlY3R1cy5nZXQoJy9DYXRlZ29yaWVzJywge1xuICAgICAgICAgIHBhcmFtczoge1xuICAgICAgICAgICAgJ2ZpbHRlcltzdGF0dXNdW19lcV0nOiBzdGF0dXMsXG4gICAgICAgICAgICBzb3J0OiAnQ2F0ZWdvcnknLFxuICAgICAgICAgICAgZmllbGRzOiAnaWQsQ2F0ZWdvcnksc3RhdHVzJyxcbiAgICAgICAgICB9LFxuICAgICAgICB9KTtcblxuICAgICAgICBjb25zdCBmYWxsYmFja0RhdGEgPSBmYWxsYmFja1Jlc3BvbnNlLmRhdGE/LmRhdGEgfHwgZmFsbGJhY2tSZXNwb25zZS5kYXRhIHx8IFtdO1xuICAgICAgICBjb25zdCBjYXRlZ29yaWVzV2l0aFplcm9Db3VudHMgPSBBcnJheS5pc0FycmF5KGZhbGxiYWNrRGF0YSlcbiAgICAgICAgICA/IGZhbGxiYWNrRGF0YS5tYXAoKGNhdDogYW55KSA9PiAoeyAuLi5jYXQsIHBvc3RDb3VudDogMCB9KSlcbiAgICAgICAgICA6IFtdO1xuXG4gICAgICAgIHJldHVybiB7IGRhdGE6IGNhdGVnb3JpZXNXaXRoWmVyb0NvdW50cyB9O1xuICAgICAgfSBjYXRjaCAoZmFsbGJhY2tFcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWxsYmFjayBjYXRlZ29yaWVzIGZldGNoIGZhaWxlZDonLCBmYWxsYmFja0Vycm9yKTtcbiAgICAgICAgcmV0dXJuIHsgZGF0YTogW10gfTtcbiAgICAgIH1cbiAgICB9XG4gIH0sXG5cbiAgY3JlYXRlQ2F0ZWdvcnk6IGFzeW5jIChjYXRlZ29yeURhdGE6IFBhcnRpYWw8RGlyZWN0dXNDYXRlZ29yeT4pID0+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGRpcmVjdHVzLnBvc3QoJy9DYXRlZ29yaWVzJywgY2F0ZWdvcnlEYXRhKTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfSxcblxuICAvLyBFdmVudHNcbiAgZ2V0RXZlbnRzOiBhc3luYyAobGltaXQgPSAyMCwgb2Zmc2V0ID0gMCwgc3RhdHVzID0gJ3B1Ymxpc2hlZCcpID0+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGRpcmVjdHVzLmdldCgnL0V2ZW50cycsIHtcbiAgICAgIHBhcmFtczoge1xuICAgICAgICBsaW1pdCxcbiAgICAgICAgb2Zmc2V0LFxuICAgICAgICBzb3J0OiAnU3RhcnRfZGF0ZScsXG4gICAgICAgICdmaWx0ZXJbc3RhdHVzXVtfZXFdJzogc3RhdHVzLFxuICAgICAgICBmaWVsZHM6ICcqLEV2ZW50X0NhdGVnb3JpZXMuQ2F0ZWdvcnknLFxuICAgICAgfSxcbiAgICB9KTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfSxcblxuICBnZXRFdmVudDogYXN5bmMgKGlkOiBudW1iZXIpID0+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGRpcmVjdHVzLmdldChgL0V2ZW50cy8ke2lkfWAsIHtcbiAgICAgIHBhcmFtczoge1xuICAgICAgICBmaWVsZHM6ICcqLEV2ZW50X0NhdGVnb3JpZXMuQ2F0ZWdvcnknLFxuICAgICAgfSxcbiAgICB9KTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfSxcblxuICBjcmVhdGVFdmVudDogYXN5bmMgKGV2ZW50RGF0YTogUGFydGlhbDxEaXJlY3R1c0V2ZW50PikgPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZGlyZWN0dXMucG9zdCgnL0V2ZW50cycsIGV2ZW50RGF0YSk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH0sXG5cbiAgLy8gQmFubmVyIFNsaWRlcnNcbiAgZ2V0QmFubmVyU2xpZGVyczogYXN5bmMgKHN0YXR1cyA9ICdwdWJsaXNoZWQnKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCBkaXJlY3R1c0ZldGNoKCdCYW5uZXJfU2xpZGVyJywge1xuICAgICAgICAnZmlsdGVyW3N0YXR1c11bX2VxXSc6IHN0YXR1cyxcbiAgICAgICAgJ3NvcnQnOiAnaWQnLFxuICAgICAgICAnZmllbGRzJzogJyonXG4gICAgICB9KTtcbiAgICAgIGNvbnNvbGUubG9nKCdCYW5uZXIgc2xpZGVycyByZXNwb25zZTonLCBkYXRhKTtcbiAgICAgIHJldHVybiBkYXRhO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBiYW5uZXIgc2xpZGVyczonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH0sXG5cbiAgLy8gRmVhdHVyZXNcbiAgZ2V0RmVhdHVyZXM6IGFzeW5jIChzdGF0dXMgPSAncHVibGlzaGVkJykgPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZGlyZWN0dXMuZ2V0KCcvRmVhdHVyZXMnLCB7XG4gICAgICBwYXJhbXM6IHtcbiAgICAgICAgJ2ZpbHRlcltzdGF0dXNdW19lcV0nOiBzdGF0dXMsXG4gICAgICAgIHNvcnQ6ICdpZCcsXG4gICAgICAgIGZpZWxkczogJyonLFxuICAgICAgfSxcbiAgICB9KTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfSxcblxuICAvLyBUZXN0aW1vbmlhbHNcbiAgZ2V0VGVzdGltb25pYWxzOiBhc3luYyAoc3RhdHVzID0gJ3B1Ymxpc2hlZCcpID0+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGRpcmVjdHVzLmdldCgnL1Rlc3RpbW9uaWFscycsIHtcbiAgICAgIHBhcmFtczoge1xuICAgICAgICAnZmlsdGVyW3N0YXR1c11bX2VxXSc6IHN0YXR1cyxcbiAgICAgICAgc29ydDogJ2lkJyxcbiAgICAgICAgZmllbGRzOiAnKix1c2VyLmZpcnN0X25hbWUsdXNlci5sYXN0X25hbWUsdXNlci5hdmF0YXInLFxuICAgICAgfSxcbiAgICB9KTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfSxcblxuICAvLyBTb2NpYWwgTWVkaWFcbiAgZ2V0U29jaWFsTWVkaWE6IGFzeW5jIChzdGF0dXMgPSAncHVibGlzaGVkJykgPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZGlyZWN0dXMuZ2V0KCcvU29jaWFsX21lZGlhJywge1xuICAgICAgcGFyYW1zOiB7XG4gICAgICAgICdmaWx0ZXJbc3RhdHVzXVtfZXFdJzogc3RhdHVzLFxuICAgICAgICBzb3J0OiAnaWQnLFxuICAgICAgICBmaWVsZHM6ICcqJyxcbiAgICAgIH0sXG4gICAgfSk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH0sXG5cbiAgLy8gQ29tbWVudHNcbiAgZ2V0Q29tbWVudHM6IGFzeW5jIChwb3N0SWQ/OiBudW1iZXIsIHN0YXR1cyA9ICdwdWJsaXNoZWQnKSA9PiB7XG4gICAgY29uc3QgcGFyYW1zOiBhbnkgPSB7XG4gICAgICAnZmlsdGVyW3N0YXR1c11bX2VxXSc6IHN0YXR1cyxcbiAgICAgIHNvcnQ6ICdkYXRlX2NyZWF0ZWQnLFxuICAgICAgZmllbGRzOiAnKix1c2VyLmZpcnN0X25hbWUsdXNlci5sYXN0X25hbWUsdXNlci5hdmF0YXInLFxuICAgIH07XG5cbiAgICBpZiAocG9zdElkKSB7XG4gICAgICBwYXJhbXNbJ2ZpbHRlcltwb3N0XVtfZXFdJ10gPSBwb3N0SWQ7XG4gICAgfVxuXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBkaXJlY3R1cy5nZXQoJy9jb21tZW50cycsIHsgcGFyYW1zIH0pO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9LFxuXG4gIGNyZWF0ZUNvbW1lbnQ6IGFzeW5jIChjb21tZW50RGF0YTogUGFydGlhbDxEaXJlY3R1c0NvbW1lbnQ+KSA9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBkaXJlY3R1cy5wb3N0KCcvY29tbWVudHMnLCBjb21tZW50RGF0YSk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH0sXG5cbiAgLy8gVXNlcnNcbiAgZ2V0VXNlclByb2ZpbGU6IGFzeW5jIChpZDogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBkaXJlY3R1cy5nZXQoYC9kaXJlY3R1c191c2Vycy8ke2lkfWApO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9LFxuXG4gIHVwZGF0ZVVzZXJQcm9maWxlOiBhc3luYyAoaWQ6IHN0cmluZywgdXNlckRhdGE6IFBhcnRpYWw8RGlyZWN0dXNVc2VyPikgPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZGlyZWN0dXMucGF0Y2goYC9kaXJlY3R1c191c2Vycy8ke2lkfWAsIHVzZXJEYXRhKTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfSxcblxuICAvLyBRdWljayB0ZXN0IGZ1bmN0aW9uIHRvIHZlcmlmeSBhbGwgY29sbGVjdGlvbnMgYXJlIGFjY2Vzc2libGVcbiAgdGVzdEFsbENvbGxlY3Rpb25zOiBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgcmVzdWx0czogYW55ID0ge307XG4gICAgY29uc3QgY29sbGVjdGlvbnMgPSBbXG4gICAgICAnUG9zdHMnLFxuICAgICAgJ0NhdGVnb3JpZXMnLFxuICAgICAgJ0V2ZW50cycsXG4gICAgICAnRXZlbnRfQ2F0ZWdvcmllcycsXG4gICAgICAnQmFubmVyX1NsaWRlcicsXG4gICAgICAnRmVhdHVyZXMnLFxuICAgICAgJ1Rlc3RpbW9uaWFscycsXG4gICAgICAnU29jaWFsX21lZGlhJ1xuICAgIF07XG5cbiAgICBmb3IgKGNvbnN0IGNvbGxlY3Rpb24gb2YgY29sbGVjdGlvbnMpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZGlyZWN0dXMuZ2V0KGAvJHtjb2xsZWN0aW9ufWAsIHtcbiAgICAgICAgICBwYXJhbXM6IHsgbGltaXQ6IDEgfVxuICAgICAgICB9KTtcbiAgICAgICAgcmVzdWx0c1tjb2xsZWN0aW9uXSA9IHtcbiAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgIGNvdW50OiByZXNwb25zZS5kYXRhLmRhdGE/Lmxlbmd0aCB8fCAwLFxuICAgICAgICAgIHNhbXBsZTogcmVzcG9uc2UuZGF0YS5kYXRhPy5bMF0gfHwgbnVsbFxuICAgICAgICB9O1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgcmVzdWx0c1tjb2xsZWN0aW9uXSA9IHtcbiAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICBlcnJvcjogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnVW5rbm93biBlcnJvcidcbiAgICAgICAgfTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4gcmVzdWx0cztcbiAgfSxcbn07XG5cbi8vIEF1dGhlbnRpY2F0ZWQgQVBJIGZ1bmN0aW9ucyAocmVxdWlyZSB1c2VyIGxvZ2luKVxuZXhwb3J0IGNvbnN0IGF1dGhlbnRpY2F0ZWRBUEkgPSB7XG4gIC8vIENyZWF0ZSBhIG5ldyBwb3N0IChyZXF1aXJlcyBhdXRoZW50aWNhdGlvbilcbiAgY3JlYXRlUG9zdDogYXN5bmMgKHBvc3REYXRhOiB7XG4gICAgVGl0bGU6IHN0cmluZztcbiAgICBEZXNjcmlwdGlvbjogc3RyaW5nO1xuICAgIENhdGVnb3JpZXM/OiBudW1iZXJbXTtcbiAgfSkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBhdXRoRGlyZWN0dXMgPSBnZXRBdXRoZW50aWNhdGVkRGlyZWN0dXMoKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXV0aERpcmVjdHVzLnBvc3QoJy9Qb3N0cycsIHtcbiAgICAgICAgLi4ucG9zdERhdGEsXG4gICAgICAgIHN0YXR1czogJ2RyYWZ0JywgLy8gU3RhcnQgYXMgZHJhZnRcbiAgICAgIH0pO1xuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0NyZWF0ZSBwb3N0IGVycm9yOicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfSxcblxuICAvLyBVcGRhdGUgdXNlcidzIG93biBwb3N0XG4gIHVwZGF0ZVBvc3Q6IGFzeW5jIChpZDogbnVtYmVyLCBwb3N0RGF0YTogUGFydGlhbDxEaXJlY3R1c1Bvc3Q+KSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGF1dGhEaXJlY3R1cyA9IGdldEF1dGhlbnRpY2F0ZWREaXJlY3R1cygpO1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhdXRoRGlyZWN0dXMucGF0Y2goYC9Qb3N0cy8ke2lkfWAsIHBvc3REYXRhKTtcbiAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdVcGRhdGUgcG9zdCBlcnJvcjonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH0sXG5cbiAgLy8gR2V0IHVzZXIncyBvd24gcG9zdHNcbiAgZ2V0VXNlclBvc3RzOiBhc3luYyAobGltaXQgPSAyMCwgb2Zmc2V0ID0gMCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBhdXRoRGlyZWN0dXMgPSBnZXRBdXRoZW50aWNhdGVkRGlyZWN0dXMoKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXV0aERpcmVjdHVzLmdldCgnL1Bvc3RzJywge1xuICAgICAgICBwYXJhbXM6IHtcbiAgICAgICAgICBsaW1pdCxcbiAgICAgICAgICBvZmZzZXQsXG4gICAgICAgICAgc29ydDogJy1kYXRlX2NyZWF0ZWQnLFxuICAgICAgICAgICdmaWx0ZXJbdXNlcl9jcmVhdGVkXVtfZXFdJzogJyRDVVJSRU5UX1VTRVInLFxuICAgICAgICAgIGZpZWxkczogJyosQ2F0ZWdvcmllcy5DYXRlZ29yeScsXG4gICAgICAgIH0sXG4gICAgICB9KTtcbiAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdHZXQgdXNlciBwb3N0cyBlcnJvcjonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH0sXG5cbiAgLy8gVXBkYXRlIHVzZXIgcHJvZmlsZVxuICB1cGRhdGVQcm9maWxlOiBhc3luYyAocHJvZmlsZURhdGE6IHtcbiAgICBmaXJzdF9uYW1lPzogc3RyaW5nO1xuICAgIGxhc3RfbmFtZT86IHN0cmluZztcbiAgICBhdmF0YXI/OiBzdHJpbmc7XG4gIH0pID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgYXV0aERpcmVjdHVzID0gZ2V0QXV0aGVudGljYXRlZERpcmVjdHVzKCk7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF1dGhEaXJlY3R1cy5wYXRjaCgnL3VzZXJzL21lJywgcHJvZmlsZURhdGEpO1xuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1VwZGF0ZSBwcm9maWxlIGVycm9yOicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfSxcbn07Il0sIm5hbWVzIjpbImF4aW9zIiwiRElSRUNUVVNfVVJMIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0RJUkVDVFVTX1VSTCIsIkRJUkVDVFVTX1RPS0VOIiwiTkVYVF9QVUJMSUNfRElSRUNUVVNfVE9LRU4iLCJkaXJlY3R1c0ZldGNoIiwiY29sbGVjdGlvbiIsInBhcmFtcyIsImJhc2VVcmwiLCJxdWVyeVBhcmFtcyIsIk9iamVjdCIsImVudHJpZXMiLCJtYXAiLCJrZXkiLCJ2YWx1ZSIsImluY2x1ZGVzIiwiZW5jb2RlZEtleSIsInJlcGxhY2UiLCJlbmNvZGVVUklDb21wb25lbnQiLCJqb2luIiwidXJsIiwicmVzcG9uc2UiLCJmZXRjaCIsImhlYWRlcnMiLCJvayIsIkVycm9yIiwic3RhdHVzIiwianNvbiIsImRpcmVjdHVzIiwiY3JlYXRlIiwiYmFzZVVSTCIsImNyZWF0ZUF1dGhlbnRpY2F0ZWREaXJlY3R1cyIsInVzZXJUb2tlbiIsImdldEF1dGhlbnRpY2F0ZWREaXJlY3R1cyIsInRva2VuIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImF1dGgiLCJ0ZXN0Q29ubmVjdGlvbiIsInNlcnZlclJlc3BvbnNlIiwiZ2V0IiwiY29uc29sZSIsImxvZyIsImRhdGEiLCJ0ZXN0UmVzdWx0cyIsInNlcnZlciIsImNvbGxlY3Rpb25zIiwiY29sbGVjdGlvbnNUb1Rlc3QiLCJsaW1pdCIsImZpZWxkcyIsInN1Y2Nlc3MiLCJjb3VudCIsImxlbmd0aCIsImNvbGxlY3Rpb25FcnJvciIsImVycm9yIiwibWVzc2FnZSIsImF1dGhBUEkiLCJsb2dpbiIsImVtYWlsIiwicGFzc3dvcmQiLCJtZXRob2QiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsImVycm9ycyIsInJlZ2lzdGVyIiwidXNlckRhdGEiLCJyb2xlc1Jlc3BvbnNlIiwid3JpdGVyUm9sZUlkIiwicm9sZXNEYXRhIiwiaWQiLCJ1c2VyUGF5bG9hZCIsInJvbGUiLCJnZXRDdXJyZW50VXNlciIsInJlZnJlc2giLCJyZWZyZXNoVG9rZW4iLCJyZWZyZXNoX3Rva2VuIiwibG9nb3V0IiwiYXBpIiwiZ2V0UG9zdHMiLCJvZmZzZXQiLCJjYXRlZ29yeUlkIiwiaXNBdXRoZW50aWNhdGVkIiwic29ydCIsInBvc3RzIiwiQXJyYXkiLCJpc0FycmF5IiwiZmlsdGVyIiwicG9zdCIsIklzX1B1YmxpYyIsImZhbGxiYWNrRXJyb3IiLCJnZXRQb3N0IiwiY3JlYXRlUG9zdCIsInBvc3REYXRhIiwidXBkYXRlUG9zdCIsInBhdGNoIiwiZGVsZXRlUG9zdCIsImRlbGV0ZSIsImdldENhdGVnb3JpZXMiLCJnZXRDYXRlZ29yaWVzV2l0aENvdW50cyIsImNhdGVnb3JpZXNSZXNwb25zZSIsImNhdGVnb3JpZXNEYXRhIiwiY2F0ZWdvcmllc1dpdGhDb3VudHMiLCJjYXRlZ29yeSIsInBvc3RDb3VudCIsIkNhdGVnb3J5IiwiZmFsbGJhY2tSZXNwb25zZSIsImZhbGxiYWNrRGF0YSIsImNhdGVnb3JpZXNXaXRoWmVyb0NvdW50cyIsImNhdCIsImNyZWF0ZUNhdGVnb3J5IiwiY2F0ZWdvcnlEYXRhIiwiZ2V0RXZlbnRzIiwiZ2V0RXZlbnQiLCJjcmVhdGVFdmVudCIsImV2ZW50RGF0YSIsImdldEJhbm5lclNsaWRlcnMiLCJnZXRGZWF0dXJlcyIsImdldFRlc3RpbW9uaWFscyIsImdldFNvY2lhbE1lZGlhIiwiZ2V0Q29tbWVudHMiLCJwb3N0SWQiLCJjcmVhdGVDb21tZW50IiwiY29tbWVudERhdGEiLCJnZXRVc2VyUHJvZmlsZSIsInVwZGF0ZVVzZXJQcm9maWxlIiwidGVzdEFsbENvbGxlY3Rpb25zIiwicmVzdWx0cyIsInNhbXBsZSIsImF1dGhlbnRpY2F0ZWRBUEkiLCJhdXRoRGlyZWN0dXMiLCJnZXRVc2VyUG9zdHMiLCJ1cGRhdGVQcm9maWxlIiwicHJvZmlsZURhdGEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/directus.ts\n"));

/***/ })

});