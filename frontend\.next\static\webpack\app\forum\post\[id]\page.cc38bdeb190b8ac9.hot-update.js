"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forum/post/[id]/page",{

/***/ "(app-pages-browser)/./app/forum/post/[id]/page.tsx":
/*!**************************************!*\
  !*** ./app/forum/post/[id]/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PostDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/header */ \"(app-pages-browser)/./components/header.tsx\");\n/* harmony import */ var _components_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/sidebar */ \"(app-pages-browser)/./components/sidebar.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _barrel_optimize_names_Heart_MessageCircle_Share2_ArrowLeft_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MessageCircle,Share2,ArrowLeft,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_MessageCircle_Share2_ArrowLeft_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MessageCircle,Share2,ArrowLeft,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_MessageCircle_Share2_ArrowLeft_Send_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MessageCircle,Share2,ArrowLeft,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_MessageCircle_Share2_ArrowLeft_Send_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MessageCircle,Share2,ArrowLeft,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_MessageCircle_Share2_ArrowLeft_Send_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MessageCircle,Share2,ArrowLeft,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var date_fns_formatDistanceToNow__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! date-fns/formatDistanceToNow */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.mjs\");\n/* harmony import */ var _lib_directus__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/directus */ \"(app-pages-browser)/./lib/directus.ts\");\n/* harmony import */ var _components_auth_provider__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/auth-provider */ \"(app-pages-browser)/./components/auth-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to generate dummy avatar with first letter\nconst generateDummyAvatar = (firstName)=>{\n    var _firstName_charAt;\n    const letter = (firstName === null || firstName === void 0 ? void 0 : (_firstName_charAt = firstName.charAt(0)) === null || _firstName_charAt === void 0 ? void 0 : _firstName_charAt.toUpperCase()) || \"U\";\n    const colors = [\n        \"bg-gradient-to-br from-orange-400 to-red-500\",\n        \"bg-gradient-to-br from-amber-400 to-orange-500\",\n        \"bg-gradient-to-br from-red-400 to-pink-500\",\n        \"bg-gradient-to-br from-yellow-400 to-amber-500\",\n        \"bg-gradient-to-br from-pink-400 to-red-500\",\n        \"bg-gradient-to-br from-purple-400 to-pink-500\",\n        \"bg-gradient-to-br from-blue-400 to-purple-500\",\n        \"bg-gradient-to-br from-green-400 to-blue-500\"\n    ];\n    const colorIndex = letter.charCodeAt(0) % colors.length;\n    const gradientClass = colors[colorIndex];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-10 h-10 rounded-full \".concat(gradientClass, \" flex items-center justify-center text-white font-bold text-sm shadow-lg\"),\n        children: letter\n    }, void 0, false, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n// Helper function to get avatar URL from Directus\nconst getAvatarUrl = (avatarId)=>{\n    const directusUrl = \"http://localhost:8055\" || 0;\n    return \"\".concat(directusUrl, \"/assets/\").concat(avatarId);\n};\nfunction PostDetailPage() {\n    var _post_Categories;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const postId = params.id;\n    const { user: authUser, isAuthenticated } = (0,_components_auth_provider__WEBPACK_IMPORTED_MODULE_12__.useAuth)();\n    const [post, setPost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newComment, setNewComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [submittingComment, setSubmittingComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchPostAndComments = async ()=>{\n            try {\n                setLoading(true);\n                // Fetch post details\n                const postResponse = await _lib_directus__WEBPACK_IMPORTED_MODULE_11__.api.getPost(parseInt(postId), isAuthenticated);\n                if (postResponse && postResponse.data) {\n                    setPost(postResponse.data);\n                }\n                // Fetch comments for this post\n                const commentsResponse = await _lib_directus__WEBPACK_IMPORTED_MODULE_11__.api.getComments(parseInt(postId));\n                if (commentsResponse && commentsResponse.data) {\n                    setComments(commentsResponse.data);\n                }\n            } catch (err) {\n                console.error(\"Error fetching post details:\", err);\n                setError(\"Failed to load post details\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (postId) {\n            fetchPostAndComments();\n        }\n    }, [\n        postId,\n        isAuthenticated\n    ]);\n    const handleSubmitComment = async (e)=>{\n        e.preventDefault();\n        if (!newComment.trim() || !isAuthenticated) return;\n        try {\n            setSubmittingComment(true);\n            // Use authenticated API to create comment\n            const authDirectus = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/directus */ \"(app-pages-browser)/./lib/directus.ts\")).then((m)=>m.getAuthenticatedDirectus());\n            await authDirectus.post(\"/comments\", {\n                comment: newComment,\n                post: parseInt(postId),\n                user: authUser === null || authUser === void 0 ? void 0 : authUser.id,\n                status: \"published\"\n            });\n            // Refresh comments\n            const commentsResponse = await _lib_directus__WEBPACK_IMPORTED_MODULE_11__.api.getComments(parseInt(postId));\n            if (commentsResponse && commentsResponse.data) {\n                setComments(commentsResponse.data);\n            }\n            setNewComment(\"\");\n        } catch (err) {\n            console.error(\"Error submitting comment:\", err);\n            alert(\"Failed to submit comment. Please try again.\");\n        } finally{\n            setSubmittingComment(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header__WEBPACK_IMPORTED_MODULE_4__.Header, {}, void 0, false, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar__WEBPACK_IMPORTED_MODULE_5__.Sidebar, {}, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 lg:ml-64\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"container mx-auto px-4 py-6 max-w-4xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-pulse space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 bg-gray-300 rounded w-1/4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white dark:bg-gray-800 rounded-lg border p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-gray-300 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-4 bg-gray-300 rounded w-32\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 141,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-3 bg-gray-300 rounded w-24\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 142,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 bg-gray-300 rounded w-3/4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 bg-gray-300 rounded w-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 147,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 bg-gray-300 rounded w-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 148,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 bg-gray-300 rounded w-2/3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n            lineNumber: 128,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !post) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header__WEBPACK_IMPORTED_MODULE_4__.Header, {}, void 0, false, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar__WEBPACK_IMPORTED_MODULE_5__.Sidebar, {}, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 lg:ml-64\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"container mx-auto px-4 py-6 max-w-4xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-600 dark:text-red-400\",\n                                            children: error || \"Post not found\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/forum\",\n                                            className: \"mt-2 text-red-600 dark:text-red-400 underline hover:no-underline\",\n                                            children: \"Back to Forum\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n            lineNumber: 163,\n            columnNumber: 7\n        }, this);\n    }\n    const postUser = typeof post.user === \"object\" ? post.user : null;\n    const userName = postUser ? \"\".concat(postUser.first_name || \"\", \" \").concat(postUser.last_name || \"\").trim() : \"Anonymous User\";\n    const firstName = (postUser === null || postUser === void 0 ? void 0 : postUser.first_name) || \"Anonymous\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header__WEBPACK_IMPORTED_MODULE_4__.Header, {}, void 0, false, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar__WEBPACK_IMPORTED_MODULE_5__.Sidebar, {}, void 0, false, {\n                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 lg:ml-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-6 max-w-4xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/forum\",\n                                    className: \"inline-flex items-center text-muted-foreground hover:text-foreground mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MessageCircle_Share2_ArrowLeft_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Back to Forum\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                    className: \"mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        (postUser === null || postUser === void 0 ? void 0 : postUser.avatar) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_9__.Avatar, {\n                                                            className: \"h-12 w-12\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_9__.AvatarImage, {\n                                                                    src: getAvatarUrl(postUser.avatar),\n                                                                    alt: userName\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 206,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_9__.AvatarFallback, {\n                                                                    children: firstName.charAt(0).toUpperCase()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 207,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 23\n                                                        }, this) : generateDummyAvatar(firstName),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-foreground\",\n                                                                            children: userName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 214,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                            variant: \"outline\",\n                                                                            className: \"text-xs\",\n                                                                            children: \"Community Member\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 215,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 213,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mt-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: (0,date_fns_formatDistanceToNow__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(new Date(post.date_created || \"\"), {\n                                                                                addSuffix: true\n                                                                            })\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 220,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        ((_post_Categories = post.Categories) === null || _post_Categories === void 0 ? void 0 : _post_Categories[0]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"•\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 225,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                                    variant: \"secondary\",\n                                                                                    className: \"text-xs\",\n                                                                                    children: post.Categories[0].Category\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 226,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold text-foreground mb-4\",\n                                                    children: post.Title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"prose prose-gray dark:prose-invert max-w-none\",\n                                                    dangerouslySetInnerHTML: {\n                                                        __html: post.Description\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 17\n                                                }, this),\n                                                post.Tags && post.Tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2 mt-6\",\n                                                    children: post.Tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"text-xs\",\n                                                            children: [\n                                                                \"#\",\n                                                                tag\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 mt-6 pt-4 border-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MessageCircle_Share2_ArrowLeft_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 255,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 256,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MessageCircle_Share2_ArrowLeft_Send_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: comments.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 261,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MessageCircle_Share2_ArrowLeft_Send_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold\",\n                                            children: [\n                                                \"Comments (\",\n                                                comments.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                className: \"pt-6\",\n                                                children: isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleSubmitComment,\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                                                            placeholder: \"Write a comment...\",\n                                                            value: newComment,\n                                                            onChange: (e)=>setNewComment(e.target.value),\n                                                            className: \"min-h-[100px]\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-end\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                type: \"submit\",\n                                                                disabled: submittingComment || !newComment.trim(),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MessageCircle_Share2_ArrowLeft_Send_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 288,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    submittingComment ? \"Posting...\" : \"Post Comment\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground mb-4\",\n                                                            children: \"You need to be logged in to post a comment.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                            onClick: ()=>window.location.href = \"/auth-test\",\n                                                            children: \"Sign In\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this),\n                                        comments.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                className: \"pt-6 text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"No comments yet. Be the first to comment!\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: comments.map((comment)=>{\n                                                const commentUser = typeof comment.user === \"object\" ? comment.user : null;\n                                                const commentUserName = commentUser ? \"\".concat(commentUser.first_name || \"\", \" \").concat(commentUser.last_name || \"\").trim() : \"Anonymous User\";\n                                                const commentFirstName = (commentUser === null || commentUser === void 0 ? void 0 : commentUser.first_name) || \"Anonymous\";\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                        className: \"pt-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-3\",\n                                                            children: [\n                                                                (commentUser === null || commentUser === void 0 ? void 0 : commentUser.avatar) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_9__.Avatar, {\n                                                                    className: \"h-8 w-8\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_9__.AvatarImage, {\n                                                                            src: getAvatarUrl(commentUser.avatar),\n                                                                            alt: commentUserName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 324,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_9__.AvatarFallback, {\n                                                                            children: commentFirstName.charAt(0).toUpperCase()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 325,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 31\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8\",\n                                                                    children: generateDummyAvatar(commentFirstName)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium text-sm\",\n                                                                                    children: commentUserName\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 334,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: (0,date_fns_formatDistanceToNow__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(new Date(comment.date_created || \"\"), {\n                                                                                        addSuffix: true\n                                                                                    })\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 335,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 333,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: comment.comment\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 339,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 332,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, comment.id, false, {\n                                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\app\\\\forum\\\\post\\\\[id]\\\\page.tsx\",\n        lineNumber: 187,\n        columnNumber: 5\n    }, this);\n}\n_s(PostDetailPage, \"7BlwNFsW6pKbbmy4/9L0tpLG0Ng=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _components_auth_provider__WEBPACK_IMPORTED_MODULE_12__.useAuth\n    ];\n});\n_c = PostDetailPage;\nvar _c;\n$RefreshReg$(_c, \"PostDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/forum/post/[id]/page.tsx\n"));

/***/ })

});