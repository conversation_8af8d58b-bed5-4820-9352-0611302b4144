"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/forum/post/[id]/page",{

/***/ "(app-pages-browser)/./components/login-dialog.tsx":
/*!*************************************!*\
  !*** ./components/login-dialog.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoginDialog: function() { return /* binding */ LoginDialog; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _auth_provider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./auth-provider */ \"(app-pages-browser)/./components/auth-provider.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ LoginDialog auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction LoginDialog(param) {\n    let { open, onOpenChange } = param;\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [firstName, setFirstName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [lastName, setLastName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [confirmPassword, setConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"login\");\n    const { login, register } = (0,_auth_provider__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const resetForm = ()=>{\n        setEmail(\"\");\n        setPassword(\"\");\n        setFirstName(\"\");\n        setLastName(\"\");\n        setConfirmPassword(\"\");\n    };\n    const handleLogin = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        try {\n            await login(email, password);\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"Successfully logged in!\");\n            onOpenChange(false);\n            resetForm();\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Login failed. Please check your credentials.\";\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(errorMessage);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleRegister = async (e)=>{\n        e.preventDefault();\n        if (password !== confirmPassword) {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Passwords do not match\");\n            return;\n        }\n        if (password.length < 8) {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Password must be at least 8 characters long\");\n            return;\n        }\n        setIsLoading(true);\n        try {\n            await register({\n                email,\n                password,\n                first_name: firstName,\n                last_name: lastName\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"Account created successfully! Your account is inactive and will require admin approval. You can try logging in once your account is activated.\");\n            onOpenChange(false);\n            resetForm();\n            // Switch to login tab in case user wants to try logging in\n            setActiveTab(\"login\");\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Registration failed. Please try again.\";\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(errorMessage);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"sm:max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                            children: \"Welcome Back\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogDescription, {\n                            children: \"Sign in to your account to continue\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                            className: \"grid w-full grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"login\",\n                                    children: \"Sign In\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"register\",\n                                    children: \"Sign Up\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"login\",\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleLogin,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"email\",\n                                                children: \"Email\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"email\",\n                                                type: \"email\",\n                                                placeholder: \"Enter your email\",\n                                                value: email,\n                                                onChange: (e)=>setEmail(e.target.value),\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"password\",\n                                                children: \"Password\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"password\",\n                                                type: \"password\",\n                                                placeholder: \"Enter your password\",\n                                                value: password,\n                                                onChange: (e)=>setPassword(e.target.value),\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"submit\",\n                                        className: \"w-full\",\n                                        disabled: isLoading,\n                                        children: isLoading ? \"Signing in...\" : \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"register\",\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleRegister,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"first-name\",\n                                                        children: \"First Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"first-name\",\n                                                        type: \"text\",\n                                                        placeholder: \"First name\",\n                                                        value: firstName,\n                                                        onChange: (e)=>setFirstName(e.target.value),\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"last-name\",\n                                                        children: \"Last Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"last-name\",\n                                                        type: \"text\",\n                                                        placeholder: \"Last name\",\n                                                        value: lastName,\n                                                        onChange: (e)=>setLastName(e.target.value),\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"reg-email\",\n                                                children: \"Email\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"reg-email\",\n                                                type: \"email\",\n                                                placeholder: \"Enter your email\",\n                                                value: email,\n                                                onChange: (e)=>setEmail(e.target.value),\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"reg-password\",\n                                                children: \"Password\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"reg-password\",\n                                                type: \"password\",\n                                                placeholder: \"Create a password (min 8 characters)\",\n                                                value: password,\n                                                onChange: (e)=>setPassword(e.target.value),\n                                                required: true,\n                                                minLength: 8\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"confirm-password\",\n                                                children: \"Confirm Password\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"confirm-password\",\n                                                type: \"password\",\n                                                placeholder: \"Confirm your password\",\n                                                value: confirmPassword,\n                                                onChange: (e)=>setConfirmPassword(e.target.value),\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: \"By creating an account, you'll be able to create posts and participate in discussions. However, your account will be inactive initially and will require admin approval.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"submit\",\n                                        className: \"w-full\",\n                                        disabled: isLoading,\n                                        children: isLoading ? \"Creating Account...\" : \"Create Account\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n            lineNumber: 95,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\shakti\\\\mandir\\\\frontend\\\\components\\\\login-dialog.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginDialog, \"FfztV84QDtC+EKQxdZeqbvn5vsA=\", false, function() {\n    return [\n        _auth_provider__WEBPACK_IMPORTED_MODULE_7__.useAuth\n    ];\n});\n_c = LoginDialog;\nvar _c;\n$RefreshReg$(_c, \"LoginDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/login-dialog.tsx\n"));

/***/ })

});