'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/components/auth-provider';
import { toast } from 'sonner';

export default function AuthTestPage() {
  const { user, login, register, logout, isLoading, isAuthenticated } = useAuth();
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password123');
  const [firstName, setFirstName] = useState('Test');
  const [lastName, setLastName] = useState('User');

  const handleTestLogin = async () => {
    try {
      await login(email, password);
      toast.success('Login successful!');
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Login failed');
    }
  };

  const handleTestRegister = async () => {
    try {
      await register({
        email,
        password,
        first_name: firstName,
        last_name: lastName,
      });
      toast.success('Registration successful!');
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Registration failed');
    }
  };

  const handleLogout = () => {
    logout();
    toast.success('Logged out successfully!');
  };

  return (
    <div className="container mx-auto p-6">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Authentication Test</CardTitle>
          <CardDescription>
            Test Directus JWT authentication with login and registration
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Current Auth Status */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium mb-2">Current Authentication Status</h3>
            <div className="space-y-1 text-sm">
              <p><strong>Authenticated:</strong> {isAuthenticated ? '✅ Yes' : '❌ No'}</p>
              <p><strong>Loading:</strong> {isLoading ? '⏳ Yes' : '✅ No'}</p>
              {user && (
                <div className="mt-2">
                  <p><strong>User Info:</strong></p>
                  <pre className="bg-white p-2 rounded text-xs overflow-auto">
                    {JSON.stringify(user, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>

          {/* Test Forms */}
          {!isAuthenticated ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">First Name</Label>
                  <Input
                    id="firstName"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    placeholder="First name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input
                    id="lastName"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    placeholder="Last name"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Email address"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Password"
                />
              </div>

              <div className="flex gap-2">
                <Button 
                  onClick={handleTestLogin} 
                  disabled={isLoading}
                  variant="outline"
                  className="flex-1"
                >
                  {isLoading ? 'Logging in...' : 'Test Login'}
                </Button>
                <Button 
                  onClick={handleTestRegister} 
                  disabled={isLoading}
                  className="flex-1"
                >
                  {isLoading ? 'Registering...' : 'Test Register'}
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center space-y-4">
              <p className="text-green-600 font-medium">
                ✅ Successfully authenticated as {user?.first_name} {user?.last_name}
              </p>
              <Button onClick={handleLogout} variant="outline">
                Logout
              </Button>
            </div>
          )}

          {/* Instructions */}
          <div className="p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium mb-2">Test Instructions:</h4>
            <ul className="text-sm space-y-1 list-disc list-inside">
              <li>Try registering a new user - they will be assigned the "Writer" role</li>
              <li>Test login with existing credentials</li>
              <li>Check that JWT tokens are stored in localStorage</li>
              <li>Verify that user data is fetched correctly</li>
              <li>Test logout functionality</li>
            </ul>
          </div>

          <div className="mt-6">
            <Button 
              onClick={() => window.location.href = '/'}
              variant="outline"
            >
              ← Back to Homepage
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
