'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { api } from '@/lib/directus';

export default function BannerTestPage() {
  const [banners, setBanners] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBanners = async () => {
      try {
        setLoading(true);
        const response = await api.getBannerSliders('published');
        console.log('Banner response:', response);
        setBanners(response.data || []);
      } catch (err) {
        console.error('Error fetching banners:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchBanners();
  }, []);

  const getImageUrl = (imageId: string) => {
    const directusUrl = process.env.NEXT_PUBLIC_DIRECTUS_URL || 'http://localhost:8055';
    return `${directusUrl}/assets/${imageId}`;
  };

  return (
    <div className="container mx-auto p-6">
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Banner Slider Test</CardTitle>
          <CardDescription>
            Testing the banner slider images from Directus
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {loading && (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
              <span className="ml-2">Loading banners...</span>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 rounded p-4">
              <h3 className="font-medium text-red-800">Error loading banners:</h3>
              <p className="text-red-600">{error}</p>
            </div>
          )}

          {!loading && !error && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">
                Found {banners.length} banner(s)
              </h3>
              
              {banners.length === 0 ? (
                <p className="text-gray-600">No banners found in Directus.</p>
              ) : (
                <div className="grid gap-4">
                  {banners.map((banner, index) => (
                    <div key={banner.id} className="border rounded-lg p-4">
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0">
                          <img
                            src={getImageUrl(banner.Slider_image)}
                            alt={banner.Title || `Banner ${index + 1}`}
                            className="w-32 h-20 object-cover rounded"
                            onError={(e) => {
                              console.error('Image failed to load:', getImageUrl(banner.Slider_image));
                              e.currentTarget.style.display = 'none';
                            }}
                          />
                        </div>
                        {/* <div className="flex-1">
                          <h4 className="font-medium">{banner.Title || `Banner ${index + 1}`}</h4>
                          <p className="text-sm text-gray-600">ID: {banner.id}</p>
                          <p className="text-sm text-gray-600">Status: {banner.status}</p>
                          <p className="text-sm text-gray-600">Image ID: {banner.Slider_image}</p>
                          <p className="text-sm text-blue-600 break-all">
                            URL: {getImageUrl(banner.Slider_image)}
                          </p>
                        </div> */}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              <div className="mt-6 p-4 bg-gray-50 rounded">
                <h4 className="font-medium mb-2">Raw API Response:</h4>
                <pre className="text-xs overflow-auto bg-white p-2 rounded border">
                  {JSON.stringify({ data: banners }, null, 2)}
                </pre>
              </div>
            </div>
          )}

          <div className="mt-6">
            <Button 
              onClick={() => window.location.href = '/'}
              variant="outline"
            >
              ← Back to Homepage
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
