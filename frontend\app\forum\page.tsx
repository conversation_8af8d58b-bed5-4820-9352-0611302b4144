'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Header } from '@/components/header';
import { Sidebar } from '@/components/sidebar';
import { PostsFeed } from '@/components/posts-feed';
import { FloatingActionButton } from '@/components/floating-action-button';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

export default function ForumPage() {
  const searchParams = useSearchParams();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [categoryName, setCategoryName] = useState<string>('');

  useEffect(() => {
    const category = searchParams.get('category');
    const name = searchParams.get('categoryName');
    setSelectedCategory(category);
    setCategoryName(name || '');
  }, [searchParams]);

  const clearFilter = () => {
    setSelectedCategory(null);
    setCategoryName('');
    // Update URL without category filter
    window.history.pushState({}, '', '/forum');
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="flex">
        <Sidebar />
        <main className="flex-1 lg:ml-64">
          <div className="container mx-auto px-4 py-6 max-w-4xl">
            <div className="mb-6">
              <h1 className="text-3xl font-bold text-foreground mb-2">Community Forum</h1>
              <p className="text-muted-foreground">Connect, share, and discuss with our community</p>

              {/* Category Filter Display */}
              {selectedCategory && (
                <div className="flex items-center gap-2 mt-4">
                  <span className="text-sm text-muted-foreground">Filtered by:</span>
                  <Badge variant="secondary" className="flex items-center gap-2">
                    {categoryName}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 hover:bg-transparent"
                      onClick={clearFilter}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                </div>
              )}
            </div>
            <PostsFeed selectedCategory={selectedCategory} />
          </div>
        </main>
      </div>
      <FloatingActionButton />
    </div>
  );
}