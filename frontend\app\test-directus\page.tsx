'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { testConnection, api } from '@/lib/directus';

export default function TestDirectusPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [apiResult, setApiResult] = useState<any>(null);
  const [apiLoading, setApiLoading] = useState(false);

  const handleTest = async () => {
    setLoading(true);
    try {
      const testResult = await testConnection();
      setResult(testResult);
    } catch (error) {
      setResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleApiTest = async () => {
    setApiLoading(true);
    try {
      const apiTestResult = await api.testAllCollections();
      setApiResult(apiTestResult);
    } catch (error) {
      setApiResult({
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    } finally {
      setApiLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Directus Connection Test</CardTitle>
          <CardDescription>
            Test the connection to your Directus instance and view available collections
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={handleTest} disabled={loading}>
              {loading ? 'Testing...' : 'Test Connection'}
            </Button>
            <Button onClick={handleApiTest} disabled={apiLoading} variant="outline">
              {apiLoading ? 'Testing API...' : 'Test API Functions'}
            </Button>
          </div>

          {result && (
            <div className="mt-4">
              <h3 className="text-lg font-semibold mb-2">
                {result.success ? '✅ Connection Successful' : '❌ Connection Failed'}
              </h3>
              
              {result.success ? (
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium">Server Information:</h4>
                    <div className="bg-gray-100 p-3 rounded text-sm">
                      <p><strong>Project:</strong> {result.server?.data?.project?.project_name || 'Unknown'}</p>
                      <p><strong>Version:</strong> {result.server?.data?.directus?.version || 'Unknown'}</p>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium">Collections Test Results:</h4>
                    <div className="space-y-2">
                      {Object.entries(result.collections || {}).map(([collection, data]: [string, any]) => (
                        <div key={collection} className="bg-gray-50 p-3 rounded">
                          <div className="flex items-center justify-between">
                            <span className="font-medium">{collection}</span>
                            <span className={`px-2 py-1 rounded text-xs ${
                              data.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {data.success ? '✅ Success' : '❌ Failed'}
                            </span>
                          </div>
                          {data.success ? (
                            <p className="text-sm text-gray-600 mt-1">
                              Found {data.count} items
                            </p>
                          ) : (
                            <p className="text-sm text-red-600 mt-1">
                              Error: {data.error}
                            </p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium">Raw Response (for debugging):</h4>
                    <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto max-h-40">
                      {JSON.stringify(result, null, 2)}
                    </pre>
                  </div>
                </div>
              ) : (
                <div>
                  <h4 className="font-medium text-red-600">Error:</h4>
                  <pre className="bg-red-50 p-3 rounded text-sm text-red-700">
                    {result.error}
                  </pre>
                </div>
              )}
            </div>
          )}

          {apiResult && (
            <div className="mt-6">
              <h3 className="text-lg font-semibold mb-2">
                🔧 API Functions Test Results
              </h3>
              <div className="space-y-2">
                {Object.entries(apiResult).map(([collection, data]: [string, any]) => (
                  <div key={collection} className="bg-gray-50 p-3 rounded">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{collection}</span>
                      <span className={`px-2 py-1 rounded text-xs ${
                        data.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {data.success ? '✅ Success' : '❌ Failed'}
                      </span>
                    </div>
                    {data.success ? (
                      <div className="text-sm text-gray-600 mt-1">
                        <p>Items found: {data.count}</p>
                        {data.sample && (
                          <details className="mt-1">
                            <summary className="cursor-pointer text-blue-600">View sample data</summary>
                            <pre className="bg-gray-100 p-2 rounded text-xs mt-1 overflow-auto">
                              {JSON.stringify(data.sample, null, 2)}
                            </pre>
                          </details>
                        )}
                      </div>
                    ) : (
                      <p className="text-sm text-red-600 mt-1">
                        Error: {data.error}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
