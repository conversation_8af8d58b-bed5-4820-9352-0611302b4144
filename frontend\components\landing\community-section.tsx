'use client';

import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { MessageCircle, Heart, Users, ArrowRight } from 'lucide-react';

const communityPosts = [
  {
    id: 1,
    author: {
      name: '<PERSON><PERSON>',
      avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=40',
      role: 'Devotee'
    },
    content: 'Just finished my morning prayers and feeling so blessed. The Gayatri Mantra always brings such peace to my heart. 🙏',
    category: 'Daily Practice',
    likes: 24,
    comments: 8,
    timeAgo: '2 hours ago'
  },
  {
    id: 2,
    author: {
      name: '<PERSON><PERSON>',
      avatar: 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=40',
      role: 'Scholar'
    },
    content: 'Beautiful discussion on Chapter 2 of the Bhagavad Gita today. The concept of dharma becomes clearer with each reading.',
    category: 'Scripture Study',
    likes: 31,
    comments: 12,
    timeAgo: '4 hours ago'
  },
  {
    id: 3,
    author: {
      name: 'Meera Devi',
      avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=40',
      role: 'Guide'
    },
    content: 'Preparing for Diwali celebrations! Would love to hear how everyone is planning to celebrate this festival of lights.',
    category: 'Festivals',
    likes: 45,
    comments: 18,
    timeAgo: '6 hours ago'
  }
];

export function CommunitySection() {
  return (
    <section className="py-24 bg-gradient-to-b from-orange-50/30 to-background dark:from-orange-950/30 dark:to-background">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left side - Content */}
          <div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
              Join Our Sacred Community
            </h2>
            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
              Connect with thousands of devotees worldwide who share your spiritual journey. 
              Share prayers, seek guidance, and grow together in faith and wisdom.
            </p>

            <div className="space-y-6 mb-8">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center">
                  <Users className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">Supportive Community</h3>
                  <p className="text-muted-foreground">Find encouragement and support from fellow practitioners</p>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center">
                  <MessageCircle className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">Meaningful Discussions</h3>
                  <p className="text-muted-foreground">Engage in deep conversations about spirituality and life</p>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center">
                  <Heart className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">Shared Devotion</h3>
                  <p className="text-muted-foreground">Experience the joy of collective prayer and worship</p>
                </div>
              </div>
            </div>

            <Button size="lg" className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white px-8 py-4 text-lg font-semibold">
              Join Community
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </div>

          {/* Right side - Community Posts */}
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-center mb-8">Recent Community Activity</h3>
            {communityPosts.map((post) => (
              <Card key={post.id} className="bg-white/70 dark:bg-black/20 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={post.author.avatar} alt={post.author.name} />
                      <AvatarFallback>{post.author.name[0]}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className="font-semibold">{post.author.name}</h4>
                        <Badge variant="outline" className="text-xs">
                          {post.author.role}
                        </Badge>
                        <span className="text-xs text-muted-foreground">•</span>
                        <span className="text-xs text-muted-foreground">{post.timeAgo}</span>
                      </div>
                      <p className="text-muted-foreground mb-3 leading-relaxed">
                        {post.content}
                      </p>
                      <div className="flex items-center justify-between">
                        <Badge variant="secondary" className="text-xs">
                          {post.category}
                        </Badge>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <Heart className="h-4 w-4" />
                            <span>{post.likes}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <MessageCircle className="h-4 w-4" />
                            <span>{post.comments}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}