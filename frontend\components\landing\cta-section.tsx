'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowRight, Mail } from 'lucide-react';

export function CallToActionSection() {
  return (
    <section className="py-24 bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-yellow-300 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-white rounded-full blur-2xl animate-pulse delay-500" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center text-white">
          <h2 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
            Begin Your Sacred Journey Today
          </h2>
          <p className="text-xl md:text-2xl mb-12 opacity-90 leading-relaxed">
            Join thousands of devotees in a community where faith flourishes, 
            wisdom is shared, and souls find their spiritual home.
          </p>

          {/* Email signup */}
          <div className="max-w-md mx-auto mb-8">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input 
                  type="email" 
                  placeholder="Enter your email address"
                  className="pl-10 py-3 text-gray-900 bg-white/90 border-0 focus:bg-white"
                />
              </div>
              <Button 
                size="lg" 
                className="bg-white text-orange-600 hover:bg-gray-100 font-semibold px-8 py-3 shadow-lg"
              >
                Join Now
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>

          <p className="text-sm opacity-75 mb-8">
            Free to join • No spam • Unsubscribe anytime
          </p>

          {/* Alternative CTA */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button 
              variant="outline" 
              size="lg" 
              className="border-2 border-white text-white hover:bg-white hover:text-orange-600 px-8 py-3 font-semibold bg-transparent"
            >
              Explore as Guest
            </Button>
            <span className="text-white/70">or</span>
            <Button 
              variant="outline" 
              size="lg" 
              className="border-2 border-white text-white hover:bg-white hover:text-orange-600 px-8 py-3 font-semibold bg-transparent"
            >
              Download App
            </Button>
          </div>

          {/* Sacred mantra */}
          <div className="mt-16 p-6 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20">
            <p className="text-2xl font-bold mb-2">
              "वसुधैव कुटुम्बकम्"
            </p>
            <p className="text-lg opacity-90">
              The World is One Family
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}