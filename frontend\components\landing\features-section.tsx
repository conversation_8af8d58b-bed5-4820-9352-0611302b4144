'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  MessageCircle,
  BookOpen,
  Users,
  Calendar,
  Heart,
  Lightbulb,
  Music,
  Globe,
  Star,
  Zap,
  Shield,
  Target,
  Award,
  Compass,
  Flame,
  Sparkles
} from 'lucide-react';
import { api, DirectusFeature } from '@/lib/directus';

// Icon mapping for Material Design icons to Lucide React icons
const iconMap: Record<string, any> = {
  // Chat/Communication icons
  'chat': MessageCircle,
  'message': MessageCircle,
  'forum': MessageCircle,

  // Book/Study icons
  'menu_book': BookOpen,
  'book': BookOpen,
  'library_books': BookOpen,
  'auto_stories': BookOpen,

  // User/People icons
  'supervised_user_circle': Users,
  'people': Users,
  'group': Users,
  'person': Users,

  // Calendar/Event icons
  'date_range': Calendar,
  'event': Calendar,
  'schedule': Calendar,
  'calendar_today': Calendar,

  // Heart/Prayer icons
  'favorite': Heart,
  'volunteer_activism': Heart,
  'healing': Heart,

  // Wisdom/Light icons
  'lightbulb': Lightbulb,
  'tips_and_updates': Lightbulb,
  'psychology': Lightbulb,

  // Music icons
  'music_note': Music,
  'library_music': Music,
  'queue_music': Music,

  // Global/Community icons
  'public': Globe,
  'language': Globe,
  'travel_explore': Globe,

  // Additional spiritual icons
  'star': Star,
  'auto_awesome': Sparkles,
  'flash_on': Zap,
  'security': Shield,
  'gps_fixed': Target,
  'emoji_events': Award,
  'explore': Compass,
  'local_fire_department': Flame,
};

// Gradient colors array for cycling through
const gradientColors = [
  'from-orange-500 to-red-500',
  'from-amber-500 to-orange-500',
  'from-red-500 to-pink-500',
  'from-yellow-500 to-amber-500',
  'from-pink-500 to-red-500',
  'from-orange-500 to-yellow-500',
  'from-purple-500 to-pink-500',
  'from-blue-500 to-purple-500',
  'from-green-500 to-blue-500',
  'from-indigo-500 to-purple-500',
];

// Function to get icon component from icon name
const getIconComponent = (iconName: string) => {
  return iconMap[iconName] || Star; // Default to Star if icon not found
};

// Function to get gradient for index
const getGradient = (index: number) => {
  return gradientColors[index % gradientColors.length];
};

export function FeaturesSection() {
  const [features, setFeatures] = useState<DirectusFeature[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFeatures = async () => {
      try {
        setLoading(true);
        const response = await api.getFeatures('published');
        if (response && response.data) {
          setFeatures(response.data);
        }
      } catch (err) {
        console.error('Error fetching features:', err);
        setError('Failed to load features');
      } finally {
        setLoading(false);
      }
    };

    fetchFeatures();
  }, []);

  if (loading) {
    return (
      <section className="py-24 bg-gradient-to-b from-background to-orange-50/30 dark:to-orange-950/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
              Spiritual Features
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Discover tools and spaces designed to nurture your spiritual growth and connect you with a community of like-minded souls
            </p>
          </div>
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-24 bg-gradient-to-b from-background to-orange-50/30 dark:to-orange-950/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
              Spiritual Features
            </h2>
            <p className="text-xl text-red-500">
              {error}
            </p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-24 bg-gradient-to-b from-background to-orange-50/30 dark:to-orange-950/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
            Spiritual Features
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Discover tools and spaces designed to nurture your spiritual growth and connect you with a community of like-minded souls
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => {
            const IconComponent = getIconComponent(feature.icon || '');
            const gradient = getGradient(index);

            return (
              <Card
                key={feature.id}
                className="group hover:shadow-xl transition-all duration-300 border-0 bg-white/70 dark:bg-black/20 backdrop-blur-sm hover:scale-105"
              >
                <CardHeader className="text-center pb-4">
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r ${gradient} flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300`}>
                    <IconComponent className="h-8 w-8 text-white" />
                  </div>
                  <CardTitle className="text-lg font-semibold text-foreground group-hover:text-orange-600 transition-colors">
                    {feature.title || 'Feature'}
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="text-muted-foreground leading-relaxed">
                    {feature.description || 'No description available'}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Sacred symbols decoration */}
        <div className="flex justify-center items-center mt-16 space-x-8 opacity-30">
          <span className="text-4xl text-orange-500">🕉️</span>
          <span className="text-4xl text-red-500">🪷</span>
          <span className="text-4xl text-amber-500">🔱</span>
          <span className="text-4xl text-pink-500">🪔</span>
        </div>
      </div>
    </section>
  );
}