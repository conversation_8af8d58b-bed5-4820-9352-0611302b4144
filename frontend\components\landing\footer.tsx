'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  MessageSquare, 
  Mail, 
  Phone, 
  MapPin, 
  Facebook, 
  Twitter, 
  Instagram, 
  Youtube,
  Heart
} from 'lucide-react';

const footerLinks = {
  community: [
    { name: 'Join Discussions', href: '/discussions' },
    { name: 'Prayer Circles', href: '/prayers' },
    { name: 'Scripture Study', href: '/scriptures' },
    { name: 'Festival Calendar', href: '/festivals' }
  ],
  resources: [
    { name: 'Daily Wisdom', href: '/wisdom' },
    { name: 'Meditation Guides', href: '/meditation' },
    { name: 'Devotional Music', href: '/music' },
    { name: 'Sacred Texts', href: '/texts' }
  ],
  support: [
    { name: 'Help Center', href: '/help' },
    { name: 'Community Guidelines', href: '/guidelines' },
    { name: 'Contact Us', href: '/contact' },
    { name: 'Feedback', href: '/feedback' }
  ]
};

export function Footer() {
  return (
    <footer className="bg-gradient-to-b from-background to-orange-50/30 dark:to-orange-950/30 border-t">
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand section */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center">
                <MessageSquare className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-foreground">Sacred Community</h3>
                <p className="text-sm text-muted-foreground">Where Devotion Meets Connection</p>
              </div>
            </div>
            
            <p className="text-muted-foreground mb-6 leading-relaxed">
              A spiritual platform dedicated to bringing together Hindu devotees worldwide, 
              fostering meaningful connections, and nurturing spiritual growth through 
              shared wisdom and collective devotion.
            </p>

            <div className="space-y-3">
              <div className="flex items-center space-x-3 text-muted-foreground">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3 text-muted-foreground">
                <Phone className="h-4 w-4" />
                <span>+****************</span>
              </div>
              <div className="flex items-center space-x-3 text-muted-foreground">
                <MapPin className="h-4 w-4" />
                <span>Global Community</span>
              </div>
            </div>
          </div>

          {/* Community links */}
          <div>
            <h4 className="font-semibold text-foreground mb-4">Community</h4>
            <ul className="space-y-3">
              {footerLinks.community.map((link) => (
                <li key={link.name}>
                  <a 
                    href={link.href} 
                    className="text-muted-foreground hover:text-orange-600 transition-colors"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Resources links */}
          <div>
            <h4 className="font-semibold text-foreground mb-4">Resources</h4>
            <ul className="space-y-3">
              {footerLinks.resources.map((link) => (
                <li key={link.name}>
                  <a 
                    href={link.href} 
                    className="text-muted-foreground hover:text-orange-600 transition-colors"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Support links */}
          <div>
            <h4 className="font-semibold text-foreground mb-4">Support</h4>
            <ul className="space-y-3">
              {footerLinks.support.map((link) => (
                <li key={link.name}>
                  <a 
                    href={link.href} 
                    className="text-muted-foreground hover:text-orange-600 transition-colors"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <Separator className="my-8" />

        {/* Social media and bottom section */}
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="icon" className="hover:text-orange-600">
              <Facebook className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon" className="hover:text-orange-600">
              <Twitter className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon" className="hover:text-orange-600">
              <Instagram className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon" className="hover:text-orange-600">
              <Youtube className="h-5 w-5" />
            </Button>
          </div>

          <div className="flex items-center space-x-6 text-sm text-muted-foreground">
            <a href="/privacy" className="hover:text-orange-600 transition-colors">
              Privacy Policy
            </a>
            <a href="/terms" className="hover:text-orange-600 transition-colors">
              Terms of Service
            </a>
            <a href="/cookies" className="hover:text-orange-600 transition-colors">
              Cookie Policy
            </a>
          </div>
        </div>

        <Separator className="my-6" />

        {/* Copyright and blessing */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
            <span>© 2024 Sacred Community. Made with</span>
            <Heart className="h-4 w-4 text-red-500 fill-current" />
            <span>for the global Hindu community</span>
          </div>
          
          <div className="text-center">
            <p className="text-lg font-semibold text-orange-600 mb-1">
              "हरे कृष्ण हरे कृष्ण कृष्ण कृष्ण हरे हरे"
            </p>
            <p className="text-sm text-muted-foreground">
              May divine blessings be upon all
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}