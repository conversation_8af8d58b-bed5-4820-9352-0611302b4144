'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Star } from 'lucide-react';
import { api, DirectusTestimonial } from '@/lib/directus';

// Helper function to generate dummy avatar with first letter
const generateDummyAvatar = (firstName: string) => {
  const letter = firstName?.charAt(0)?.toUpperCase() || 'U';
  const colors = [
    'bg-gradient-to-br from-orange-400 to-red-500',
    'bg-gradient-to-br from-amber-400 to-orange-500',
    'bg-gradient-to-br from-red-400 to-pink-500',
    'bg-gradient-to-br from-yellow-400 to-amber-500',
    'bg-gradient-to-br from-pink-400 to-red-500',
    'bg-gradient-to-br from-purple-400 to-pink-500',
    'bg-gradient-to-br from-blue-400 to-purple-500',
    'bg-gradient-to-br from-green-400 to-blue-500',
  ];

  // Use first letter to determine color (consistent for same letter)
  const colorIndex = letter.charCodeAt(0) % colors.length;
  const gradientClass = colors[colorIndex];

  return (
    <div className={`w-12 h-12 rounded-full ${gradientClass} flex items-center justify-center text-white font-bold text-lg shadow-lg`}>
      {letter}
    </div>
  );
};

// Helper function to get avatar URL from Directus
const getAvatarUrl = (avatarId: string) => {
  const directusUrl = process.env.NEXT_PUBLIC_DIRECTUS_URL || 'http://localhost:8055';
  return `${directusUrl}/assets/${avatarId}`;
};

// Helper function to get user's full name
const getUserName = (testimonial: DirectusTestimonial) => {
  if (testimonial.user?.first_name || testimonial.user?.last_name) {
    return `${testimonial.user.first_name || ''} ${testimonial.user.last_name || ''}`.trim();
  }
  return testimonial.Name || 'Anonymous User';
};

// Helper function to get testimonial content
const getTestimonialContent = (testimonial: DirectusTestimonial) => {
  return testimonial.title || testimonial.Content || 'No testimonial content available';
};

export function TestimonialsSection() {
  const [testimonials, setTestimonials] = useState<DirectusTestimonial[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        setLoading(true);
        const response = await api.getTestimonials('published');
        if (response && response.data) {
          setTestimonials(response.data);
        }
      } catch (err) {
        console.error('Error fetching testimonials:', err);
        setError('Failed to load testimonials');
      } finally {
        setLoading(false);
      }
    };

    fetchTestimonials();
  }, []);

  if (loading) {
    return (
      <section className="py-24 bg-gradient-to-b from-background to-orange-50/30 dark:to-orange-950/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
              Blessed Testimonials
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Hear from our community members about how their spiritual journey has been enriched through our platform
            </p>
          </div>
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-24 bg-gradient-to-b from-background to-orange-50/30 dark:to-orange-950/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
              Blessed Testimonials
            </h2>
            <p className="text-xl text-red-500">
              {error}
            </p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-24 bg-gradient-to-b from-background to-orange-50/30 dark:to-orange-950/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
            Blessed Testimonials
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Hear from our community members about how their spiritual journey has been enriched through our platform
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial) => {
            const userName = getUserName(testimonial);
            const content = getTestimonialContent(testimonial);
            const firstName = testimonial.user?.first_name || testimonial.Name || 'User';
            const hasAvatar = testimonial.user?.avatar;

            return (
              <Card
                key={testimonial.id}
                className="bg-white/70 dark:bg-black/20 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
              >
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating || 5)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                    ))}
                  </div>

                  <p className="text-muted-foreground mb-6 leading-relaxed italic">
                    &ldquo;{content}&rdquo;
                  </p>

                  <div className="flex items-center space-x-4">
                    {hasAvatar ? (
                      <Avatar className="h-12 w-12">
                        <AvatarImage
                          src={getAvatarUrl(testimonial.user!.avatar!)}
                          alt={userName}
                        />
                        <AvatarFallback>
                          {firstName.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                    ) : (
                      generateDummyAvatar(firstName)
                    )}
                    <div>
                      <h4 className="font-semibold text-foreground">{userName}</h4>
                      <p className="text-sm text-muted-foreground">Community Member</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Sacred blessing */}
        <div className="text-center mt-16">
          <div className="inline-block p-6 bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900 dark:to-red-900 rounded-2xl">
            <p className="text-lg font-semibold text-orange-800 dark:text-orange-200 mb-2">
              &ldquo;सत्यं शिवं सुन्दरम्&rdquo;
            </p>
            <p className="text-sm text-orange-600 dark:text-orange-300">
              Truth, Goodness, Beauty
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}