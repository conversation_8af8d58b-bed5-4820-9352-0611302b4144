'use client';

import React from 'react';
import { useAuth } from './auth-provider';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { LoginDialog } from './login-dialog';
import { useState } from 'react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requireRole?: string;
}

export function ProtectedRoute({ 
  children, 
  fallback,
  requireRole 
}: ProtectedRouteProps) {
  const { user, isLoading, isAuthenticated } = useAuth();
  const [showLoginDialog, setShowLoginDialog] = useState(false);

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  // Show login prompt if not authenticated
  if (!isAuthenticated) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="container mx-auto p-6">
        <Card className="max-w-md mx-auto">
          <CardHeader className="text-center">
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>
              You need to be logged in to access this page
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <Button 
              onClick={() => setShowLoginDialog(true)}
              className="w-full"
            >
              Sign In
            </Button>
            <p className="text-sm text-muted-foreground">
              Don't have an account? You can create one in the sign-in dialog.
            </p>
          </CardContent>
        </Card>
        <LoginDialog open={showLoginDialog} onOpenChange={setShowLoginDialog} />
      </div>
    );
  }

  // Check role requirement if specified
  if (requireRole && user?.role?.name !== requireRole) {
    return (
      <div className="container mx-auto p-6">
        <Card className="max-w-md mx-auto">
          <CardHeader className="text-center">
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You don't have the required permissions to access this page
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-sm">
              Required role: <strong>{requireRole}</strong>
            </p>
            <p className="text-sm">
              Your role: <strong>{user?.role?.name || 'No role assigned'}</strong>
            </p>
            <Button 
              onClick={() => window.history.back()}
              variant="outline"
            >
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // User is authenticated and has required role
  return <>{children}</>;
}

// Higher-order component version
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  requireRole?: string
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <ProtectedRoute requireRole={requireRole}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}
