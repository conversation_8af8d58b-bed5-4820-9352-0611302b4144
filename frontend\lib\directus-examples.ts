// Example usage of Directus API functions
import { api } from './directus';

// Example: Get all published posts
export const getPublishedPosts = async () => {
  try {
    const posts = await api.getPosts(10, 0, 'published');
    console.log('Published posts:', posts);
    return posts;
  } catch (error) {
    console.error('Error fetching posts:', error);
    throw error;
  }
};

// Example: Get all categories
export const getAllCategories = async () => {
  try {
    const categories = await api.getCategories('published');
    console.log('Categories:', categories);
    return categories;
  } catch (error) {
    console.error('Error fetching categories:', error);
    throw error;
  }
};

// Example: Get upcoming events
export const getUpcomingEvents = async () => {
  try {
    const events = await api.getEvents(10, 0, 'published');
    console.log('Events:', events);
    return events;
  } catch (error) {
    console.error('Error fetching events:', error);
    throw error;
  }
};

// Example: Get banner sliders for homepage
export const getBannerSliders = async () => {
  try {
    const banners = await api.getBannerSliders('published');
    console.log('Banner sliders:', banners);
    return banners;
  } catch (error) {
    console.error('Error fetching banners:', error);
    throw error;
  }
};

// Example: Get website features
export const getWebsiteFeatures = async () => {
  try {
    const features = await api.getFeatures('published');
    console.log('Features:', features);
    return features;
  } catch (error) {
    console.error('Error fetching features:', error);
    throw error;
  }
};

// Example: Get testimonials
export const getTestimonials = async () => {
  try {
    const testimonials = await api.getTestimonials('published');
    console.log('Testimonials:', testimonials);
    return testimonials;
  } catch (error) {
    console.error('Error fetching testimonials:', error);
    throw error;
  }
};

// Example: Get social media links
export const getSocialMediaLinks = async () => {
  try {
    const socialMedia = await api.getSocialMedia('published');
    console.log('Social media links:', socialMedia);
    return socialMedia;
  } catch (error) {
    console.error('Error fetching social media:', error);
    throw error;
  }
};

// Example: Create a new post
export const createNewPost = async (title: string, description: string) => {
  try {
    const newPost = await api.createPost({
      Title: title,
      Description: description,
      status: 'draft' // Start as draft
    });
    console.log('Created post:', newPost);
    return newPost;
  } catch (error) {
    console.error('Error creating post:', error);
    throw error;
  }
};

// Example: Create a new category
export const createNewCategory = async (categoryName: string) => {
  try {
    const newCategory = await api.createCategory({
      Category: categoryName,
      status: 'published'
    });
    console.log('Created category:', newCategory);
    return newCategory;
  } catch (error) {
    console.error('Error creating category:', error);
    throw error;
  }
};

// Example: Create a new event
export const createNewEvent = async (
  title: string, 
  description: string, 
  startDate: string, 
  endDate: string
) => {
  try {
    const newEvent = await api.createEvent({
      Title: title,
      Description: description,
      Start_date: startDate,
      End_date: endDate,
      status: 'draft'
    });
    console.log('Created event:', newEvent);
    return newEvent;
  } catch (error) {
    console.error('Error creating event:', error);
    throw error;
  }
};
