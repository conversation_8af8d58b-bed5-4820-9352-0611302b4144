{"collections": [{"collection": "Community", "meta": {"collection": "Community", "icon": "folder", "note": null, "display_template": null, "hidden": false, "singleton": false, "translations": null, "archive_field": null, "archive_app_filter": true, "archive_value": null, "unarchive_value": null, "sort_field": null, "accountability": "all", "color": null, "item_duplication_fields": null, "sort": 1, "group": null, "collapse": "open", "preview_url": null, "versioning": false}, "schema": null}, {"collection": "Posts", "meta": {"collection": "Posts", "icon": null, "note": null, "display_template": null, "hidden": false, "singleton": false, "translations": null, "archive_field": "status", "archive_app_filter": true, "archive_value": "archived", "unarchive_value": "draft", "sort_field": null, "accountability": "all", "color": null, "item_duplication_fields": null, "sort": 1, "group": "Community", "collapse": "open", "preview_url": null, "versioning": false}, "schema": {}}, {"collection": "Categories", "meta": {"collection": "Categories", "icon": null, "note": null, "display_template": null, "hidden": false, "singleton": false, "translations": null, "archive_field": "status", "archive_app_filter": true, "archive_value": "archived", "unarchive_value": "draft", "sort_field": null, "accountability": "all", "color": null, "item_duplication_fields": null, "sort": 2, "group": "Community", "collapse": "open", "preview_url": null, "versioning": false}, "schema": {}}, {"collection": "Categories_Posts", "meta": {"collection": "Categories_Posts", "icon": "import_export", "note": null, "display_template": null, "hidden": true, "singleton": false, "translations": null, "archive_field": null, "archive_app_filter": true, "archive_value": null, "unarchive_value": null, "sort_field": null, "accountability": "all", "color": null, "item_duplication_fields": null, "sort": null, "group": null, "collapse": "open", "preview_url": null, "versioning": false}, "schema": {}}, {"collection": "Posts_Categories", "meta": {"collection": "Posts_Categories", "icon": "import_export", "note": null, "display_template": null, "hidden": true, "singleton": false, "translations": null, "archive_field": null, "archive_app_filter": true, "archive_value": null, "unarchive_value": null, "sort_field": null, "accountability": "all", "color": null, "item_duplication_fields": null, "sort": null, "group": null, "collapse": "open", "preview_url": null, "versioning": false}, "schema": {}}], "fields": [{"collection": "Posts", "field": "id", "type": "integer", "schema": {"name": "id", "table": "Posts", "data_type": "integer", "default_value": "nextval('\"Posts_id_seq\"'::regclass)", "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": true, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Posts", "field": "id", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "status", "type": "string", "schema": {"name": "status", "table": "Posts", "data_type": "character varying", "default_value": "draft", "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Posts", "field": "status", "special": null, "interface": "select-dropdown", "options": {"choices": [{"text": "Published", "value": "published", "color": "var(--theme--primary)"}, {"text": "Draft", "value": "draft", "color": "var(--theme--foreground)"}, {"text": "Archived", "value": "archived", "color": "var(--theme--warning)"}]}, "display": "labels", "display_options": {"showAsDot": true, "choices": [{"text": "Published", "value": "published", "color": "var(--theme--primary)", "foreground": "var(--theme--primary)", "background": "var(--theme--primary-background)"}, {"text": "Draft", "value": "draft", "color": "var(--theme--foreground)", "foreground": "var(--theme--foreground)", "background": "var(--theme--background-normal)"}, {"text": "Archived", "value": "archived", "color": "var(--theme--warning)", "foreground": "var(--theme--warning)", "background": "var(--theme--warning-background)"}]}, "readonly": false, "hidden": false, "sort": 2, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "Posts", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Posts", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "Posts", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Posts", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "Posts", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Posts", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "Posts", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Posts", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 6, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "Categories", "type": "alias", "schema": null, "meta": {"collection": "Posts", "field": "Categories", "special": ["m2m"], "interface": "list-m2m", "options": {"enableCreate": false, "enableLink": true, "template": "{{Categories_id.Category}}"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "Is_Public", "type": "boolean", "schema": {"name": "Is_Public", "table": "Posts", "data_type": "boolean", "default_value": false, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Posts", "field": "Is_Public", "special": ["cast-boolean"], "interface": "boolean", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "Title", "type": "string", "schema": {"name": "Title", "table": "Posts", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Posts", "field": "Title", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 9, "width": "full", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "Description", "type": "text", "schema": {"name": "Description", "table": "Posts", "data_type": "text", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Posts", "field": "Description", "special": null, "interface": "input-rich-text-html", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 10, "width": "full", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "Tags", "type": "json", "schema": {"name": "Tags", "table": "Posts", "data_type": "json", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Posts", "field": "Tags", "special": ["cast-json"], "interface": "tags", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 11, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "comments", "type": "alias", "schema": null, "meta": {"collection": "Posts", "field": "comments", "special": ["o2m"], "interface": "list-o2m", "options": {"template": "{{comment}}"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 12, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts", "field": "user", "type": "uuid", "schema": {"name": "user", "table": "Posts", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Posts", "field": "user", "special": ["m2o"], "interface": "select-dropdown-m2o", "options": {"template": "{{first_name}} {{last_name}}"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 13, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Categories", "field": "id", "type": "integer", "schema": {"name": "id", "table": "Categories", "data_type": "integer", "default_value": "nextval('\"Categories_id_seq\"'::regclass)", "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": true, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Categories", "field": "id", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": true, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Categories", "field": "status", "type": "string", "schema": {"name": "status", "table": "Categories", "data_type": "character varying", "default_value": "draft", "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Categories", "field": "status", "special": null, "interface": "select-dropdown", "options": {"choices": [{"text": "Published", "value": "published", "color": "var(--theme--primary)"}, {"text": "Draft", "value": "draft", "color": "var(--theme--foreground)"}, {"text": "Archived", "value": "archived", "color": "var(--theme--warning)"}]}, "display": "labels", "display_options": {"showAsDot": true, "choices": [{"text": "Published", "value": "published", "color": "var(--theme--primary)", "foreground": "var(--theme--primary)", "background": "var(--theme--primary-background)"}, {"text": "Draft", "value": "draft", "color": "var(--theme--foreground)", "foreground": "var(--theme--foreground)", "background": "var(--theme--background-normal)"}, {"text": "Archived", "value": "archived", "color": "var(--theme--warning)", "foreground": "var(--theme--warning)", "background": "var(--theme--warning-background)"}]}, "readonly": false, "hidden": false, "sort": 2, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Categories", "field": "user_created", "type": "uuid", "schema": {"name": "user_created", "table": "Categories", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Categories", "field": "user_created", "special": ["user-created"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 3, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Categories", "field": "date_created", "type": "timestamp", "schema": {"name": "date_created", "table": "Categories", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Categories", "field": "date_created", "special": ["date-created"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 4, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Categories", "field": "user_updated", "type": "uuid", "schema": {"name": "user_updated", "table": "Categories", "data_type": "uuid", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Categories", "field": "user_updated", "special": ["user-updated"], "interface": "select-dropdown-m2o", "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "display": "user", "display_options": null, "readonly": true, "hidden": true, "sort": 5, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Categories", "field": "date_updated", "type": "timestamp", "schema": {"name": "date_updated", "table": "Categories", "data_type": "timestamp with time zone", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Categories", "field": "date_updated", "special": ["date-updated"], "interface": "datetime", "options": null, "display": "datetime", "display_options": {"relative": true}, "readonly": true, "hidden": true, "sort": 6, "width": "half", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Categories", "field": "Category", "type": "string", "schema": {"name": "Category", "table": "Categories", "data_type": "character varying", "default_value": null, "generation_expression": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Categories", "field": "Category", "special": null, "interface": "input", "options": null, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 7, "width": "full", "translations": null, "note": null, "conditions": null, "required": true, "group": null, "validation": null, "validation_message": null}}, {"collection": "Categories", "field": "Posts", "type": "alias", "schema": null, "meta": {"collection": "Categories", "field": "Posts", "special": ["m2m"], "interface": "list-m2m", "options": {"enableCreate": false, "enableLink": true, "template": "{{Posts_id.Title}}"}, "display": null, "display_options": null, "readonly": false, "hidden": false, "sort": 8, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts_Categories", "field": "id", "type": "integer", "schema": {"name": "id", "table": "Posts_Categories", "data_type": "integer", "default_value": "nextval('\"Posts_Categories_id_seq\"'::regclass)", "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": true, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Posts_Categories", "field": "id", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts_Categories", "field": "Posts_id", "type": "integer", "schema": {"name": "Posts_id", "table": "Posts_Categories", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "Posts", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Posts_Categories", "field": "Posts_id", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 2, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Posts_Categories", "field": "Categories_id", "type": "integer", "schema": {"name": "Categories_id", "table": "Posts_Categories", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "Categories", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Posts_Categories", "field": "Categories_id", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 3, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Categories_Posts", "field": "id", "type": "integer", "schema": {"name": "id", "table": "Categories_Posts", "data_type": "integer", "default_value": "nextval('\"Categories_Posts_id_seq\"'::regclass)", "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "has_auto_increment": true, "foreign_key_schema": null, "foreign_key_table": null, "foreign_key_column": null, "comment": null}, "meta": {"collection": "Categories_Posts", "field": "id", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 1, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Categories_Posts", "field": "Categories_id", "type": "integer", "schema": {"name": "Categories_id", "table": "Categories_Posts", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "Categories", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Categories_Posts", "field": "Categories_id", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 2, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}, {"collection": "Categories_Posts", "field": "Posts_id", "type": "integer", "schema": {"name": "Posts_id", "table": "Categories_Posts", "data_type": "integer", "default_value": null, "generation_expression": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_generated": false, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "has_auto_increment": false, "foreign_key_schema": "public", "foreign_key_table": "Posts", "foreign_key_column": "id", "comment": null}, "meta": {"collection": "Categories_Posts", "field": "Posts_id", "special": null, "interface": null, "options": null, "display": null, "display_options": null, "readonly": false, "hidden": true, "sort": 3, "width": "full", "translations": null, "note": null, "conditions": null, "required": false, "group": null, "validation": null, "validation_message": null}}], "relations": [{"collection": "Posts", "field": "user", "related_collection": "directus_users", "schema": {"constraint_name": "posts_user_foreign", "table": "Posts", "column": "user", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "SET NULL"}, "meta": {"many_collection": "Posts", "many_field": "user", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Posts", "field": "user_updated", "related_collection": "directus_users", "schema": {"constraint_name": "posts_user_updated_foreign", "table": "Posts", "column": "user_updated", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION"}, "meta": {"many_collection": "Posts", "many_field": "user_updated", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Posts", "field": "user_created", "related_collection": "directus_users", "schema": {"constraint_name": "posts_user_created_foreign", "table": "Posts", "column": "user_created", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION"}, "meta": {"many_collection": "Posts", "many_field": "user_created", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Categories", "field": "user_created", "related_collection": "directus_users", "schema": {"constraint_name": "categories_user_created_foreign", "table": "Categories", "column": "user_created", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION"}, "meta": {"many_collection": "Categories", "many_field": "user_created", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Categories", "field": "user_updated", "related_collection": "directus_users", "schema": {"constraint_name": "categories_user_updated_foreign", "table": "Categories", "column": "user_updated", "foreign_key_schema": "public", "foreign_key_table": "directus_users", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "NO ACTION"}, "meta": {"many_collection": "Categories", "many_field": "user_updated", "one_collection": "directus_users", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": null, "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Posts_Categories", "field": "Categories_id", "related_collection": "Categories", "schema": {"constraint_name": "posts_categories_categories_id_foreign", "table": "Posts_Categories", "column": "Categories_id", "foreign_key_schema": "public", "foreign_key_table": "Categories", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "SET NULL"}, "meta": {"many_collection": "Posts_Categories", "many_field": "Categories_id", "one_collection": "Categories", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": "Posts_id", "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Posts_Categories", "field": "Posts_id", "related_collection": "Posts", "schema": {"constraint_name": "posts_categories_posts_id_foreign", "table": "Posts_Categories", "column": "Posts_id", "foreign_key_schema": "public", "foreign_key_table": "Posts", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "SET NULL"}, "meta": {"many_collection": "Posts_Categories", "many_field": "Posts_id", "one_collection": "Posts", "one_field": "Categories", "one_collection_field": null, "one_allowed_collections": null, "junction_field": "Categories_id", "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Categories_Posts", "field": "Posts_id", "related_collection": "Posts", "schema": {"constraint_name": "categories_posts_posts_id_foreign", "table": "Categories_Posts", "column": "Posts_id", "foreign_key_schema": "public", "foreign_key_table": "Posts", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "SET NULL"}, "meta": {"many_collection": "Categories_Posts", "many_field": "Posts_id", "one_collection": "Posts", "one_field": null, "one_collection_field": null, "one_allowed_collections": null, "junction_field": "Categories_id", "sort_field": null, "one_deselect_action": "nullify"}}, {"collection": "Categories_Posts", "field": "Categories_id", "related_collection": "Categories", "schema": {"constraint_name": "categories_posts_categories_id_foreign", "table": "Categories_Posts", "column": "Categories_id", "foreign_key_schema": "public", "foreign_key_table": "Categories", "foreign_key_column": "id", "on_update": "NO ACTION", "on_delete": "SET NULL"}, "meta": {"many_collection": "Categories_Posts", "many_field": "Categories_id", "one_collection": "Categories", "one_field": "Posts", "one_collection_field": null, "one_allowed_collections": null, "junction_field": "Posts_id", "sort_field": null, "one_deselect_action": "nullify"}}]}